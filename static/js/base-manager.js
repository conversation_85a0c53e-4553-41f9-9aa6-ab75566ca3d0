class BaseDataManager {
    constructor(options = {}) {
        this.refreshInterval = options.refreshInterval || 5000;
        this.updateTimer = null;
        this.isLoading = false;
        this.apiEndpoint = options.apiEndpoint || '/api/data/';
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.startAutoRefresh();
        this.loadData();
    }
    
    setupEventListeners() {
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadData();
            });
        }
        
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopAutoRefresh();
            } else {
                this.startAutoRefresh();
            }
        });
    }
    
    startAutoRefresh() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
        }
        
        this.updateTimer = setInterval(() => {
            this.loadData();
        }, this.refreshInterval);
    }
    
    stopAutoRefresh() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
        }
    }
    
    async loadData() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading();
        
        try {
            const data = await fetchWithErrorHandling(this.apiEndpoint);
            this.updateUI(data);
            this.updateLastUpdated();
            
        } catch (error) {
            console.error('Error loading data:', error);
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }
    
    updateUI(data) {
        console.warn('updateUI method should be implemented in child class');
    }
    
    updateLastUpdated() {
        const lastUpdatedEl = document.getElementById('last-updated');
        if (lastUpdatedEl) {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            lastUpdatedEl.textContent = `Last updated: ${timeString}`;
        }
    }
    
    showLoading() {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.classList.remove('hidden');
        }
    }
    
    hideLoading() {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.classList.add('hidden');
        }
    }
}