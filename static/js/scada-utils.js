// SCADA Utility Functions

// Format values with appropriate units
function formatValue(value, decimals = 1, unit = '') {
    if (value === null || value === undefined) {
        return '--' + (unit ? ' ' + unit : '');
    }
    return `${parseFloat(value).toFixed(decimals)}${unit ? ' ' + unit : ''}`;
}

// Initialize a chart with default options and empty data
function initChart(canvasId, type = 'line', options = {}) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) {
        console.error(`Canvas with ID ${canvasId} not found`);
        return null;
    }
    
    const ctx = canvas.getContext('2d');
    
    // Default options for SCADA charts
    const defaultOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
                labels: {
                    color: 'rgba(148, 163, 184, 0.8)',
                    font: { size: 12 },
                    boxWidth: 12
                }
            },
            tooltip: {
                backgroundColor: 'rgba(15, 23, 42, 0.8)',
                titleColor: '#fff',
                bodyColor: '#fff',
                borderColor: 'rgba(148, 163, 184, 0.2)',
                borderWidth: 1,
                padding: 10
            }
        },
        scales: {
            x: {
                grid: {
                    color: 'rgba(148, 163, 184, 0.1)',
                    borderColor: 'rgba(148, 163, 184, 0.2)'
                },
                ticks: {
                    color: 'rgba(148, 163, 184, 0.8)'
                }
            },
            y: {
                grid: {
                    color: 'rgba(148, 163, 184, 0.1)',
                    borderColor: 'rgba(148, 163, 184, 0.2)'
                },
                ticks: {
                    color: 'rgba(148, 163, 184, 0.8)'
                },
                min: 0,
                suggestedMax: 100
            }
        },
        elements: {
            line: {
                tension: 0.3
            },
            point: {
                radius: 2
            }
        }
    };
    
    // Merge default options with provided options
    const mergedOptions = {
        ...defaultOptions,
        ...options,
        scales: {
            ...defaultOptions.scales,
            ...(options.scales || {})
        },
        plugins: {
            ...defaultOptions.plugins,
            ...(options.plugins || {})
        }
    };
    
    // Create and return the chart
    return new Chart(ctx, {
        type: type,
        data: {
            labels: [],
            datasets: []
        },
        options: mergedOptions
    });
}

// Handle empty or missing chart data
function safeChartData(data, fallback) {
    if (!data || (Array.isArray(data) && data.length === 0)) {
        return fallback;
    }
    return data;
}

// Generate placeholder timestamps for empty charts
function generatePlaceholderTimestamps(count = 24, interval = 'hour') {
    const timestamps = [];
    const now = new Date();
    
    for (let i = count - 1; i >= 0; i--) {
        let time;
        if (interval === 'hour') {
            time = new Date(now - i * 3600000);
            timestamps.push(time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }));
        } else if (interval === 'day') {
            time = new Date(now - i * 86400000);
            timestamps.push(time.toLocaleDateString([], { month: 'short', day: 'numeric' }));
        } else if (interval === 'week') {
            time = new Date(now - i * 604800000);
            timestamps.push(time.toLocaleDateString([], { month: 'short', day: 'numeric' }));
        } else if (interval === 'month') {
            time = new Date(now.getFullYear(), now.getMonth() - i, 1);
            timestamps.push(time.toLocaleDateString([], { month: 'short', year: '2-digit' }));
        }
    }
    
    return timestamps;
}

// Generate random data for placeholder charts
function generatePlaceholderData(count = 24, min = 0, max = 100) {
    return Array(count).fill(0).map(() => Math.floor(Math.random() * (max - min + 1)) + min);
}

// Create a placeholder dataset for empty charts
function createPlaceholderDataset(label, color, count = 24, min = 0, max = 100) {
    return {
        label: label,
        data: generatePlaceholderData(count, min, max),
        borderColor: color,
        backgroundColor: color.replace(')', ', 0.1)').replace('rgb', 'rgba'),
        borderWidth: 2,
        pointRadius: 2,
        fill: true
    };
}

// Update chart with new data or use placeholders
function updateChartData(chart, newData, options = {}) {
    if (!chart) return;
    
    const {
        labels = null,
        datasets = null,
        preserveDatasets = false,
        animate = true
    } = options;
    
    // Update labels if provided
    if (labels) {
        chart.data.labels = labels;
    }
    
    // Update datasets if provided
    if (datasets) {
        if (preserveDatasets) {
            // Update existing datasets
            datasets.forEach((dataset, i) => {
                if (chart.data.datasets[i]) {
                    if (dataset.data) chart.data.datasets[i].data = dataset.data;
                    if (dataset.label) chart.data.datasets[i].label = dataset.label;
                    if (dataset.borderColor) chart.data.datasets[i].borderColor = dataset.borderColor;
                    if (dataset.backgroundColor) chart.data.datasets[i].backgroundColor = dataset.backgroundColor;
                }
            });
        } else {
            // Replace datasets
            chart.data.datasets = datasets;
        }
    }
    
    // Update chart with animation options
    chart.update(animate ? undefined : { duration: 0 });
}

// Create a responsive chart container
function createResponsiveChartContainer(parentElement, chartId) {
    const container = document.createElement('div');
    container.className = 'chart-container';
    container.style.position = 'relative';
    container.style.height = '300px';
    container.style.width = '100%';
    
    const canvas = document.createElement('canvas');
    canvas.id = chartId;
    
    container.appendChild(canvas);
    parentElement.appendChild(container);
    
    return canvas;
}

// Show notification
function showNotification(message, type = 'info', duration = 5000) {
    const notificationArea = document.getElementById('notification-area') || (() => {
        const el = document.createElement('div');
        el.id = 'notification-area';
        el.className = 'fixed bottom-4 right-4 z-50 flex flex-col items-end';
        document.body.appendChild(el);
        return el;
    })();
    const notification = document.createElement('div');
    notification.className = 'mb-3 p-4 rounded-lg shadow-lg transform transition-all duration-300 ease-in-out translate-x-0';
    if (type === 'success') {
        notification.classList.add('bg-green-500', 'bg-opacity-90', 'text-white');
        notification.innerHTML = `<i class="fas fa-check-circle mr-2"></i>${message}`;
    } else if (type === 'error') {
        notification.classList.add('bg-red-500', 'bg-opacity-90', 'text-white');
        notification.innerHTML = `<i class="fas fa-exclamation-circle mr-2"></i>${message}`;
    } else if (type === 'warning') {
        notification.classList.add('bg-yellow-500', 'bg-opacity-90', 'text-white');
        notification.innerHTML = `<i class="fas fa-exclamation-triangle mr-2"></i>${message}`;
    } else {
        notification.classList.add('bg-cyan-500', 'bg-opacity-90', 'text-white');
        notification.innerHTML = `<i class="fas fa-info-circle mr-2"></i>${message}`;
    }
    notificationArea.appendChild(notification);
    setTimeout(() => {
        notification.classList.add('opacity-0', 'translate-x-10');
        setTimeout(() => {
            if (notification.parentNode) {
                notificationArea.removeChild(notification);
            }
        }, 300);
    }, duration);
}

// Handle API errors
async function fetchWithErrorHandling(url, options = {}) {
    try {
        const response = await fetch(url, {
            ...options,
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json',
                ...(options.headers || {})
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return await response.json();
    } catch (error) {
        showNotification(error.message || 'API request failed', 'error');
        throw error;
    }
}

// Get CSRF token from cookies
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
