version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: thetha-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-thetha_db}
      POSTGRES_USER: ${POSTGRES_USER:-thetha_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-thetha_secure_password}
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-thetha_user} -d ${POSTGRES_DB:-thetha_db}"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # All-in-One Thetha Application (Backend + Redis + Celery + Celery-Beat)
  thetha:
    build:
      context: .
    container_name: thetha-app
    environment:
      # Database Configuration
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: ${POSTGRES_DB:-thetha_db}
      DATABASE_USER: ${POSTGRES_USER:-thetha_user}
      DATABASE_PASSWORD: ${POSTGRES_PASSWORD:-thetha_secure_password}

      # Django Configuration
      DJANGO_SETTINGS_MODULE: simple_settings
      DJANGO_SECRET_KEY: ${DJANGO_SECRET_KEY:-change-this-secret-key-in-production}
      DJANGO_DEBUG: ${DJANGO_DEBUG:-False}
      DJANGO_ALLOWED_HOSTS: ${DJANGO_ALLOWED_HOSTS:-localhost,127.0.0.1,thetha-app}

      # Redis Configuration (Internal)
      REDIS_HOST: localhost
      REDIS_PORT: 6379
      REDIS_URL: redis://localhost:6379/0
      CELERY_BROKER_URL: redis://localhost:6379/0
      CELERY_RESULT_BACKEND: redis://localhost:6379/0

      # Audio Processing Configuration
      WHISPER_MODEL: ${WHISPER_MODEL:-base}
      MAX_AUDIO_DURATION: ${MAX_AUDIO_DURATION:-300}
      MAX_SEGMENT_DURATION: ${MAX_SEGMENT_DURATION:-30}

      # Performance Optimizations
      OMP_NUM_THREADS: "2"
      MKL_NUM_THREADS: "2"
      TORCH_NUM_THREADS: "2"

      # Email Configuration
      EMAIL_BACKEND: ${EMAIL_BACKEND:-django.core.mail.backends.smtp.EmailBackend}
      EMAIL_HOST: ${EMAIL_HOST:-smtp.gmail.com}
      EMAIL_PORT: ${EMAIL_PORT:-587}
      EMAIL_USE_TLS: ${EMAIL_USE_TLS:-True}
      EMAIL_HOST_USER: ${EMAIL_HOST_USER:-}
      EMAIL_HOST_PASSWORD: ${EMAIL_HOST_PASSWORD:-}
      DEFAULT_FROM_EMAIL: ${DEFAULT_FROM_EMAIL:-}

      # Auto-create Django superuser (optional)
      DJANGO_SUPERUSER_USERNAME: ${DJANGO_SUPERUSER_USERNAME:-}
      DJANGO_SUPERUSER_EMAIL: ${DJANGO_SUPERUSER_EMAIL:-}
      DJANGO_SUPERUSER_PASSWORD: ${DJANGO_SUPERUSER_PASSWORD:-}

    ports:
      - "${BACKEND_PORT:-8000}:8000"

    volumes:
      - media_files:/app/mediafiles
      - static_files:/app/staticfiles
      - app_logs:/app/logs

    depends_on:
      postgres:
        condition: service_healthy

    restart: unless-stopped

    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'

  # Frontend
  frontend:
    build:
      context: ./Frontend/thetha
    container_name: thetha-frontend
    environment:
      # Frontend Configuration
      VITE_API_URL: ${DOCKER_VITE_API_URL:-}
      VITE_API_BASE_URL: ${DOCKER_VITE_API_BASE_URL:-/api}
      VITE_APP_NAME: ${VITE_APP_NAME:-Thetha}
      VITE_APP_VERSION: ${VITE_APP_VERSION:-1.0.0}
      VITE_APP_ENVIRONMENT: "docker"
      VITE_NODE_ENV: "production"
      VITE_ENABLE_DEBUG: "false"
      VITE_ENABLE_ANALYTICS: "${VITE_ENABLE_ANALYTICS:-false}"
      VITE_ENABLE_SENTRY: "${VITE_ENABLE_SENTRY:-false}"
      VITE_MAX_FILE_SIZE: ${VITE_MAX_FILE_SIZE:-104857600}
      VITE_ALLOWED_FILE_TYPES: ${VITE_ALLOWED_FILE_TYPES:-audio/mp3,audio/wav,audio/m4a,audio/aac,audio/ogg,audio/flac,audio/webm,audio/mpeg}
      VITE_WS_URL: ws://thetha:8000/ws
    ports:
      - "${FRONTEND_PORT:-3000}:80"
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'

volumes:
  postgres_data:
    driver: local
  media_files:
    driver: local
  static_files:
    driver: local
  app_logs:
    driver: local

networks:
  default:
    driver: bridge
