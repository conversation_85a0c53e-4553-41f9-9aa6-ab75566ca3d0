#!/bin/bash
set -e

echo "🚀 Starting Thetha All-in-One Container..."

# Wait for database to be ready if external
if [ -n "$DATABASE_HOST" ] && [ "$DATABASE_HOST" != "localhost" ]; then
    echo "⏳ Waiting for database at $DATABASE_HOST:$DATABASE_PORT..."
    until nc -z "$DATABASE_HOST" "$DATABASE_PORT"; do
        echo "Database not ready, waiting..."
        sleep 2
    done
    echo "✅ Database is ready!"
fi

# Run Django migrations as thetha user
echo "🔄 Running database migrations..."
su -c "python manage.py migrate --noinput" thetha

# Collect static files
echo "📦 Collecting static files..."
su -c "python manage.py collectstatic --noinput --clear" thetha

# Create superuser if environment variables are provided
if [ -n "$DJANGO_SUPERUSER_USERNAME" ] && [ -n "$DJANGO_SUPERUSER_EMAIL" ] && [ -n "$DJANGO_SUPERUSER_PASSWORD" ]; then
    echo "👤 Creating superuser..."
    su -c "python manage.py shell -c \"
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='$DJANGO_SUPERUSER_USERNAME').exists():
    User.objects.create_superuser('$DJANGO_SUPERUSER_USERNAME', '$DJANGO_SUPERUSER_EMAIL', '$DJANGO_SUPERUSER_PASSWORD')
    print('Superuser created successfully!')
else:
    print('Superuser already exists.')
\"" thetha
fi

# Create celerybeat schedule directory
mkdir -p /app/celerybeat-schedule
chown thetha:thetha /app/celerybeat-schedule

echo "🎯 Starting all services with supervisor..."

# Start supervisor which will manage all our services
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
