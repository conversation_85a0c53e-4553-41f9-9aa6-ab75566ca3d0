# Testing Guide for Thetha

This document explains how to test the Thetha backend, particularly the transcription functionality, without requiring JWT authentication.

## Authentication Bypass Options

There are several ways to test the backend without authentication:

### Option 1: Use the Test Endpoints

The backend includes special endpoints that don't require authentication:

```
POST http://localhost:8000/api/transcribe/test/upload/
GET http://localhost:8000/api/transcribe/test/{id}/status/
GET http://localhost:8000/api/transcribe/test/{id}/results/
```

These endpoints mirror the functionality of the authenticated endpoints but allow testing without a JWT token.

### Option 2: Use the Testing Script

A Python script is provided to easily test the transcription functionality:

```bash
./test_transcription.py your_audio_file.mp3
```

This script:
- Uploads the file to the test endpoint
- Polls for status updates
- Retrieves and displays the final transcription
- Saves the results to a JSON file

### Option 3: Disable Authentication Globally

You can temporarily disable authentication for all endpoints:

```bash
# Toggle authentication on/off
./toggle_auth.sh

# Or manually set the environment variable
DISABLE_AUTH=true docker-compose up -d
```

## Code Quality Testing

The project uses flake8 for code quality checks. A consolidated configuration is provided in the `.flake8` file at the project root.

### Running Flake8

```bash
# Install flake8 and plugins
pip install flake8 flake8-docstrings

# Run flake8
flake8
```

The configuration ignores common Django boilerplate files and patterns to reduce noise.

## Unit Testing

To run the Django unit tests:

```bash
# Run all tests
docker-compose exec backend python manage.py test

# Run specific tests
docker-compose exec backend python manage.py test transcriber.tests
```

## Integration Testing

For integration testing, you can use the test endpoints or disable authentication globally:

1. Start the services:
```bash
docker-compose up -d
```

2. Disable authentication:
```bash
./toggle_auth.sh
```

3. Use tools like Postman or curl to test the API endpoints.

## Reverting to Secure Mode

After testing, remember to re-enable authentication:

```bash
./toggle_auth.sh
```

Or manually set `DISABLE_AUTH=false` in your `.env` file and restart the backend container.
