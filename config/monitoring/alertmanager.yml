global:
  resolve_timeout: 5m

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'email-notifications'

receivers:
- name: 'email-notifications'
  email_configs:
  - to: '<EMAIL>'
    from: '<EMAIL>'
    smarthost: 'smtp.gmail.com:587'
    auth_username: '<EMAIL>'
    auth_password: 'your-app-specific-password'
    auth_identity: '<EMAIL>'
    auth_secret: 'your-app-specific-password'

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname']
