groups:
# Django application alerts
- name: django_alerts
  rules:
  - alert: HighErrorRate
    expr: rate(error_requests_total[5m]) > 0.1
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: High error rate detected
      description: "Error rate is {{ $value }} for the past 5 minutes"

  - alert: SlowResponseTime
    expr: rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m]) > 1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: Slow response times detected
      description: "Average response time is {{ $value }}s for the past 5 minutes"

  - alert: HighDBQueryTime
    expr: rate(db_query_duration_seconds_sum[5m]) / rate(db_query_duration_seconds_count[5m]) > 0.5
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: Slow database queries detected
      description: "Average query time is {{ $value }}s for the past 5 minutes"

  - alert: HighRequestRate
    expr: rate(http_requests_total[5m]) > 100
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: High request rate detected
      description: "Request rate is {{ $value }} requests/second"

# Redis alerts
- name: redis_alerts
  rules:
  - alert: RedisDown
    expr: redis_up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: Redis instance is down
      description: "Redis instance has been down for more than 1 minute"

  - alert: RedisHighMemoryUsage
    expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: Redis high memory usage
      description: "Redis memory usage is {{ $value }}%"

  - alert: RedisRejectedConnections
    expr: rate(redis_rejected_connections_total[1m]) > 0
    for: 1m
    labels:
      severity: warning
    annotations:
      summary: Redis is rejecting connections
      description: "Redis is rejecting connections at a rate of {{ $value }} per second"

# PostgreSQL alerts
- name: postgres_alerts
  rules:
  - alert: PostgreSQLDown
    expr: pg_up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: PostgreSQL instance is down
      description: "PostgreSQL instance has been down for more than 1 minute"

  - alert: PostgreSQLHighCPU
    expr: rate(pg_stat_activity_count{state="active"}[1m]) > 20
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: PostgreSQL high CPU usage
      description: "PostgreSQL has {{ $value }} active queries"

  - alert: PostgreSQLSlowQueries
    expr: pg_stat_activity_max_tx_duration{datname!~"template.*|postgres"} > 60
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: PostgreSQL slow queries
      description: "PostgreSQL has queries running for more than 60 seconds"

# Celery alerts
- name: celery_alerts
  rules:
  - alert: CeleryWorkerDown
    expr: celery_workers_online < 1
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: Celery worker is down
      description: "No Celery workers are online"

  - alert: CeleryHighTaskFailureRate
    expr: rate(celery_tasks_failed_total[5m]) / rate(celery_tasks_total[5m]) > 0.1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: High Celery task failure rate
      description: "Celery task failure rate is {{ $value | humanizePercentage }} for the past 5 minutes"

  - alert: CeleryTasksStuck
    expr: celery_tasks_pending > 100
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: Celery tasks are stuck in the queue
      description: "{{ $value }} Celery tasks are pending for more than 10 minutes"

# System alerts
- name: system_alerts
  rules:
  - alert: HighCPUUsage
    expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: High CPU usage
      description: "CPU usage is {{ $value | humanizePercentage }} for the past 5 minutes"

  - alert: HighMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: High memory usage
      description: "Memory usage is {{ $value | humanizePercentage }}"

  - alert: HighDiskUsage
    expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes * 100 > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: High disk usage
      description: "Disk usage is {{ $value | humanizePercentage }}"

# Container alerts
- name: container_alerts
  rules:
  - alert: ContainerHighCPUUsage
    expr: sum by(name) (rate(container_cpu_usage_seconds_total[5m])) > 0.8
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: Container high CPU usage
      description: "Container {{ $labels.name }} CPU usage is {{ $value | humanizePercentage }}"

  - alert: ContainerHighMemoryUsage
    expr: container_memory_usage_bytes / container_spec_memory_limit_bytes * 100 > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: Container high memory usage
      description: "Container {{ $labels.name }} memory usage is {{ $value | humanizePercentage }}"

  - alert: ContainerRestarting
    expr: rate(container_restart_count[5m]) > 0
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: Container is restarting frequently
      description: "Container {{ $labels.name }} is restarting {{ $value }} times per 5 minutes"

# Transcriber app specific alerts
- name: transcriber_alerts
  rules:
  - alert: HighTranscriptionFailureRate
    expr: rate(transcription_failures_total[5m]) / rate(transcription_attempts_total[5m]) > 0.1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: High transcription failure rate
      description: "Transcription failure rate is {{ $value | humanizePercentage }} for the past 5 minutes"

  - alert: LongTranscriptionQueue
    expr: transcription_queue_size > 20
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: Long transcription queue
      description: "{{ $value }} transcription tasks are waiting in the queue"

  - alert: SlowTranscriptionProcessing
    expr: rate(transcription_processing_time_seconds_sum[5m]) / rate(transcription_processing_time_seconds_count[5m]) > 60
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: Slow transcription processing
      description: "Average transcription processing time is {{ $value }}s for the past 5 minutes"
