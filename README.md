# ZimHealth-ID 🏥

**A comprehensive healthcare identity management system for Zimbabwe**

[![Django](https://img.shields.io/badge/Django-5.2.4-green.svg)](https://www.djangoproject.com/)
[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-3.0+-06B6D4.svg)](https://tailwindcss.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

ZimHealth-ID is a modern, secure, and comprehensive healthcare identity management platform designed specifically for Zimbabwe's healthcare system. It provides healthcare providers with reliable patient identification, medical record management, appointment scheduling, and prescription tracking capabilities.

## 🌟 Features

### 🔐 **Authentication & Security**
- **Secure User Registration & Login** with email verification
- **Password Reset** functionality with secure token-based system
- **User Profile Management** with avatar upload
- **Session Management** with automatic logout
- **CSRF Protection** and security best practices
- **Login Attempt Tracking** for enhanced security

### 👥 **Patient Management**
- **Unique ZimHealth ID Generation** (Format: ZH-YYYY-XXXXXX)
- **Comprehensive Patient Profiles** with personal and medical information
- **QR Code Generation** for quick patient identification
- **National ID Validation** for Zimbabwean citizens
- **Blood Type & Allergy Tracking**
- **Emergency Contact Management**
- **Patient Search & Filtering** capabilities

### 📋 **Medical Records**
- **Digital Medical Records** with comprehensive visit documentation
- **Vital Signs Tracking** (Temperature, Blood Pressure, Heart Rate, Weight, Height)
- **BMI Calculation** and health metrics
- **Diagnosis & Treatment Documentation**
- **Doctor & Facility Information** tracking
- **Medical History Timeline** for each patient
- **Search & Filter** by patient, diagnosis, doctor, or facility

### 💊 **Prescription Management**
- **Digital Prescription Creation** and management
- **Medication Tracking** with dosage and frequency
- **Prescription Status Management** (Active, Completed, Discontinued)
- **Refill Management** and quantity tracking
- **Expiration Alerts** for active prescriptions
- **Drug Interaction Warnings** (planned feature)

### 📅 **Appointment Scheduling**
- **Comprehensive Appointment Management**
- **Multiple Appointment Types** (Consultation, Follow-up, Check-up, Screening)
- **Doctor & Facility Assignment**
- **Appointment Status Tracking** (Scheduled, Completed, Cancelled, No-show)
- **Priority Levels** for urgent appointments
- **Conflict Prevention** with unique scheduling constraints
- **Appointment Reminders** (planned feature)

### 📊 **Analytics & Reporting**
- **Real-time Dashboard** with key performance indicators
- **Patient Registration Trends**
- **Appointment Statistics** and completion rates
- **Prescription Analytics** and medication trends
- **Healthcare Facility Performance** metrics
- **System Performance Monitoring**
- **Top Diagnoses** and health trend analysis

### 🎨 **Modern User Interface**
- **Responsive Design** optimized for mobile and desktop
- **Tailwind CSS** for modern, clean aesthetics
- **Medical-themed Color Palette** (Medical Blues & Health Greens)
- **Intuitive Navigation** with role-based access
- **Interactive Components** with smooth transitions
- **Accessibility Features** following WCAG guidelines

## 🏗️ **System Architecture**

### **Backend Framework**
- **Django 5.2.4** - Robust Python web framework
- **PostgreSQL/SQLite** - Reliable database management
- **Django ORM** - Object-relational mapping
- **Django Admin** - Administrative interface

### **Frontend Technologies**
- **Tailwind CSS 3.0+** - Utility-first CSS framework
- **Font Awesome 6.4** - Icon library
- **Inter Font Family** - Modern typography
- **Vanilla JavaScript** - Interactive functionality
- **Responsive Grid System** - Mobile-first design

### **Security Features**
- **CSRF Protection** - Cross-site request forgery prevention
- **SQL Injection Prevention** - Parameterized queries
- **XSS Protection** - Cross-site scripting prevention
- **Secure Password Hashing** - Django's built-in authentication
- **Session Security** - Secure session management

## 📱 **User Interface**

### **Dashboard Overview**
- **Statistics Cards** - Total patients, appointments, prescriptions
- **Recent Activity Feed** - Latest system activities
- **Quick Actions** - Fast access to common tasks
- **Upcoming Appointments** - Schedule overview
- **Performance Metrics** - System health indicators

### **Patient Management Interface**
- **Patient List View** - Searchable and filterable patient directory
- **Patient Detail View** - Comprehensive patient information
- **Patient Registration Form** - New patient onboarding
- **Medical History Timeline** - Chronological health records
- **QR Code Display** - Quick identification system

### **Medical Records Interface**
- **Record Creation Form** - Comprehensive visit documentation
- **Vital Signs Input** - Structured health metrics
- **Diagnosis & Treatment** - Medical assessment tools
- **Prescription Integration** - Seamless medication management
- **Print & Export** - Record sharing capabilities

### **Appointment Management**
- **Calendar View** - Visual appointment scheduling
- **List View** - Detailed appointment information
- **Status Management** - Appointment lifecycle tracking
- **Conflict Detection** - Scheduling optimization
- **Bulk Operations** - Efficient appointment management

## 🚀 **Getting Started**

### **Prerequisites**
- Python 3.8 or higher
- pip (Python package installer)
- Virtual environment (recommended)
- Git

### **Installation**

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/ZimHealth-ID.git
   cd ZimHealth-ID
   ```

2. **Create and activate virtual environment**
   ```bash
   python -m venv .zhid_venv

   # On Windows
   .zhid_venv\Scripts\activate

   # On macOS/Linux
   source .zhid_venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Navigate to project directory**
   ```bash
   cd zimhealth_id
   ```

5. **Run database migrations**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

6. **Create superuser account**
   ```bash
   python manage.py createsuperuser
   ```

7. **Create sample data (optional)**
   ```bash
   python manage.py create_sample_data --patients 5
   ```

8. **Start development server**
   ```bash
   python manage.py runserver
   ```

9. **Access the application**
   - **Main Application**: http://127.0.0.1:8000/
   - **Admin Interface**: http://127.0.0.1:8000/admin/
   - **API Dashboard**: http://127.0.0.1:8000/api/

### **Default Login Credentials**
- **Admin User**: Use the superuser credentials you created
- **Sample Users**: Created through the registration process

## 📁 **Project Structure**

```
ZimHealth-ID/
├── zimhealth_id/                 # Main Django project
│   ├── zimhealth_id/            # Project settings
│   │   ├── settings.py          # Django configuration
│   │   ├── urls.py              # URL routing
│   │   └── wsgi.py              # WSGI configuration
│   ├── zhid_auth/               # Authentication app
│   │   ├── models.py            # User profile models
│   │   ├── views.py             # Authentication views
│   │   ├── forms.py             # Authentication forms
│   │   └── urls.py              # Auth URL patterns
│   ├── api/                     # Core healthcare app
│   │   ├── models.py            # Patient, MedicalRecord, etc.
│   │   ├── views.py             # Healthcare views
│   │   ├── admin.py             # Admin configuration
│   │   └── urls.py              # API URL patterns
│   ├── templates/               # HTML templates
│   │   ├── base.html            # Base template
│   │   ├── zhid_auth/           # Authentication templates
│   │   ├── api/                 # Healthcare templates
│   │   └── dashboard/           # Dashboard templates
│   ├── static/                  # Static files
│   └── media/                   # User uploads
├── requirements.txt             # Python dependencies
├── README.md                    # Project documentation
└── .gitignore                   # Git ignore rules
```
