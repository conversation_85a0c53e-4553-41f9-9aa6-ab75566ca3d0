# Hydro System Guard - SCADA Web Application: System Design

## 1. Introduction

### 1.1. Overview
The "Hydro System Guard" is a full-stack web application designed to function as a Supervision, Control, and Data Acquisition (SCADA) system. It aims to monitor and manage a water pumping system, drawing specific requirements from the challenges faced by the Nyamandlovu pump station (City of Bulawayo). The primary objectives include enhancing pumping equipment lifespan, improving water quality monitoring, minimizing operational downtime, and providing robust data for informed decision-making and regulatory compliance.

---

## 2. System Architecture

### 2.1. Target Technology Stack
* **Backend:** Django (Python)
* **Frontend:** Django Templating Engine (Server-Side Rendered)
* **Styling:** Tailwind CSS (via CDN)
* **Database:** SQLite
* **Real-time Communication:** API endpoints with JavaScript `Workspace` for data refresh at defined intervals.
* **Hardware Interface:** ESP32 Microcontroller Unit (MCU) communicating via API endpoints.

---

## 3. Core Functionalities

### 3.1. User Authentication and Authorization
* **Roles:**
    * `Operator`: Views dashboards, acknowledges alerts, potential limited control actions (e.g., manual pump override).
    * `Administrator`: Full system access including configuration, user management, alert thresholds, report generation, and all operator functions.
* **Features:**
    * Secure login/logout.
    * Password management (reset, secure storage).

### 3.2. Real-Time Monitoring Dashboards
* **Main Dashboard:**
    * System overview: Key Performance Indicators (KPIs) like overall efficiency, total water pumped, active alerts.
    * Pump status visualization: Online, Offline, Tripped, Maintenance.
    * Critical sensor readings at a glance.
* **Pump-Specific Dashboards:**
    * **Operational Parameters:**
        * Flow Rate (L/s, m³/h): Line graphs, current value gauges.
        * Pressure (Inlet/Outlet): Similar visualizations.
        * Temperature (Motor/Water): Similar visualizations.
        * Vibration Levels: Similar visualizations (if sensor available).
        * Pump Status: On/Off, Running Hours (cumulative, since maintenance), Power Consumption (Current, Voltage, kWh).
    * **Water Quality Parameters (from ESP32):**
        * Water Hardness (mg/L as CaCO₃): Critical for Nyamandlovu.
        * pH levels.
        * Turbidity (NTU).
        * (Other relevant parameters as planned).
    * **Reservoir Levels:** Tank level indicators.
* **Data Visualization:**
    * Charts: Line (trends), bar (comparisons), gauges (current values).
    * Status Indicators: Color-coded (e.g., green/normal, yellow/warning, red/alarm).
    * Refresh Interval: Near real-time (e.g., every 5-10 seconds via JavaScript `Workspace`).
    * Historical Data Viewing: Selectable time ranges on charts (last hour, 24 hours, custom).

### 3.3. Alert System
* **Configurable Thresholds:** Admin-defined warning and critical thresholds for all monitored parameters.
* **Notification Types:**
    * On-screen alerts (persistent until acknowledged).
    * Visual cues on dashboards (flashing icons, color changes).
    * *Future Consideration:* Email/SMS notifications.
* **Alert Management:**
    * Alert Log: Timestamp, type, severity, parameter, value, acknowledged status, acknowledger, acknowledgment time, comments.
    * Operator Acknowledgment: Ability to acknowledge alerts with optional comments.
* **Critical Alerts:**
    * Dry Running Prevention: Specific alert; potential for automated shutdown logic.
    * Equipment Faults: Pump trips, motor overloads, ESP32 communication loss.

### 3.4. Data Acquisition (ESP32 Interface)
* **API Endpoints:**
    * Secure RESTful API endpoints for ESP32 to POST sensor data.
    * Data Format: JSON.
    * Authentication: `csrf_exempt` for simplified prototyping (Note: Implement proper token-based authentication for production).
* **Data Handling:**
    * Server-side timestamping of incoming data.
    * Storage in the database.
    * Basic input validation.

### 3.5. Control Capabilities (Phased Approach)
* **Initial Focus:** Monitoring.
* **Manual Override (Operator/Admin):**
    * Remote Start/Stop of pumps (with clear warnings and confirmation steps).
    * Lower priority for initial MVP; architecture should support future implementation.
* **Automated Control Logic (Future):**
    * E.g., Automatic pump shutdown on critical alerts (dry run, severe overload).

### 3.6. Reporting System
* **Compliance Reports:**
    * For local water authorities (e.g., City of Bulawayo).
    * Details: Water quality, abstraction rates, operational uptime.
    * Customizable date ranges.
* **Operational Reports:**
    * Pump performance (running hours, energy consumption, efficiency trends).
    * Maintenance logs (manual entry or linked to alerts).
    * Alert history.
* **Format:** Printable HTML, export to CSV/PDF.

### 3.7. Diagnostic Features
* **Trend Analysis:** Plotting historical data for selected parameters.
* **Fault Indication:** Clear display of sensor failures or ESP32 communication issues.
* **System Log:** Records of major system events, user actions, and errors.

---

## 4. User Interface (UI) and User Experience (UX)

* **Design:** Modern, fluid, clean, uncluttered, professional look using Tailwind CSS.
* **Navigation:** Intuitive and easy-to-understand menu structure.
* **Responsiveness:** Usable on desktop browsers and tablets.
* **Accessibility:** Adherence to basic web accessibility standards.
* **Clarity:** Clear and concise information presentation; tooltips for jargon.

---

## 5. Database Design

* **Models/Tables (Conceptual):**
    * `User` (Django's built-in, extended if needed)
    * `Pump` (ID, name, location, installation_date, specifications)
    * `Sensor` (linked to Pump, type, units, calibration_data)
    * `SensorReading` (timestamp, sensor_id, value)
    * `Alert` (alert_id, timestamp, pump_id, sensor_id, parameter, value, severity, status, acknowledged_by, acknowledged_at, comments)
    * `SystemSetting` (e.g., alert_thresholds_for_parameter_X)
    * `ReportTemplate` (if dynamic report structures are needed, otherwise reports are generated on the fly)
* **Relationships:** Standard relational database principles (One-to-Many, Many-to-Many where appropriate).

---

## 6. ESP32 Integration

### 6.1. ESP32 Communication Protocol

#### 6.1.1. Pump Sensor Data Payload
```json
{
  "esp32_id": "ESP32_PumpStation_01",
  "timestamp": "2024-05-26T14:30:00Z",
  "pump_id": "Pump_01",
  "readings": [
    {
      "parameter": "flow_rate_lps",
      "value": 55.2,
      "quality": "good"
    },
    {
      "parameter": "outlet_pressure_bar",
      "value": 2.1,
      "quality": "good"
    },
    {
      "parameter": "outlet_pressure_bar",
      "value": 5.5,
      "quality": "good"
    },
    {
      "parameter": "motor_temp_c",
      "value": 65.0,
      "quality": "good"
    },
    {
      "parameter": "water_temp_c",
      "value": 22.5,
      "quality": "good"
    },
    {
      "parameter": "vibration_mm_s",
      "value": 0.5,
      "quality": "good"
    },
    {
      "parameter": "power_kw",
      "value": 10.2,
      "quality": "good"
    },
    {
      "parameter": "current_a",
      "value": 15.3,
      "quality": "good"
    },
    {
      "parameter": "voltage_v",
      "value": 400.1,
      "quality": "good"
    }
  ],
  "status": "running",
  "running_hours": 1205.5
}
```

#### 6.1.2. Water Quality Data Payload
```json
{
  "esp32_id": "ESP32_PumpStation_01",
  "timestamp": "2024-05-26T14:35:00Z",
  "location": "Nyamandlovu Station",
  "hardness_mg_l": 350.0,
  "ph": 7.8,
  "turbidity_ntu": 1.2,
  "temperature_c": 22.5,
  "conductivity_ms_cm": 450.0,
  "quality_status": "acceptable"
}
```

#### 6.1.3. Reservoir Level Data Payload
```json
{
  "esp32_id": "ESP32_PumpStation_01",
  "timestamp": "2024-05-26T14:40:00Z",
  "reservoirs": [
    {
      "reservoir_id": "Magwegwe_Reservoir_East",
      "level_percentage": 65.0,
      "volume_liters": 1200000,
      "capacity_liters": 2000000
    },
    {
      "reservoir_id": "Magwegwe_Reservoir_West",
      "level_percentage": 58.0,
      "volume_liters": 870000,
      "capacity_liters": 1500000
    }
  ]
}
```

#### 6.1.4. Water Softening Status Payload
```json
{
  "esp32_id": "ESP32_PumpStation_01",
  "timestamp": "2024-05-26T14:45:00Z",
  "softening_system_id": "Softener_01",
  "operational_status": "running",
  "regeneration_cycle": false,
  "salt_level_percentage": 75.0,
  "resin_health_percentage": 92.0,
  "inlet_hardness_mg_l": 350.0,
  "outlet_hardness_mg_l": 85.0,
  "efficiency_percentage": 75.7,
  "flow_rate_lps": 12.5,
  "total_treated_volume_liters": 1250000,
  "next_regeneration_hours": 36
}
```
