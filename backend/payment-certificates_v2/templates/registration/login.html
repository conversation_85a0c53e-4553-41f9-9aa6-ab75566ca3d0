{% extends 'base.html' %}

{% block title %}Login - Payment Certificates Generator{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Sign in to your account
            </h2>
        </div>
        <form class="mt-8 space-y-6" method="post">
            {% csrf_token %}
            <div class="rounded-md shadow-sm -space-y-px">
                <div>
                    <label for="{{ form.username.id_for_label }}" class="sr-only">Username</label>
                    {{ form.username }}
                    {% if form.username.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.username.errors.0 }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.password.id_for_label }}" class="sr-only">Password</label>
                    {{ form.password }}
                    {% if form.password.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.password.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>

            {% if form.non_field_errors %}
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    {{ form.non_field_errors }}
                </div>
            {% endif %}

            <div>
                <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Sign in
                </button>
            </div>

            <div class="text-center">
                <p class="text-sm text-gray-600">
                    Don't have an account?
                    <a href="{% url 'register' %}" class="font-medium text-blue-600 hover:text-blue-500">
                        Register here
                    </a>
                </p>
            </div>
        </form>
    </div>
</div>
{% endblock %}
