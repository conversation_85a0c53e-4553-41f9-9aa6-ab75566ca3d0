{% extends 'base.html' %}

{% block title %}{{ project.name_of_contractor }} - Payment Certificates Generator{% endblock %}

{% block content %}
<div class="px-4 py-6 sm:px-0">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Project Details</h1>
        <div class="space-x-2">
            <a href="{% url 'project_list' %}" class="border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50">
                Back to Projects
            </a>
            <a href="{% url 'project_update' project.pk %}" class="border border-blue-600 text-blue-600 px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-50">
                Edit Project
            </a>
            <a href="{% url 'certificate_create' project_pk=project.pk %}" class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                Create Certificate
            </a>
        </div>
    </div>

    <div class="grid gap-6 md:grid-cols-2">
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="px-6 py-4 border-b">
                <h2 class="text-lg font-semibold text-gray-900">Project Information</h2>
            </div>
            <div class="p-6">
                <dl class="space-y-4">
                    <div class="flex justify-between">
                        <dt class="font-medium text-gray-600">Contractor:</dt>
                        <dd class="text-gray-900">{{ project.name_of_contractor }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="font-medium text-gray-600">Contract No:</dt>
                        <dd class="text-gray-900">{{ project.contract_no }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="font-medium text-gray-600">Vote No:</dt>
                        <dd class="text-gray-900">{{ project.vote_no }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="font-medium text-gray-600">Tender Sum:</dt>
                        <dd class="text-gray-900">${{ project.tender_sum|floatformat:2 }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="font-medium text-gray-600">Created:</dt>
                        <dd class="text-gray-900">{{ project.created_at|date:"M d, Y" }}</dd>
                    </div>
                </dl>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border">
            <div class="px-6 py-4 border-b flex justify-between items-center">
                <h2 class="text-lg font-semibold text-gray-900">Certificates</h2>
                <a href="{% url 'certificate_create' project_pk=project.pk %}" class="bg-green-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-green-700">
                    + New Certificate
                </a>
            </div>
            <div class="p-6">
                {% if certificates %}
                    <div class="space-y-3">
                        {% for cert in certificates %}
                            <div class="flex justify-between items-center p-3 border rounded-lg hover:bg-gray-50">
                                <div>
                                    <span class="font-medium">Certificate #{{ cert.id }}</span>
                                    <div class="text-sm text-gray-600">
                                        {{ cert.currency }} {{ cert.current_claim_excl_vat|floatformat:2 }} • {{ cert.created_at|date:"M d, Y" }}
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <a href="{% url 'certificate_detail' project_pk=project.pk pk=cert.pk %}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        View
                                    </a>
                                    <a href="{% url 'certificate_pdf' project_pk=project.pk pk=cert.pk %}" class="text-red-600 hover:text-red-800 text-sm font-medium">
                                        PDF
                                    </a>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No certificates</h3>
                        <p class="mt-1 text-sm text-gray-500">Get started by creating a new certificate.</p>
                        <div class="mt-6">
                            <a href="{% url 'certificate_create' project_pk=project.pk %}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                <svg class="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                                </svg>
                                Create Certificate
                            </a>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-8 bg-white rounded-lg shadow-sm border">
        <div class="px-6 py-4 border-b">
            <h2 class="text-lg font-semibold text-gray-900">Quick Actions</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="{% url 'certificate_create' project_pk=project.pk %}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-900">New Certificate</h3>
                        <p class="text-sm text-gray-500">Create a payment certificate</p>
                    </div>
                </a>
                
                <a href="{% url 'project_update' project.pk %}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-900">Edit Project</h3>
                        <p class="text-sm text-gray-500">Update project details</p>
                    </div>
                </a>
                
                <button onclick="confirmDelete()" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-red-50 text-left w-full">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-900">Delete Project</h3>
                        <p class="text-sm text-gray-500">Remove this project</p>
                    </div>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete() {
    if (confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
        window.location.href = "{% url 'project_delete' project.pk %}";
    }
}
</script>
{% endblock %}
