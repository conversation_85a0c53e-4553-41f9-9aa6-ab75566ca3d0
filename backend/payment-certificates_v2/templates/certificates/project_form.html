{% extends 'base.html' %}

{% block title %}{{ title }} - Payment Certificates Generator{% endblock %}

{% block content %}
<div class="px-4 py-6 sm:px-0">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="px-6 py-4 border-b">
                <h1 class="text-xl font-semibold text-gray-900">{{ title }}</h1>
            </div>
            <div class="p-6">
                {% if form.non_field_errors %}
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        {{ form.non_field_errors }}
                    </div>
                {% endif %}

                <form method="post" class="space-y-6">
                    {% csrf_token %}
                    
                    <div>
                        <label for="{{ form.name_of_contractor.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.name_of_contractor.label }}
                        </label>
                        {{ form.name_of_contractor }}
                        {% if form.name_of_contractor.errors %}
                            <p class="text-red-600 text-sm mt-1">{{ form.name_of_contractor.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.contract_no.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.contract_no.label }}
                        </label>
                        {{ form.contract_no }}
                        {% if form.contract_no.errors %}
                            <p class="text-red-600 text-sm mt-1">{{ form.contract_no.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.vote_no.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.vote_no.label }}
                        </label>
                        {{ form.vote_no }}
                        {% if form.vote_no.errors %}
                            <p class="text-red-600 text-sm mt-1">{{ form.vote_no.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.tender_sum.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.tender_sum.label }}
                        </label>
                        {{ form.tender_sum }}
                        {% if form.tender_sum.errors %}
                            <p class="text-red-600 text-sm mt-1">{{ form.tender_sum.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div class="flex justify-end space-x-3 pt-6 border-t">
                        <a href="{% url 'project_list' %}" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Cancel
                        </a>
                        <button type="submit" class="px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Create Project
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
