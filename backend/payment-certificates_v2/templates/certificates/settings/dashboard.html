{% extends 'base.html' %}

{% block title %}Settings - Payment Certificates Generator{% endblock %}

{% block content %}
<div class="px-4 py-6 sm:px-0">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Settings</h1>
    </div>

    <!-- Settings Navigation -->
    <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
        <!-- User Preferences -->
        <a href="{% url 'user_preferences' %}" class="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">User Preferences</h3>
                    <p class="text-sm text-gray-500">Customize your personal settings and preferences</p>
                </div>
            </div>
        </a>

        {% if is_admin %}
        <!-- System Settings -->
        <a href="{% url 'system_settings' %}" class="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">System Settings</h3>
                    <p class="text-sm text-gray-500">Configure system-wide settings and preferences</p>
                </div>
            </div>
        </a>

        <!-- Statistics -->
        <a href="{% url 'system_statistics' %}" class="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">Statistics</h3>
                    <p class="text-sm text-gray-500">View system statistics and analytics</p>
                </div>
            </div>
        </a>

        <!-- Audit Log -->
        <a href="{% url 'audit_log' %}" class="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">Audit Log</h3>
                    <p class="text-sm text-gray-500">View system activity and audit trail</p>
                </div>
            </div>
        </a>

        <!-- Data Export -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">Data Export</h3>
                    <p class="text-sm text-gray-500">Export system data and create backups</p>
                    <div class="mt-2 space-x-2">
                        <a href="{% url 'export_data' %}?type=projects" class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Projects</a>
                        <a href="{% url 'export_data' %}?type=certificates" class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Certificates</a>
                        <a href="{% url 'export_data' %}?type=audit_logs" class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">Audit Logs</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Backup -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">System Backup</h3>
                    <p class="text-sm text-gray-500">Create a complete system backup</p>
                    <form method="post" action="{% url 'system_backup' %}" class="mt-2">
                        {% csrf_token %}
                        <button type="submit" class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded hover:bg-gray-200">
                            Create Backup
                        </button>
                    </form>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    {% if is_admin and recent_logs %}
    <!-- Recent Activity -->
    <div class="bg-white rounded-lg shadow-sm border">
        <div class="px-6 py-4 border-b">
            <h2 class="text-lg font-semibold text-gray-900">Recent Activity</h2>
        </div>
        <div class="p-6">
            <div class="space-y-3">
                {% for log in recent_logs %}
                <div class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            {% if log.action == 'CREATE' %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    {{ log.action }}
                                </span>
                            {% elif log.action == 'UPDATE' %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ log.action }}
                                </span>
                            {% elif log.action == 'DELETE' %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    {{ log.action }}
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    {{ log.action }}
                                </span>
                            {% endif %}
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-gray-900">
                                {{ log.user.username|default:"System" }} {{ log.description|lower }}
                            </p>
                            <p class="text-xs text-gray-500">{{ log.timestamp|timesince }} ago</p>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            <div class="mt-4">
                <a href="{% url 'audit_log' %}" class="text-sm text-blue-600 hover:text-blue-800">View all activity →</a>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
