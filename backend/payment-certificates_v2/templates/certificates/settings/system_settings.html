{% extends 'base.html' %}

{% block title %}System Settings - Payment Certificates Generator{% endblock %}

{% block content %}
<div class="px-4 py-6 sm:px-0">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">System Settings</h1>
        <a href="{% url 'settings_dashboard' %}" class="border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50">
            Back to Settings
        </a>
    </div>

    <form method="post" enctype="multipart/form-data" class="space-y-8">
        {% csrf_token %}

        <!-- Company Information -->
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="px-6 py-4 border-b">
                <h2 class="text-lg font-semibold text-gray-900">Company Information</h2>
            </div>
            <div class="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="{{ form.company_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Company Name
                    </label>
                    {{ form.company_name }}
                    {% if form.company_name.errors %}
                        <p class="text-red-600 text-sm mt-1">{{ form.company_name.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.company_email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Company Email
                    </label>
                    {{ form.company_email }}
                    {% if form.company_email.errors %}
                        <p class="text-red-600 text-sm mt-1">{{ form.company_email.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.company_phone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Company Phone
                    </label>
                    {{ form.company_phone }}
                    {% if form.company_phone.errors %}
                        <p class="text-red-600 text-sm mt-1">{{ form.company_phone.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.company_logo.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Company Logo
                    </label>
                    {{ form.company_logo }}
                    {% if form.company_logo.errors %}
                        <p class="text-red-600 text-sm mt-1">{{ form.company_logo.errors.0 }}</p>
                    {% endif %}
                </div>

                <div class="md:col-span-2">
                    <label for="{{ form.company_address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Company Address
                    </label>
                    {{ form.company_address }}
                    {% if form.company_address.errors %}
                        <p class="text-red-600 text-sm mt-1">{{ form.company_address.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Certificate Settings -->
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="px-6 py-4 border-b">
                <h2 class="text-lg font-semibold text-gray-900">Certificate Settings</h2>
            </div>
            <div class="p-6 grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="{{ form.default_currency.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Default Currency
                    </label>
                    {{ form.default_currency }}
                    {% if form.default_currency.errors %}
                        <p class="text-red-600 text-sm mt-1">{{ form.default_currency.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.vat_rate.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        VAT Rate (%)
                    </label>
                    {{ form.vat_rate }}
                    {% if form.vat_rate.errors %}
                        <p class="text-red-600 text-sm mt-1">{{ form.vat_rate.errors.0 }}</p>
                    {% endif %}
                    <p class="text-xs text-gray-500 mt-1">{{ form.vat_rate.help_text }}</p>
                </div>

                <div>
                    <label for="{{ form.retention_rate.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Retention Rate (%)
                    </label>
                    {{ form.retention_rate }}
                    {% if form.retention_rate.errors %}
                        <p class="text-red-600 text-sm mt-1">{{ form.retention_rate.errors.0 }}</p>
                    {% endif %}
                    <p class="text-xs text-gray-500 mt-1">{{ form.retention_rate.help_text }}</p>
                </div>
            </div>
        </div>

        <!-- PDF Settings -->
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="px-6 py-4 border-b">
                <h2 class="text-lg font-semibold text-gray-900">PDF Settings</h2>
            </div>
            <div class="p-6 space-y-6">
                <div>
                    <label for="{{ form.pdf_header_text.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        PDF Header Text
                    </label>
                    {{ form.pdf_header_text }}
                    {% if form.pdf_header_text.errors %}
                        <p class="text-red-600 text-sm mt-1">{{ form.pdf_header_text.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.pdf_footer_text.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        PDF Footer Text
                    </label>
                    {{ form.pdf_footer_text }}
                    {% if form.pdf_footer_text.errors %}
                        <p class="text-red-600 text-sm mt-1">{{ form.pdf_footer_text.errors.0 }}</p>
                    {% endif %}
                </div>

                <div class="flex items-center">
                    {{ form.include_company_logo_in_pdf }}
                    <label for="{{ form.include_company_logo_in_pdf.id_for_label }}" class="ml-2 text-sm text-gray-700">
                        Include company logo in PDF certificates
                    </label>
                </div>
            </div>
        </div>

        <!-- Approval Settings -->
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="px-6 py-4 border-b">
                <h2 class="text-lg font-semibold text-gray-900">Approval Settings</h2>
            </div>
            <div class="p-6 space-y-6">
                <div class="flex items-center">
                    {{ form.require_dual_approval }}
                    <label for="{{ form.require_dual_approval.id_for_label }}" class="ml-2 text-sm text-gray-700">
                        Require dual approval for certificates
                    </label>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.approval_title_1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            First Approval Title
                        </label>
                        {{ form.approval_title_1 }}
                        {% if form.approval_title_1.errors %}
                            <p class="text-red-600 text-sm mt-1">{{ form.approval_title_1.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.approval_title_2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            Second Approval Title
                        </label>
                        {{ form.approval_title_2 }}
                        {% if form.approval_title_2.errors %}
                            <p class="text-red-600 text-sm mt-1">{{ form.approval_title_2.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Settings -->
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="px-6 py-4 border-b">
                <h2 class="text-lg font-semibold text-gray-900">Security Settings</h2>
            </div>
            <div class="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="{{ form.session_timeout_minutes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Session Timeout (minutes)
                    </label>
                    {{ form.session_timeout_minutes }}
                    {% if form.session_timeout_minutes.errors %}
                        <p class="text-red-600 text-sm mt-1">{{ form.session_timeout_minutes.errors.0 }}</p>
                    {% endif %}
                    <p class="text-xs text-gray-500 mt-1">{{ form.session_timeout_minutes.help_text }}</p>
                </div>

                <div>
                    <label for="{{ form.max_login_attempts.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Max Login Attempts
                    </label>
                    {{ form.max_login_attempts }}
                    {% if form.max_login_attempts.errors %}
                        <p class="text-red-600 text-sm mt-1">{{ form.max_login_attempts.errors.0 }}</p>
                    {% endif %}
                    <p class="text-xs text-gray-500 mt-1">{{ form.max_login_attempts.help_text }}</p>
                </div>
            </div>
        </div>

        <!-- System Preferences -->
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="px-6 py-4 border-b">
                <h2 class="text-lg font-semibold text-gray-900">System Preferences</h2>
            </div>
            <div class="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="{{ form.items_per_page.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Items Per Page
                    </label>
                    {{ form.items_per_page }}
                    {% if form.items_per_page.errors %}
                        <p class="text-red-600 text-sm mt-1">{{ form.items_per_page.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.default_date_format.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Default Date Format
                    </label>
                    {{ form.default_date_format }}
                    {% if form.default_date_format.errors %}
                        <p class="text-red-600 text-sm mt-1">{{ form.default_date_format.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Advanced Settings -->
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="px-6 py-4 border-b">
                <h2 class="text-lg font-semibold text-gray-900">Advanced Settings</h2>
            </div>
            <div class="p-6 space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="flex items-center">
                        {{ form.enable_audit_trail }}
                        <label for="{{ form.enable_audit_trail.id_for_label }}" class="ml-2 text-sm text-gray-700">
                            Enable audit trail
                        </label>
                    </div>

                    <div class="flex items-center">
                        {{ form.enable_data_export }}
                        <label for="{{ form.enable_data_export.id_for_label }}" class="ml-2 text-sm text-gray-700">
                            Enable data export
                        </label>
                    </div>

                    <div class="flex items-center">
                        {{ form.maintenance_mode }}
                        <label for="{{ form.maintenance_mode.id_for_label }}" class="ml-2 text-sm text-gray-700">
                            Maintenance mode
                        </label>
                    </div>
                </div>

                <div>
                    <label for="{{ form.maintenance_message.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Maintenance Message
                    </label>
                    {{ form.maintenance_message }}
                    {% if form.maintenance_message.errors %}
                        <p class="text-red-600 text-sm mt-1">{{ form.maintenance_message.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Save Button -->
        <div class="flex justify-end">
            <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Save Settings
            </button>
        </div>
    </form>
</div>
{% endblock %}
