from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator

class UserPreferences(models.Model):
    """Model to store user preferences for certificate generation"""

    CURRENCY_CHOICES = [
        ('USD', 'US Dollar'),
        ('ZIG', 'Zimbabwe Gold'),
        ('EUR', 'Euro'),
        ('GBP', 'British Pound'),
    ]

    PDF_FORMAT_CHOICES = [
        ('A4', 'A4'),
        ('Letter', 'Letter'),
    ]

    PDF_ORIENTATION_CHOICES = [
        ('portrait', 'Portrait'),
        ('landscape', 'Landscape'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='preferences')

    # General Preferences
    default_currency = models.CharField(
        max_length=3,
        choices=CURRENCY_CHOICES,
        default='USD'
    )
    company_name = models.CharField(max_length=200, blank=True)
    company_address = models.TextField(blank=True)

    # Certificate Settings
    vat_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=15.00,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    retention_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=10.00,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    auto_calculate = models.BooleanField(default=True)
    include_signature_lines = models.BooleanField(default=True)

    # PDF Settings
    pdf_format = models.CharField(
        max_length=10,
        choices=PDF_FORMAT_CHOICES,
        default='A4'
    )
    pdf_orientation = models.CharField(
        max_length=10,
        choices=PDF_ORIENTATION_CHOICES,
        default='portrait'
    )
    include_watermark = models.BooleanField(default=False)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "User Preference"
        verbose_name_plural = "User Preferences"

    def __str__(self):
        return f"{self.user.username}'s Preferences"

class Project(models.Model):
    """Model for construction projects"""

    name_of_contractor = models.CharField(max_length=200)
    contract_no = models.CharField(max_length=100, unique=True)
    vote_no = models.CharField(max_length=100)
    tender_sum = models.DecimalField(max_digits=15, decimal_places=2)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='projects')

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name_of_contractor} - {self.contract_no}"

class Certificate(models.Model):
    """Model for payment certificates"""

    CURRENCY_CHOICES = [
        ('USD', 'US Dollar'),
        ('ZIG', 'Zimbabwe Gold'),
        ('EUR', 'Euro'),
        ('GBP', 'British Pound'),
    ]

    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='certificates')
    currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES, default='USD')
    current_claim_excl_vat = models.DecimalField(max_digits=15, decimal_places=2)
    vat_value = models.DecimalField(max_digits=15, decimal_places=2)
    previous_payment_excl_vat = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='certificates')

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Certificate for {self.project.name_of_contractor} - {self.created_at.strftime('%Y-%m-%d')}"

    @property
    def value_of_workdone_incl_vat(self):
        return self.current_claim_excl_vat + self.vat_value

    @property
    def total_value_of_workdone_excl_vat(self):
        return self.current_claim_excl_vat

    @property
    def retention(self):
        # Get user's retention rate preference or default to 10%
        try:
            retention_rate = self.created_by.preferences.retention_rate
        except UserPreferences.DoesNotExist:
            retention_rate = 10.00
        return self.current_claim_excl_vat * (retention_rate / 100)

    @property
    def total_amount_payable(self):
        return self.current_claim_excl_vat + self.vat_value - self.retention

class Calculations(models.Model):
    """Model to store calculated values for certificates"""

    certificate = models.OneToOneField(Certificate, on_delete=models.CASCADE, related_name='calculations')
    value_of_workdone_incl_vat = models.DecimalField(max_digits=15, decimal_places=2)
    total_value_of_workdone_excl_vat = models.DecimalField(max_digits=15, decimal_places=2)
    retention = models.DecimalField(max_digits=15, decimal_places=2)
    total_amount_payable = models.DecimalField(max_digits=15, decimal_places=2)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Calculations for {self.certificate}"
