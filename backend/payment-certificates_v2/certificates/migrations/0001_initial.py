# Generated by Django 4.2.23 on 2025-06-22 21:27

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserPreferences',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('theme', models.CharField(choices=[('light', 'Light'), ('dark', 'Dark'), ('auto', 'Auto')], default='light', max_length=10)),
                ('language', models.CharField(choices=[('en', 'English'), ('es', 'Spanish'), ('fr', 'French')], default='en', max_length=10)),
                ('timezone', models.CharField(choices=[('UTC', 'UTC'), ('America/New_York', 'Eastern Time'), ('America/Chicago', 'Central Time'), ('America/Denver', 'Mountain Time'), ('America/Los_Angeles', 'Pacific Time'), ('Europe/London', 'London'), ('Europe/Paris', 'Paris'), ('Asia/Tokyo', 'Tokyo')], default='UTC', max_length=50)),
                ('email_notifications', models.BooleanField(default=True)),
                ('browser_notifications', models.BooleanField(default=False)),
                ('weekly_summary', models.BooleanField(default=True)),
                ('show_recent_projects', models.BooleanField(default=True)),
                ('show_statistics', models.BooleanField(default=True)),
                ('projects_per_page', models.IntegerField(default=12, validators=[django.core.validators.MinValueValidator(5), django.core.validators.MaxValueValidator(50)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='preferences', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_name', models.CharField(default='Your Company Name', max_length=200)),
                ('company_address', models.TextField(default='Company Address')),
                ('company_phone', models.CharField(blank=True, max_length=50)),
                ('company_email', models.EmailField(blank=True, max_length=254)),
                ('company_logo', models.ImageField(blank=True, null=True, upload_to='company_logos/')),
                ('default_currency', models.CharField(choices=[('USD', 'USD'), ('ZIG', 'ZIG'), ('EUR', 'EUR'), ('GBP', 'GBP')], default='USD', max_length=3)),
                ('vat_rate', models.DecimalField(decimal_places=2, default=Decimal('15.00'), max_digits=5, validators=[django.core.validators.MinValueValidator(Decimal('0.00')), django.core.validators.MaxValueValidator(Decimal('100.00'))])),
                ('retention_rate', models.DecimalField(decimal_places=2, default=Decimal('10.00'), max_digits=5, validators=[django.core.validators.MinValueValidator(Decimal('0.00')), django.core.validators.MaxValueValidator(Decimal('100.00'))])),
                ('pdf_header_text', models.CharField(default='PAYMENT CERTIFICATE', max_length=200)),
                ('pdf_footer_text', models.TextField(default='This certificate is issued without prejudice to the rights and obligations of the parties under the Contract.')),
                ('include_company_logo_in_pdf', models.BooleanField(default=True)),
                ('require_dual_approval', models.BooleanField(default=True)),
                ('approval_title_1', models.CharField(default='Project Manager', max_length=100)),
                ('approval_title_2', models.CharField(default='Finance Officer', max_length=100)),
                ('email_notifications_enabled', models.BooleanField(default=True)),
                ('notify_on_certificate_creation', models.BooleanField(default=True)),
                ('notify_on_project_creation', models.BooleanField(default=False)),
                ('session_timeout_minutes', models.IntegerField(default=60, validators=[django.core.validators.MinValueValidator(5), django.core.validators.MaxValueValidator(480)])),
                ('max_login_attempts', models.IntegerField(default=5, validators=[django.core.validators.MinValueValidator(3), django.core.validators.MaxValueValidator(10)])),
                ('items_per_page', models.IntegerField(default=12, validators=[django.core.validators.MinValueValidator(5), django.core.validators.MaxValueValidator(50)])),
                ('default_date_format', models.CharField(choices=[('%B %d, %Y', 'January 01, 2024'), ('%d/%m/%Y', '01/01/2024'), ('%m/%d/%Y', '01/01/2024'), ('%Y-%m-%d', '2024-01-01')], default='%B %d, %Y', max_length=20)),
                ('auto_backup_enabled', models.BooleanField(default=False)),
                ('backup_frequency_days', models.IntegerField(default=7, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(30)])),
                ('enable_audit_trail', models.BooleanField(default=True)),
                ('enable_data_export', models.BooleanField(default=True)),
                ('maintenance_mode', models.BooleanField(default=False)),
                ('maintenance_message', models.TextField(blank=True, default='System is under maintenance. Please try again later.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'System Settings',
                'verbose_name_plural': 'System Settings',
            },
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name_of_contractor', models.CharField(max_length=200)),
                ('contract_no', models.CharField(max_length=100, unique=True)),
                ('vote_no', models.CharField(max_length=100)),
                ('tender_sum', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='projects', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Certificate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('currency', models.CharField(choices=[('USD', 'USD'), ('ZIG', 'ZIG'), ('EUR', 'EUR'), ('GBP', 'GBP')], default='USD', max_length=3)),
                ('current_claim_excl_vat', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('vat_value', models.DecimalField(decimal_places=2, editable=False, max_digits=12)),
                ('previous_payment_excl_vat', models.DecimalField(decimal_places=2, default=0, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='certificates', to='certificates.project')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Calculations',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('value_of_workdone_incl_vat', models.DecimalField(decimal_places=2, max_digits=12)),
                ('total_value_of_workdone_excl_vat', models.DecimalField(decimal_places=2, max_digits=12)),
                ('retention', models.DecimalField(decimal_places=2, max_digits=12)),
                ('total_amount_payable', models.DecimalField(decimal_places=2, max_digits=12)),
                ('certificate', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='calculations', to='certificates.certificate')),
            ],
            options={
                'verbose_name_plural': 'Calculations',
            },
        ),
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('CREATE', 'Create'), ('UPDATE', 'Update'), ('DELETE', 'Delete'), ('VIEW', 'View'), ('EXPORT', 'Export'), ('LOGIN', 'Login'), ('LOGOUT', 'Logout')], max_length=10)),
                ('model_name', models.CharField(max_length=50)),
                ('object_id', models.CharField(blank=True, max_length=50)),
                ('description', models.TextField()),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.AddIndex(
            model_name='project',
            index=models.Index(fields=['owner', '-created_at'], name='certificate_owner_i_bec95d_idx'),
        ),
        migrations.AddIndex(
            model_name='project',
            index=models.Index(fields=['contract_no'], name='certificate_contrac_267c45_idx'),
        ),
        migrations.AddIndex(
            model_name='certificate',
            index=models.Index(fields=['project', '-created_at'], name='certificate_project_285666_idx'),
        ),
    ]
