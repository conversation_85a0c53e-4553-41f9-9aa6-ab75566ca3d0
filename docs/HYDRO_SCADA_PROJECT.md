# Hydro SCADA Project Documentation

## Overview

The `hydro_scada` project is the main Django project that houses the Hydro System Guard SCADA application. It provides the configuration, settings, and URL routing for the entire system, integrating the `monitoring` app and any future apps that may be added.

## Project Structure

```
hydro_scada/
├── hydro_scada/
│   ├── __init__.py
│   ├── asgi.py
│   ├── settings.py
│   ├── urls.py
│   └── wsgi.py
├── monitoring/
│   ├── __init__.py
│   ├── admin.py
│   ├── apps.py
│   ├── management/
│   │   └── commands/
│   │       ├── reset_db.py
│   │       └── setup_base_infrastructure.py
│   ├── migrations/
│   ├── models.py
│   ├── serializers.py
│   ├── static/
│   ├── templates/
│   ├── tests/
│   ├── urls.py
│   └── views.py
├── docs/
│   ├── PROJECT_DOCUMENTATION.md
│   ├── MONITORING_APP.md
│   └── HYDRO_SCADA_PROJECT.md
├── static/
├── media/
├── templates/
├── manage.py
└── README.md
```

## Configuration Files

### settings.py

The main settings file for the Django project, containing:

- **Database Configuration**: SQLite for development
- **Installed Apps**: 
  - Django built-in apps
  - `monitoring` app
  - Third-party apps (if any)
- **Middleware Configuration**
- **Template Configuration**
- **Static and Media Files Configuration**
- **Authentication Settings**
- **Internationalization Settings**
- **Logging Configuration**

### urls.py

The main URL configuration file that includes:

- Admin site URLs
- Monitoring app URLs
- Static and media file serving for development

```python
urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('monitoring.urls')),
]

# Serve static files during development
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
```

### wsgi.py and asgi.py

Web server gateway interface and asynchronous server gateway interface configurations for deployment.

## Dependencies

The project relies on the following key dependencies:

- **Django**: Web framework (4.2.x)
- **Django REST Framework**: API framework
- **Matplotlib**: For generating charts and graphs
- **WeasyPrint**: For PDF report generation
- **Pillow**: For image processing
- **NumPy**: For numerical operations

## Development Environment Setup

1. **Clone the repository**:
   ```
   git clone <repository-url>
   cd hydro_scada
   ```

2. **Create a virtual environment**:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```
   pip install -r requirements.txt
   ```

4. **Run migrations**:
   ```
   python manage.py migrate
   ```

5. **Set up base infrastructure**:
   ```
   python manage.py setup_base_infrastructure
   ```

6. **Create a superuser**:
   ```
   python manage.py createsuperuser
   ```

7. **Run the development server**:
   ```
   python manage.py runserver
   ```

## Deployment

The project is configured for deployment using:

- **WSGI**: For traditional web servers (Apache, Nginx+uWSGI)
- **ASGI**: For asynchronous servers (Daphne, Uvicorn)

Deployment steps:

1. Set `DEBUG = False` in settings.py
2. Configure a production database (PostgreSQL recommended)
3. Set up static file serving
4. Configure a web server (Nginx, Apache)
5. Set up HTTPS with SSL certificates
6. Configure environment variables for sensitive settings

## Admin Interface

The Django admin interface is available at `/admin/` and provides:

- User management
- Infrastructure management
- Pump and motor management
- Reservoir management
- Water quality data management
- Alert management
- System log viewing

## Security Considerations

- **Authentication**: Django's built-in authentication system
- **Authorization**: Permission-based access control
- **CSRF Protection**: Enabled for all forms
- **XSS Protection**: Template escaping
- **SQL Injection Protection**: Django's ORM
- **Secure Cookies**: Session cookies are secure
- **HTTPS**: Recommended for production

## Future Development

Planned enhancements for the project:

1. **Real-time Updates**:
   - WebSocket integration for live data updates

2. **Advanced Authentication**:
   - Two-factor authentication
   - OAuth integration

3. **Mobile Responsiveness**:
   - Enhance mobile UI for field operators

4. **Expanded API**:
   - Additional endpoints for external integrations

5. **Predictive Analytics**:
   - Machine learning integration for predictive maintenance

## Troubleshooting

Common issues and solutions:

1. **Database Migrations**:
   - If migrations fail, try `python manage.py migrate --fake-initial`

2. **Static Files Not Loading**:
   - Run `python manage.py collectstatic`
   - Check STATIC_URL and STATIC_ROOT settings

3. **ESP32 Connection Issues**:
   - Verify network connectivity
   - Check API endpoint URLs
   - Validate JSON payload format

4. **Performance Issues**:
   - Enable database query caching
   - Optimize database indexes
   - Consider pagination for large datasets

## References

- [Django Documentation](https://docs.djangoproject.com/)
- [Django REST Framework Documentation](https://www.django-rest-framework.org/)
- [ESP32 Documentation](https://docs.espressif.com/projects/esp-idf/en/latest/)