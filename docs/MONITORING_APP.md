# Monitoring App Documentation

## Overview

The `monitoring` app is the core component of the Hydro System Guard SCADA system, responsible for real-time monitoring and management of water infrastructure. It handles data collection from ESP32 microcontrollers, data visualization, alert management, and reporting.

## Models

### Infrastructure
Represents physical infrastructure components in the water system.

- **Fields**:
  - `name`: Name of the infrastructure
  - `type`: Type of infrastructure (PUMP_HOUSE, RESERVOIR, TREATMENT_PLANT, etc.)
  - `location`: Physical location
  - `status`: Current operational status (ACTIVE, INACTIVE, MAINTENANCE, etc.)
  - `description`: Detailed description
  - `installation_date`: Date of installation
  - `last_maintenance`: Date of last maintenance
  - `next_maintenance`: Scheduled date for next maintenance

### Pump
Represents water pumps in the system.

- **Fields**:
  - `infrastructure`: ForeignKey to Infrastructure
  - `name`: Pump name
  - `model`: Pump model number
  - `serial_number`: Unique serial number
  - `status`: Current status (ON, OFF, MAINTENANCE, etc.)
  - `flow_rate`: Current flow rate (L/s)
  - `max_flow_rate`: Maximum flow rate capacity
  - `outlet_pressure`: Current outlet pressure (bar)
  - `motor_temp`: Current motor temperature (°C)
  - `running_hours`: Cumulative running hours

### Motor
Represents motors (particularly stirring motors) in the system.

- **Fields**:
  - `infrastructure`: ForeignKey to Infrastructure
  - `name`: Motor name
  - `motor_type`: Type of motor (STIRRER, CONVEYOR, etc.)
  - `model`: Motor model number
  - `serial_number`: Unique serial number
  - `status`: Current status (ON, OFF, MAINTENANCE, etc.)
  - `rpm`: Current rotations per minute
  - `max_rpm`: Maximum RPM capacity
  - `temperature`: Current temperature (°C)
  - `power`: Current power consumption (kW)
  - `running_hours`: Cumulative running hours

### MotorReading
Time-series data for motor readings.

- **Fields**:
  - `motor`: ForeignKey to Motor
  - `timestamp`: Time of reading
  - `rpm`: RPM at time of reading
  - `temperature`: Temperature at time of reading
  - `power`: Power consumption at time of reading

### Reservoir
Represents water storage reservoirs.

- **Fields**:
  - `infrastructure`: ForeignKey to Infrastructure
  - `name`: Reservoir name
  - `capacity`: Total capacity (liters)
  - `current_level`: Current water level (liters)
  - `status`: Current status (NORMAL, LOW, HIGH, etc.)

### WaterQuality
Represents water quality measurements.

- **Fields**:
  - `infrastructure`: ForeignKey to Infrastructure
  - `hardness_mg_l`: Water hardness (mg/L)
  - `ph`: pH level
  - `turbidity_ntu`: Turbidity (NTU)
  - `temperature_c`: Water temperature (°C)
  - `chlorine_mg_l`: Chlorine level (mg/L)
  - `conductivity_ms_cm`: Conductivity (µS/cm)
  - `last_updated`: Timestamp of last update

### Alert
Represents system alerts and notifications.

- **Fields**:
  - `timestamp`: Time alert was generated
  - `category`: Alert category (PUMP, QUALITY, RESERVOIR, etc.)
  - `severity`: Alert severity (INFO, WARNING, CRITICAL, etc.)
  - `message`: Alert message
  - `source`: Source of the alert
  - `acknowledged`: Whether alert has been acknowledged
  - `acknowledged_by`: User who acknowledged the alert
  - `acknowledged_at`: Time of acknowledgment
  - `resolved`: Whether alert has been resolved
  - `resolved_at`: Time of resolution

### SystemLog
Represents system activity logs.

- **Fields**:
  - `timestamp`: Time of log entry
  - `level`: Log level (INFO, WARNING, ERROR, etc.)
  - `source`: Source of the log entry
  - `message`: Log message
  - `user`: User associated with the log entry (if applicable)

### ProcessStage
Represents stages in the water treatment process.

- **Fields**:
  - `infrastructure`: ForeignKey to Infrastructure
  - `name`: Stage name
  - `order`: Order in the process sequence
  - `status`: Current status
  - `description`: Description of the process stage

### UserProfile
Extends the Django User model with additional fields.

- **Fields**:
  - `user`: OneToOneField to Django User
  - `role`: User role (OPERATOR, ADMINISTRATOR, etc.)
  - `full_name`: User's full name
  - `is_active`: Whether user is active

## Views

### Dashboard Views
- `DashboardView`: Main dashboard displaying system overview
- `PumpDetailView`: Detailed view for individual pumps
- `MotorDetailView`: Detailed view for motors
- `ReservoirDetailView`: Detailed view for reservoirs
- `WaterQualityView`: Water quality monitoring view
- `AlertsView`: Alert management view
- `ReportsView`: System reports view

### API Views
- `DashboardDataView`: Provides data for dashboard widgets
- `SystemStatusView`: Provides overall system status
- `WaterQualityCurrentView`: Current water quality data
- `WaterQualityHistoricalView`: Historical water quality data
- `GenerateReportView`: Generates system reports
- `ProcessSensorDataView`: Processes incoming sensor data from ESP32

## URLs

- `/`: Dashboard home
- `/pumps/<int:pk>/`: Pump detail view
- `/motor/<int:pk>/`: Motor detail view
- `/reservoirs/<int:pk>/`: Reservoir detail view
- `/water-quality/`: Water quality view
- `/alerts/`: Alerts view
- `/reports/`: Reports view
- `/api/dashboard-data/`: Dashboard data API
- `/api/system-status/`: System status API
- `/api/water-quality/current/`: Current water quality API
- `/api/water-quality/historical/`: Historical water quality API
- `/api/reports/generate/`: Report generation API
- `/api/process-sensor-data/`: Sensor data processing API

## Management Commands

### `setup_base_infrastructure`
Sets up the base infrastructure for the water monitoring system.

**Usage**:
```
python manage.py setup_base_infrastructure
```

**Creates**:
- Main pump house infrastructure
- 4 pumps with zero data
- 1 stirring motor
- 1 reservoir
- Water quality monitoring setup

### `reset_db`
Resets the database for real-time microcontroller data streaming.

**Usage**:
```
python manage.py reset_db [--no-input]
```

**Options**:
- `--no-input`: Skip confirmation prompt

## Templates

### Base Templates
- `base.html`: Base template with common structure
- `sidebar.html`: Navigation sidebar
- `header.html`: Page header with user info and controls

### Dashboard Templates
- `dashboard.html`: Main dashboard template
- `pump_detail.html`: Pump detail template
- `motor_detail.html`: Motor detail template
- `reservoir_detail.html`: Reservoir detail template
- `water_quality.html`: Water quality template
- `alerts.html`: Alerts management template
- `reports.html`: Reports template

## Static Files

### CSS
- Tailwind CSS (via CDN)
- Custom styles for dashboard components

### JavaScript
- Chart.js for data visualization
- Custom scripts for real-time updates
- Dashboard widget functionality
- Alert management

### Images
- System icons
- Status indicators
- Logo and branding assets

## ESP32 Integration

The app is designed to receive and process data from ESP32 microcontrollers via the `ProcessSensorDataView`. The expected data formats are:

### Pump Data
```json
{
  "esp32_id": "ESP32_PumpStation_01",
  "timestamp": "2024-05-26T14:30:00Z",
  "pump_id": "Pump_01",
  "readings": [
    {"parameter": "flow_rate_lps", "value": 55.2, "quality": "good"},
    {"parameter": "outlet_pressure_bar", "value": 2.1, "quality": "good"},
    {"parameter": "motor_temp_c", "value": 65.0, "quality": "good"}
  ],
  "status": "running",
  "running_hours": 1205.5
}
```

### Motor Data
```json
{
  "esp32_id": "ESP32_PumpStation_01",
  "timestamp": "2024-05-26T14:45:00Z",
  "motor_id": "Stirring_Motor",
  "motor_type": "STIRRER",
  "status": "running",
  "rpm": 1200.0,
  "temperature": 45.0,
  "power": 2.5,
  "running_hours": 350.5
}
```

### Water Quality Data
```json
{
  "esp32_id": "ESP32_PumpStation_01",
  "timestamp": "2024-05-26T14:35:00Z",
  "location": "Nyamandlovu Station",
  "hardness_mg_l": 350.0,
  "ph": 7.8,
  "turbidity_ntu": 1.2,
  "temperature_c": 22.5,
  "conductivity_ms_cm": 450.0,
  "quality_status": "acceptable"
}
```

### Reservoir Data
```json
{
  "esp32_id": "ESP32_PumpStation_01",
  "timestamp": "2024-05-26T14:40:00Z",
  "reservoirs": [
    {
      "reservoir_id": "Magwegwe_Reservoir_East",
      "level_percentage": 65.0,
      "volume_liters": 1200000,
      "capacity_liters": 2000000
    }
  ]
}
```