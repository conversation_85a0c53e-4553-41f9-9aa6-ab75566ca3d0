# Unified Requirements for All-in-One Container
# Core Django dependencies
asgiref==3.8.1
Django==5.1.5
django-cors-headers==4.4.0
django-humanize==0.1.2
django-jazzmin==3.0.0
django-prometheus==2.3.1
django-rest-framework==0.1.0
djangorestframework==3.15.2
djangorestframework-simplejwt==5.3.1
djoser==2.3.1
drf-yasg==1.21.7
setuptools>=42.0.0

# Database
psycopg2-binary==2.9.9
dj-database-url==2.3.0

# Utilities
python-decouple==3.8
python-dotenv==1.0.1
humanize==4.11.0
PyJWT==2.9.0
requests==2.32.3

# Authentication
cryptography==44.0.0
defusedxml==0.8.0rc2
oauthlib==3.2.2
python3-openid==3.2.0
requests-oauthlib==2.0.0
social-auth-app-django==5.4.2
social-auth-core==4.5.4

# Redis and Celery (all-in-one)
redis>=4.5.0
celery[redis]>=5.3.0
django-celery-results>=2.5.0
django-redis>=5.4.0

# Audio processing (CPU-optimized)
numpy>=1.24.0
--find-links https://download.pytorch.org/whl/torch_stable.html
torch==2.3.1+cpu
torchaudio==2.3.1+cpu
openai-whisper>=20231117
pydub>=0.25.0
librosa>=0.10.0
soundfile>=0.12.0

# Process management
supervisor>=4.2.0
