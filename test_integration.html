<!DOCTYPE html>
<html>
<head>
    <title>Frontend-Backend Integration Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Frontend-Backend Integration Test</h1>
    
    <div class="test">
        <h3>Frontend Access Test</h3>
        <button onclick="testFrontend()">Test Frontend Loading</button>
        <div id="frontend-result"></div>
    </div>

    <div class="test">
        <h3>Backend API Test (Direct)</h3>
        <button onclick="testBackendDirect()">Test Backend Direct</button>
        <div id="backend-direct-result"></div>
    </div>

    <div class="test">
        <h3>Backend API Test (Through Proxy)</h3>
        <button onclick="testBackendProxy()">Test Backend Through Frontend Proxy</button>
        <div id="backend-proxy-result"></div>
    </div>

    <div class="test">
        <h3>Registration Test</h3>
        <form onsubmit="testRegistration(event)">
            <input type="email" id="email" placeholder="<EMAIL>" required>
            <input type="text" id="username" placeholder="testuser" required>
            <input type="password" id="password" placeholder="password123" required>
            <button type="submit">Test Registration</button>
        </form>
        <div id="registration-result"></div>
    </div>

    <script>
        function updateResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<pre>${message}</pre>`;
            element.className = isSuccess ? 'success' : 'error';
        }

        async function testFrontend() {
            try {
                const response = await fetch('http://localhost:3000');
                const text = await response.text();
                updateResult('frontend-result', `Frontend loaded successfully!\nStatus: ${response.status}\nContent length: ${text.length} chars`);
            } catch (error) {
                updateResult('frontend-result', `Frontend test failed: ${error.message}`, false);
            }
        }

        async function testBackendDirect() {
            try {
                const response = await fetch('http://localhost:8000/auth/users/', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                const text = await response.text();
                updateResult('backend-direct-result', `Backend accessible!\nStatus: ${response.status}\nResponse: ${text || 'Empty response'}`);
            } catch (error) {
                updateResult('backend-direct-result', `Backend direct test failed: ${error.message}`, false);
            }
        }

        async function testBackendProxy() {
            try {
                const response = await fetch('http://localhost:3000/auth/users/', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                const text = await response.text();
                updateResult('backend-proxy-result', `Backend proxy working!\nStatus: ${response.status}\nResponse: ${text || 'Empty response'}`);
            } catch (error) {
                updateResult('backend-proxy-result', `Backend proxy test failed: ${error.message}`, false);
            }
        }

        async function testRegistration(event) {
            event.preventDefault();
            const email = document.getElementById('email').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch('http://localhost:3000/auth/users/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email,
                        username,
                        password,
                        first_name: 'Test',
                        last_name: 'User'
                    })
                });
                
                const text = await response.text();
                let result;
                try {
                    result = JSON.parse(text);
                } catch {
                    result = text;
                }

                if (response.ok) {
                    updateResult('registration-result', `Registration successful!\nStatus: ${response.status}\nResponse: ${JSON.stringify(result, null, 2)}`);
                } else {
                    updateResult('registration-result', `Registration failed!\nStatus: ${response.status}\nResponse: ${JSON.stringify(result, null, 2)}`, false);
                }
            } catch (error) {
                updateResult('registration-result', `Registration test failed: ${error.message}`, false);
            }
        }

        // Auto-run frontend test on load
        window.onload = function() {
            testFrontend();
        };
    </script>
</body>
</html>
