# Djoser Module Integration Test Cases

This document provides test cases for verifying the Djoser module integration within the Django backend.

## Test Cases

### 1. User Registration
- **Description:** Register a new user with email and password.
- **Method:** POST
- **URL:** `/auth/users/`
- **Payload Example:**
  ```json
  {
    "email": "<EMAIL>",
    "username": "testuser",
    "password": "SecurePassword!23"
  }
  ```
- **Expected Result:** User is created, and a response contains a "created" status and user data.

### 2. User Activation
- **Description:** Activate a registered user.
- **Method:** POST
- **URL:** `/auth/users/activation/`
- **Payload Example:**
  ```json
  {
    "uid": "<uid>",
    "token": "<token>"
  }
  ```
- **Expected Result:** User account is activated, and a success message is returned.

### 3. Login and Token Retrieval
- **Description:** Retrieve JWT token with valid credentials.
- **Method:** POST
- **URL:** `/auth/jwt/create/`
- **Payload Example:**
  ```json
  {
    "email": "<EMAIL>",
    "password": "SecurePassword!23"
  }
  ```
- **Expected Result:** A JWT token pair (access and refresh) is returned.

### 4. Password Reset Request
- **Description:** Request a password reset.
- **Method:** POST
- **URL:** `/auth/users/reset_password/`
- **Payload Example:**
  ```json
  {
    "email": "<EMAIL>"
  }
  ```
- **Expected Result:** An email with reset instructions is sent.

### 5. Password Reset Confirmation
- **Description:** Confirm password reset with uid and token.
- **Method:** POST
- **URL:** `/auth/users/reset_password_confirm/`
- **Payload Example:**
  ```json
  {
    "uid": "<uid>",
    "token": "<token>",
    "new_password": "NewSecurePassword!23"
  }
  ```
- **Expected Result:** Password is reset, and a success message is returned.

### 6. User Profile Retrieval
- **Description:** Retrieve user profile using JWT token.
- **Method:** GET
- **URL:** `/auth/users/me/`
- **Headers:** Authorization: Bearer <access_token>
- **Expected Result:** User profile data is returned.

### 7. User Profile Update
- **Description:** Update user profile with new data.
- **Method:** PUT
- **URL:** `/auth/users/me/`
- **Headers:** Authorization: Bearer <access_token>
- **Payload Example:**
  ```json
  {
    "full_name": "New Name",
    "phone_number": "**********"
  }
  ```
- **Expected Result:** User profile is updated, and updated data is returned.

### 8. Account Deletion
- **Description:** Delete user account.
- **Method:** DELETE
- **URL:** `/auth/users/me/`
- **Headers:** Authorization: Bearer <access_token>
- **Expected Result:** User account is deleted, and a success message is returned.

