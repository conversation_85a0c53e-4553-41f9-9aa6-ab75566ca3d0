# Unified All-in-One Container: Backend + Redis + Celery + Celery-Beat
# Multi-stage build for minimal production image
FROM python:3.12-slim as builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install build dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    libsndfile1-dev \
    libffi-dev \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy requirements and install Python dependencies
COPY requirements.txt /tmp/
RUN pip install --upgrade pip && \
    pip install -r /tmp/requirements.unified.txt

# Production stage
FROM python:3.12-slim as production

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PATH="/opt/venv/bin:$PATH" \
    TORCH_HOME=/tmp/torch \
    HF_HOME=/tmp/huggingface \
    REDIS_URL=redis://localhost:6379/0

# Install runtime dependencies including Redis
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libpq5 \
    ffmpeg \
    libsndfile1 \
    curl \
    netcat-traditional \
    redis-server \
    supervisor \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv

# Create app user for security
RUN groupadd -r thetha && useradd -r -g thetha thetha

# Set working directory
WORKDIR /app

# Copy the Django project code
COPY --chown=thetha:thetha Backend/thetha/ /app/

# Copy simplified settings
COPY --chown=thetha:thetha simple_settings.py /app/

# Create necessary directories and set permissions
RUN mkdir -p /app/mediafiles /app/staticfiles /app/logs /tmp/torch /tmp/huggingface /var/lib/redis /var/log/redis && \
    chown -R thetha:thetha /app /tmp/torch /tmp/huggingface && \
    chown -R redis:redis /var/lib/redis /var/log/redis && \
    chmod 755 /var/lib/redis /var/log/redis

# Copy supervisor configuration
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Copy startup script
COPY start-services.sh /start-services.sh
RUN chmod +x /start-services.sh

# Keep as root for startup script, supervisor will handle user switching

# Expose ports
EXPOSE 8000 6379

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8000/health/ || exit 1

# Start all services
CMD ["/start-services.sh"]
