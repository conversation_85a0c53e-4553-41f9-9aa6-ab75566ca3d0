# Use Ubuntu-based Node image to avoid Alpine permission issues
FROM node:18-bullseye-slim as builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies and vite globally
RUN npm ci && npm install -g vite

# Copy source code
COPY . .

# Build the application using global vite
# Environment variables will be passed from docker-compose
RUN vite build

# Production stage with nginx
FROM nginx:alpine

# Copy built files to nginx
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx config
COPY nginx.conf /etc/nginx/nginx.conf

# Expose port 80
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
