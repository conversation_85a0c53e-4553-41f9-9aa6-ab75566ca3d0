{"name": "thetha", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "axios": "^1.6.2", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.20.1", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@eslint/js": "^9.12.0", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.16", "eslint": "^9.12.0", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.11.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^5.4.8"}}