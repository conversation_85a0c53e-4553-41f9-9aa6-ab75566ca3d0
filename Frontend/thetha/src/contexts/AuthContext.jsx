import React, { createContext, useContext, useEffect, useState } from 'react';
import { authAPI } from '../services/api';
import toast from 'react-hot-toast';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check if user is authenticated on app load
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (token) {
        const userData = await authAPI.getCurrentUser();
        setUser(userData);
        setIsAuthenticated(true);
      }
    } catch (error) {
      // Token might be expired or invalid
      logout();
    } finally {
      setLoading(false);
    }
  };

  const login = async (credentials) => {
    try {
      setLoading(true);
      const response = await authAPI.login(credentials);
      
      // Store tokens
      localStorage.setItem('access_token', response.access);
      localStorage.setItem('refresh_token', response.refresh);
      
      // Get user data
      const userData = await authAPI.getCurrentUser();
      setUser(userData);
      setIsAuthenticated(true);
      
      toast.success('Login successful!');
      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 
                          error.response?.data?.non_field_errors?.[0] ||
                          'Login failed. Please check your credentials.';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData) => {
    try {
      setLoading(true);
      await authAPI.register(userData);
      toast.success('Registration successful! Please check your email to activate your account.');
      return { success: true };
    } catch (error) {
      console.error('Registration error in AuthContext:', error);
      // Don't show toast for field errors, let the component handle them
      // Only show toast for general errors
      if (!error.response?.data || typeof error.response.data !== 'object') {
        const errorMessage = 'Registration failed. Please try again.';
        toast.error(errorMessage);
      }
      // Re-throw the error so the Register component can handle field-specific errors
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    setUser(null);
    setIsAuthenticated(false);
    toast.success('Logged out successfully');
  };

  const updateProfile = async (profileData) => {
    try {
      const updatedUser = await authAPI.updateProfile(profileData);
      setUser(updatedUser);
      toast.success('Profile updated successfully!');
      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.email?.[0] ||
                          error.response?.data?.username?.[0] ||
                          'Failed to update profile. Please try again.';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const changePassword = async (passwordData) => {
    try {
      await authAPI.changePassword(passwordData);
      toast.success('Password changed successfully!');
      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.new_password?.[0] ||
                          error.response?.data?.current_password?.[0] ||
                          error.response?.data?.non_field_errors?.[0] ||
                          'Failed to change password. Please try again.';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const requestPasswordReset = async (email) => {
    try {
      await authAPI.requestPasswordReset(email);
      toast.success('Password reset email sent! Please check your inbox.');
      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.email?.[0] ||
                          'Failed to send password reset email. Please try again.';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const confirmPasswordReset = async (resetData) => {
    try {
      await authAPI.confirmPasswordReset(resetData);
      toast.success('Password reset successful! Please login with your new password.');
      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.token?.[0] ||
                          error.response?.data?.new_password?.[0] ||
                          error.response?.data?.non_field_errors?.[0] ||
                          'Failed to reset password. Please try again.';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const activateAccount = async (activationData) => {
    try {
      await authAPI.activateAccount(activationData);
      toast.success('Account activated successfully! You can now login.');
      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.token?.[0] ||
                          error.response?.data?.uid?.[0] ||
                          error.response?.data?.non_field_errors?.[0] ||
                          'Failed to activate account. Please try again.';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const resendActivation = async (email) => {
    try {
      await authAPI.resendActivation(email);
      toast.success('Activation email sent! Please check your inbox.');
      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.email?.[0] ||
                          'Failed to send activation email. Please try again.';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Social authentication
  const socialLogin = (provider) => {
    const baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:8000';
    const socialUrl = `${baseUrl}/auth/o/${provider}/`;
    
    // Create a popup window for social authentication
    const popup = window.open(
      socialUrl,
      'social-auth',
      'width=600,height=600,scrollbars=yes,resizable=yes'
    );

    // Listen for the popup to close
    const checkClosed = setInterval(() => {
      if (popup.closed) {
        clearInterval(checkClosed);
        // Check if authentication was successful
        checkAuthStatus();
      }
    }, 1000);

    return popup;
  };

  const loginWithGoogle = () => {
    return socialLogin('google-oauth2');
  };

  const loginWithGithub = () => {
    return socialLogin('github');
  };

  const value = {
    user,
    loading,
    isAuthenticated,
    login,
    register,
    logout,
    updateProfile,
    changePassword,
    requestPasswordReset,
    confirmPasswordReset,
    activateAccount,
    resendActivation,
    checkAuthStatus,
    loginWithGoogle,
    loginWithGithub,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
