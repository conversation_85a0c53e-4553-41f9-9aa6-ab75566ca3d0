import React from 'react';
import { cn } from '../../utils';

const LoadingSpinner = ({ 
  size = 'md', 
  color = 'primary', 
  className,
  text 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
  };

  const colorClasses = {
    primary: 'text-primary',
    white: 'text-white',
    gray: 'text-gray-500',
    success: 'text-success',
    error: 'text-error',
    warning: 'text-warning',
  };

  return (
    <div className={cn('flex flex-col items-center justify-center', className)}>
      <div
        className={cn(
          'animate-spin rounded-full border-2 border-gray-300 border-t-current',
          sizeClasses[size],
          colorClasses[color]
        )}
      />
      {text && (
        <p className={cn(
          'mt-2 text-sm font-medium',
          colorClasses[color] === 'text-white' ? 'text-white' : 'text-gray-600 dark:text-gray-400'
        )}>
          {text}
        </p>
      )}
    </div>
  );
};

export default LoadingSpinner;
