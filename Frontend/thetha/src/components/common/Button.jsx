import React from 'react';
import { cn } from '../../utils';
import LoadingSpinner from './LoadingSpinner';

const Button = ({
  children,
  className,
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

  const variants = {
    primary: 'bg-primary hover:bg-primaryDark text-white focus:ring-primary/50 shadow-md hover:shadow-lg',
    secondary: 'bg-accent hover:bg-accent/90 text-white focus:ring-accent/50 shadow-md hover:shadow-lg',
    outline: 'border-2 border-primary text-primary hover:bg-primary hover:text-white focus:ring-primary/50',
    ghost: 'text-primary hover:bg-primary/10 focus:ring-primary/50',
    danger: 'bg-error hover:bg-error/90 text-white focus:ring-error/50 shadow-md hover:shadow-lg',
    success: 'bg-success hover:bg-success/90 text-white focus:ring-success/50 shadow-md hover:shadow-lg',
    warning: 'bg-warning hover:bg-warning/90 text-white focus:ring-warning/50 shadow-md hover:shadow-lg',
    dark: 'bg-gray-800 hover:bg-gray-900 text-white focus:ring-gray-500/50 shadow-md hover:shadow-lg',
    light: 'bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 focus:ring-gray-400/50 shadow-sm hover:shadow-md',
  };

  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
    xl: 'px-8 py-4 text-lg',
  };

  const isDisabled = disabled || loading;

  return (
    <button
      className={cn(
        baseClasses,
        variants[variant],
        sizes[size],
        fullWidth && 'w-full',
        isDisabled && 'opacity-50 cursor-not-allowed',
        className
      )}
      disabled={isDisabled}
      {...props}
    >
      {loading && (
        <LoadingSpinner 
          size="sm" 
          color={variant === 'light' ? 'gray' : 'white'} 
          className="mr-2" 
        />
      )}
      
      {!loading && icon && iconPosition === 'left' && (
        <span className="mr-2">{icon}</span>
      )}
      
      {children}
      
      {!loading && icon && iconPosition === 'right' && (
        <span className="ml-2">{icon}</span>
      )}
    </button>
  );
};

export default Button;
