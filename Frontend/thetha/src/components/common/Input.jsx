import React, { forwardRef } from 'react';
import { cn } from '../../utils';

const Input = forwardRef(({
  className,
  type = 'text',
  label,
  error,
  helper,
  icon,
  iconPosition = 'left',
  ...props
}, ref) => {
  const baseClasses = 'flex w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/50 focus-visible:border-primary disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 dark:placeholder:text-gray-400';

  const errorClasses = error 
    ? 'border-error focus-visible:ring-error/50 focus-visible:border-error' 
    : '';

  const iconClasses = icon 
    ? iconPosition === 'left' 
      ? 'pl-10' 
      : 'pr-10'
    : '';

  return (
    <div className="space-y-2">
      {label && (
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
        </label>
      )}
      
      <div className="relative">
        {icon && (
          <div className={cn(
            'absolute inset-y-0 flex items-center px-3 pointer-events-none',
            iconPosition === 'left' ? 'left-0' : 'right-0'
          )}>
            <span className="text-gray-400 dark:text-gray-500">
              {icon}
            </span>
          </div>
        )}
        
        <input
          type={type}
          className={cn(
            baseClasses,
            errorClasses,
            iconClasses,
            className
          )}
          ref={ref}
          {...props}
        />
      </div>
      
      {error && (
        <p className="text-sm text-error">{error}</p>
      )}
      
      {helper && !error && (
        <p className="text-sm text-gray-500 dark:text-gray-400">{helper}</p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;
