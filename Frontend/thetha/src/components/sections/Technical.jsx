import React from 'react';

const Technical = () => {
  return (
    <section id="technical" className="py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Technical Excellence</h2>
          <p className="text-lg text-gray-700 dark:text-gray-300 max-w-3xl mx-auto">
            Built with cutting-edge technology and designed for developers.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          {/* API Documentation Preview */}
          <div className="bg-gray-900 rounded-xl shadow-2xl overflow-hidden">
            <div className="bg-gray-800 px-4 py-2 flex items-center">
              <div className="flex space-x-2 mr-4">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
              </div>
              <div className="text-gray-400 text-sm font-mono">api.thetha.ai/v1/transcribe</div>
            </div>
            <div className="p-6">
              <pre className="text-sm font-mono">
                <code className="language-javascript text-gray-300">
{`// Initialize Thetha client
const thetha = new Thetha({
  apiKey: process.env.THETHA_API_KEY
});

// Start transcription
const transcription = await thetha.transcribe({
  audio: audioFile,
  language: 'en',
  speakers: 'auto',
  format: 'detailed',
  callback_url: 'https://your-app.com/webhook'
});

// Real-time transcription events
thetha.on('transcription.progress', (event) => {
  console.log(\`Progress: \${event.progress}%\`);
  console.log(\`Text: \${event.text}\`);
});

// Get AI summary
const summary = await thetha.summarize({
  transcriptionId: transcription.id,
  format: 'bullets'
});`}
                </code>
              </pre>
            </div>
          </div>

          {/* Technical Features */}
          <div className="space-y-8">
            <div>
              <h3 className="text-2xl font-bold mb-4">Powerful API Integration</h3>
              <p className="text-gray-700 dark:text-gray-300 mb-6">
                Integrate Thetha&apos;s powerful transcription and AI capabilities directly into your applications with our comprehensive API.
              </p>
              <div className="grid sm:grid-cols-2 gap-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <i className="fas fa-bolt text-primary"></i>
                  </div>
                  <div className="ml-4">
                    <h4 className="font-semibold mb-1">Real-time Events</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">WebSocket support for live transcription updates</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <i className="fas fa-code text-primary"></i>
                  </div>
                  <div className="ml-4">
                    <h4 className="font-semibold mb-1">RESTful API</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Simple and intuitive REST endpoints</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <i className="fas fa-key text-primary"></i>
                  </div>
                  <div className="ml-4">
                    <h4 className="font-semibold mb-1">Secure Access</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">API key authentication and HTTPS encryption</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <i className="fas fa-book text-primary"></i>
                  </div>
                  <div className="ml-4">
                    <h4 className="font-semibold mb-1">Documentation</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Comprehensive guides and examples</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="border-t border-gray-200 dark:border-gray-700 pt-8">
              <h3 className="text-2xl font-bold mb-4">Technical Specifications</h3>
              <div className="grid sm:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-2">Supported Audio Formats</h4>
                  <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                    <li className="flex items-center">
                      <i className="fas fa-check text-green-500 mr-2"></i>
                      MP3, WAV, M4A
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check text-green-500 mr-2"></i>
                      OGG, FLAC, AAC
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check text-green-500 mr-2"></i>
                      WebM, MP4 Audio
                    </li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Language Support</h4>
                  <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                    <li className="flex items-center">
                      <i className="fas fa-check text-green-500 mr-2"></i>
                      50+ Languages
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check text-green-500 mr-2"></i>
                      Dialect Detection
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check text-green-500 mr-2"></i>
                      Auto Language Detection
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* SDK Support */}
        <div className="text-center">
          <h3 className="text-2xl font-bold mb-8">SDK Support</h3>
          <div className="flex flex-wrap justify-center gap-8">
            <div className="flex items-center space-x-3 bg-white dark:bg-gray-800 px-6 py-3 rounded-lg shadow-md">
              <i className="fab fa-js text-yellow-400 text-2xl"></i>
              <span className="font-medium">JavaScript</span>
            </div>
            <div className="flex items-center space-x-3 bg-white dark:bg-gray-800 px-6 py-3 rounded-lg shadow-md">
              <i className="fab fa-python text-blue-500 text-2xl"></i>
              <span className="font-medium">Python</span>
            </div>
            <div className="flex items-center space-x-3 bg-white dark:bg-gray-800 px-6 py-3 rounded-lg shadow-md">
              <i className="fab fa-java text-red-500 text-2xl"></i>
              <span className="font-medium">Java</span>
            </div>
            <div className="flex items-center space-x-3 bg-white dark:bg-gray-800 px-6 py-3 rounded-lg shadow-md">
              <i className="fab fa-php text-purple-500 text-2xl"></i>
              <span className="font-medium">PHP</span>
            </div>
            <div className="flex items-center space-x-3 bg-white dark:bg-gray-800 px-6 py-3 rounded-lg shadow-md">
              <i className="fas fa-code text-gray-600 dark:text-gray-400 text-2xl"></i>
              <span className="font-medium">More coming soon</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Technical;
