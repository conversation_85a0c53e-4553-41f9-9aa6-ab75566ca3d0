import React from 'react';

const interfaceFeatures = [
  {
    icon: 'fa-palette',
    title: 'Sleek Modern Design',
    description: 'A clean, intuitive interface that puts functionality first without compromising on aesthetics.'
  },
  {
    icon: 'fa-tachometer-alt',
    title: 'Organized Dashboard',
    description: 'Easily access ongoing and past transcriptions, categorized by project or conversation type.'
  },
  {
    icon: 'fa-magic',
    title: 'Interactive AI Tools',
    description: 'Powerful editing tools for refining transcriptions with AI assistance at every step.'
  },
  {
    icon: 'fa-moon',
    title: 'Dark Mode & Accessibility',
    description: 'Support for dark mode and accessibility options ensures comfort for all users, in any environment.'
  }
];

const Interface = () => {
  return (
    <section id="interface" className="py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">User Experience &amp; Interface</h2>
          <p className="text-lg text-gray-700 dark:text-gray-300 max-w-3xl mx-auto">
            A sleek, modern, and intuitive UI/UX that prioritizes ease of use across all devices.
          </p>
        </div>

        <div className="flex flex-col lg:flex-row items-center">
          {/* Dashboard Preview */}
          <div className="lg:w-3/5 mb-10 lg:mb-0">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl overflow-hidden">
              <div className="bg-gray-100 dark:bg-gray-700 px-4 py-3 flex items-center">
                <div className="flex space-x-2 mr-4">
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                </div>
                <div className="text-sm font-medium">Thetha Dashboard</div>
              </div>
              <div className="px-6 py-6">
                <div className="flex flex-wrap -mx-2">
                  <div className="w-full lg:w-1/4 px-2 mb-4">
                    <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 h-full">
                      <h4 className="text-sm font-semibold mb-4">Projects</h4>
                      <ul className="space-y-3">
                        <li className="flex items-center text-primary font-medium">
                          <i className="fas fa-folder mr-2"></i>
                          <span>Quarterly Meeting</span>
                        </li>
                        <li className="flex items-center text-gray-700 dark:text-gray-300">
                          <i className="fas fa-folder mr-2"></i>
                          <span>Client Interviews</span>
                        </li>
                        <li className="flex items-center text-gray-700 dark:text-gray-300">
                          <i className="fas fa-folder mr-2"></i>
                          <span>Research Notes</span>
                        </li>
                        <li className="flex items-center text-gray-700 dark:text-gray-300">
                          <i className="fas fa-folder-plus mr-2"></i>
                          <span>New Project</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div className="w-full lg:w-3/4 px-2">
                    <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
                      <div className="flex justify-between items-center mb-4">
                        <h4 className="text-lg font-semibold">Quarterly Meeting</h4>
                        <div className="flex space-x-2">
                          <button className="p-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-800">
                            <i className="fas fa-edit text-primary"></i>
                          </button>
                          <button className="p-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-800">
                            <i className="fas fa-share-alt text-primary"></i>
                          </button>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="flex items-start">
                          <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs font-bold mr-3">JD</div>
                          <div className="bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm max-w-[80%]">
                            <p className="text-sm">We need to discuss the quarterly results in detail.</p>
                            <span className="text-xs text-gray-500">14:23 - John Doe</span>
                          </div>
                          <div className="ml-2 flex flex-col">
                            <button className="p-1 text-gray-400 hover:text-primary">
                              <i className="fas fa-highlighter"></i>
                            </button>
                            <button className="p-1 text-gray-400 hover:text-primary">
                              <i className="fas fa-comment"></i>
                            </button>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center text-white text-xs font-bold mr-3">AS</div>
                          <div className="bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm max-w-[80%]">
                            <p className="text-sm">I&apos;ve prepared the slides with all the key performance indicators.</p>
                            <span className="text-xs text-gray-500">14:24 - Anna Smith</span>
                          </div>
                          <div className="ml-2 flex flex-col">
                            <button className="p-1 text-gray-400 hover:text-primary">
                              <i className="fas fa-highlighter"></i>
                            </button>
                            <button className="p-1 text-gray-400 hover:text-primary">
                              <i className="fas fa-comment"></i>
                            </button>
                          </div>
                        </div>
                        <div className="border-l-4 border-primary bg-primary/10 p-3 rounded-r-lg">
                          <p className="text-xs font-semibold text-primary mb-1">AI Summary &amp; Action Items:</p>
                          <p className="text-sm">Discussion of quarterly results with prepared KPI slides. Key focus areas include market performance and revenue streams.</p>
                          <div className="flex mt-2 space-x-2">
                            <span className="bg-primary text-white text-xs px-2 py-1 rounded-full">Review KPIs</span>
                            <span className="bg-primary text-white text-xs px-2 py-1 rounded-full">Prepare report</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Interface Features */}
          <div className="lg:w-2/5 lg:pl-12">
            <h3 className="text-2xl font-bold mb-6">Designed for Effortless Use</h3>
            <div className="space-y-6">
              {interfaceFeatures.map((feature, index) => (
                <div key={index} className="flex">
                  <div className="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                    <i className={`fas ${feature.icon} text-primary`}></i>
                  </div>
                  <div className="ml-4">
                    <h4 className="text-lg font-semibold mb-1">{feature.title}</h4>
                    <p className="text-gray-600 dark:text-gray-400">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Interface;
