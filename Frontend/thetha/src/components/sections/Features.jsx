import React from 'react';

const features = [
  {
    icon: 'fa-microphone-alt',
    title: 'Real-Time Transcription',
    description: 'Instantly transcribe conversations, meetings, and interviews as they happen with industry-leading accuracy.'
  },
  {
    icon: 'fa-language',
    title: 'Multi-Language Support',
    description: 'Support for over 50 languages and dialects to break communication barriers and enhance global collaboration.'
  },
  {
    icon: 'fa-cogs',
    title: 'Industry-Specific Customization',
    description: 'Adaptation to specialized jargon and terminology for precise transcriptions in legal, medical, academic, and media fields.'
  },
  {
    icon: 'fa-users',
    title: 'Speaker Identification',
    description: 'Automatically detect and label different speakers in a conversation for clear, organized transcripts.'
  },
  {
    icon: 'fa-edit',
    title: 'AI-Powered Editing Tools',
    description: 'Highlight, annotate, and summarize key points effortlessly with intuitive editing capabilities.'
  },
  {
    icon: 'fa-cloud',
    title: 'Cloud Integration & Collaboration',
    description: 'Sync with Google Drive, Dropbox, and Microsoft Teams for seamless sharing and collaborative editing.'
  },
  {
    icon: 'fa-shield-alt',
    title: 'Security & Compliance',
    description: 'Bank-level encryption and GDPR/HIPAA compliance ensures your sensitive data remains protected at all times.'
  },
  {
    icon: 'fa-book',
    title: 'Custom Vocabulary & Training',
    description: 'Teach the AI your unique vocabulary, acronyms, and phrases for increasingly accurate transcriptions over time.'
  },
  {
    icon: 'fa-volume-up',
    title: 'Audio Enhancement Technology',
    description: 'Enhance transcription quality with noise reduction and audio improvement, even in challenging environments.'
  },
  {
    icon: 'fa-chart-line',
    title: 'Analytics & Insights',
    description: 'Gain valuable insights with sentiment analysis, keyword tracking, and conversation analytics for data-driven decisions.'
  }
];

const Features = () => {
  return (
    <section className="py-16 bg-[#1a1f2d]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Core Features</h2>
          <p className="text-lg text-gray-300 max-w-3xl mx-auto">
            Thetha offers cutting-edge transcription capabilities designed for professionals across industries.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="feature-card bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md transition-all duration-300">
              <div className="w-14 h-14 bg-primary/10 rounded-full flex items-center justify-center mb-5">
                <i className={`fas ${feature.icon} text-xl text-primary`}></i>
              </div>
              <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
              <p className="text-gray-600 dark:text-gray-400">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;
