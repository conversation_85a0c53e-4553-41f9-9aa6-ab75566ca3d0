import React from 'react';

const Hero = () => {
  return (
    <section className="relative min-h-[90vh] flex items-center bg-gray-900 dark:bg-gray-900 overflow-hidden">
      <div className="container mx-auto px-4 py-12 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="space-y-6">
            <h1 className="text-5xl lg:text-6xl font-bold leading-tight">
              Revolutionize{' '}
              <span className="text-primary">Conversations</span>{' '}
              with AI-Powered Transcription
            </h1>
            <p className="text-lg text-gray-300">
              Thetha transforms how professionals capture and process conversations
              in real-time with unparalleled accuracy, speed, and ease of use.
            </p>
            <div className="flex flex-wrap gap-4">
              <button className="px-8 py-3 bg-primary hover:bg-primary/90 text-white rounded-full font-medium transition-colors">
                Start Free Trial
              </button>
              <button className="px-8 py-3 border border-primary text-primary hover:bg-primary/10 rounded-full font-medium transition-colors">
                Explore Features
              </button>
            </div>
            <div className="flex items-center space-x-2 text-gray-400">
              <i className="fas fa-check-circle text-primary"></i>
              <span>No credit card required</span>
            </div>
          </div>

          {/* Right Content - Preview */}
          <div className="relative">
            <div className="relative">
              <div className="w-full h-full rounded-xl bg-gradient-to-br from-primaryLight to-primary p-1 shadow-2xl">
                <div className="bg-white dark:bg-gray-900 rounded-lg p-4 h-full">
                  <div className="flex items-center mb-4">
                    <div className="flex space-x-2">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    </div>
                    <div className="ml-4 text-sm font-medium">Thetha Transcription</div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs font-bold mr-3">JD</div>
                      <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 max-w-[80%]">
                        <p className="text-sm">We need to discuss the quarterly results in detail.</p>
                        <span className="text-xs text-gray-500">John Doe - CEO</span>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center text-white text-xs font-bold mr-3">AS</div>
                      <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 max-w-[80%]">
                        <p className="text-sm">I've prepared the slides with all the key performance indicators.</p>
                        <span className="text-xs text-gray-500">Anna Smith - CFO</span>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-full bg-purple-500 flex items-center justify-center text-white text-xs font-bold mr-3">MJ</div>
                      <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 max-w-[80%]">
                        <p className="text-sm">Great, I'd like to focus specifically on the new market segment we entered last quarter.</p>
                        <span className="text-xs text-gray-500">Mark Johnson - COO</span>
                      </div>
                    </div>
                    <div className="bg-primary/10 border border-primary/30 rounded-lg p-3">
                      <p className="text-xs font-semibold text-primary">Thetha AI Summary:</p>
                      <p className="text-sm">Meeting discussing Q3 results with focus on new market segment performance. Action items: Review KPIs and prepare market analysis report.</p>
                    </div>
                  </div>
                </div>
              </div>
              {/* Floating elements */}
              <div className="absolute -top-4 -right-4 bg-white dark:bg-gray-900 shadow-lg rounded-lg px-3 py-2 text-sm flex items-center">
                <i className="fas fa-language text-primary mr-2"></i>
                <span>50+ Languages</span>
              </div>
              <div className="absolute -bottom-4 -left-4 bg-white dark:bg-gray-900 shadow-lg rounded-lg px-3 py-2 text-sm flex items-center">
                <i className="fas fa-microphone-alt text-primary mr-2"></i>
                <span>Real-time Transcription</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Wave Background */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-b from-transparent to-[#1a1f2d]">
        <svg
          className="absolute bottom-0 w-full h-full"
          viewBox="0 0 1440 320"
          preserveAspectRatio="none"
        >
          <path
            fill="#1a1f2d"
            fillOpacity="1"
            d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,112C672,96,768,96,864,112C960,128,1056,160,1152,160C1248,160,1344,128,1392,112L1440,96L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
          ></path>
        </svg>
      </div>
    </section>
  );
};

export default Hero;
