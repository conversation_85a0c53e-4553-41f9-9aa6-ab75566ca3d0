import React from 'react';

const pricingTiers = [
  {
    name: 'Basic',
    price: '0',
    description: 'Perfect for individuals and small teams getting started.',
    features: [
      'Up to 5 hours of transcription per month',
      'Basic AI summarization',
      'Export to common formats',
      'Email support',
      '2 team members'
    ],
    cta: 'Start Free',
    popular: false
  },
  {
    name: 'Pro',
    price: '29',
    description: 'Ideal for growing teams and businesses.',
    features: [
      'Up to 50 hours of transcription per month',
      'Advanced AI summarization',
      'Custom vocabulary training',
      'Priority support',
      'Up to 10 team members',
      'API access',
      'Analytics dashboard'
    ],
    cta: 'Start Pro Trial',
    popular: true
  },
  {
    name: 'Enterprise',
    price: 'Custom',
    description: 'Advanced features for large organizations.',
    features: [
      'Unlimited transcription hours',
      'Enterprise-grade AI features',
      'Custom integration support',
      'Dedicated account manager',
      'Unlimited team members',
      'Custom API solutions',
      'Advanced analytics',
      'SLA guarantee'
    ],
    cta: 'Contact Sales',
    popular: false
  }
];

const Pricing = () => {
  return (
    <section id="pricing" className="py-16 bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Simple, Transparent Pricing</h2>
          <p className="text-lg text-gray-700 dark:text-gray-300 max-w-3xl mx-auto">
            Choose the perfect plan for your needs. All plans include a 14-day free trial.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {pricingTiers.map((tier, index) => (
            <div
              key={index}
              className={`relative bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden transform transition-transform hover:scale-105 ${
                tier.popular ? 'ring-2 ring-primary' : ''
              }`}
            >
              {tier.popular && (
                <div className="absolute top-0 right-0 bg-primary text-white px-4 py-1 rounded-bl-lg text-sm font-medium">
                  Most Popular
                </div>
              )}
              <div className="p-8">
                <h3 className="text-2xl font-bold mb-2">{tier.name}</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">{tier.description}</p>
                <div className="mb-6">
                  <span className="text-4xl font-bold">
                    {tier.price === 'Custom' ? (
                      'Custom'
                    ) : (
                      <>
                        ${tier.price}
                        <span className="text-lg text-gray-600 dark:text-gray-400">/month</span>
                      </>
                    )}
                  </span>
                </div>
                <ul className="space-y-4 mb-8">
                  {tier.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <i className="fas fa-check text-green-500 mt-1 mr-3"></i>
                      <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>
                <button
                  className={`w-full py-3 px-6 rounded-lg font-medium transition-colors ${
                    tier.popular
                      ? 'bg-primary text-white hover:bg-primary/90'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  {tier.cta}
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-16 text-center">
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            All plans include core features:
          </p>
          <div className="flex flex-wrap justify-center gap-8 max-w-4xl mx-auto">
            <div className="flex items-center">
              <i className="fas fa-shield-alt text-primary mr-2"></i>
              <span>Enterprise-grade security</span>
            </div>
            <div className="flex items-center">
              <i className="fas fa-cloud text-primary mr-2"></i>
              <span>Cloud storage</span>
            </div>
            <div className="flex items-center">
              <i className="fas fa-sync text-primary mr-2"></i>
              <span>Regular updates</span>
            </div>
            <div className="flex items-center">
              <i className="fas fa-headset text-primary mr-2"></i>
              <span>24/7 Support</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Pricing;
