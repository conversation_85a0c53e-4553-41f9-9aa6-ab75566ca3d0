.transcription-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.transcription-page h1 {
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 2.5rem;
  font-weight: 600;
}

.transcription-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.error {
  background-color: #fee;
  color: #c33;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  border: 1px solid #fcc;
}

@media (max-width: 768px) {
  .transcription-page {
    padding: 1rem;
  }
  
  .transcription-page h1 {
    font-size: 2rem;
  }
  
  .transcription-list {
    grid-template-columns: 1fr;
  }
}
