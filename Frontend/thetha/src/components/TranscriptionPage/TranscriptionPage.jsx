import React, { useEffect, useState } from 'react';
import { transcriptionAPI } from '../../services/api';
import TranscriptionCard from './TranscriptionCard';
import './TranscriptionPage.css';

const TranscriptionPage = () => {
  const [transcriptions, setTranscriptions] = useState([]);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchTranscriptions();
  }, []);

  const fetchTranscriptions = async () => {
    try {
      const data = await transcriptionAPI.getAll();
      setTranscriptions(data);
    } catch (err) {
      setError('Failed to fetch transcriptions.');
    }
  };

  const handleDelete = async (id) => {
    try {
      await transcriptionAPI.delete(id);
      fetchTranscriptions();
    } catch (err) {
      setError('Failed to delete transcription.');
    }
  };

  return (
    <div className="transcription-page">
      {error && <div className="error">{error}</div>}
      <h1>Transcriptions</h1>
      <div className="transcription-list">
        {transcriptions.map((transcription) => (
          <TranscriptionCard
            key={transcription.id}
            transcription={transcription}
            onDelete={handleDelete}
          />
        ))}
      </div>
    </div>
  );
};

export default TranscriptionPage;

