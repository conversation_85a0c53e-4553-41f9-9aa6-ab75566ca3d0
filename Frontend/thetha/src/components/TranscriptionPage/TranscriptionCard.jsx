import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { transcriptionAPI } from '../../services/api';
import './TranscriptionCard.css';

const TranscriptionCard = ({ transcription, onDelete }) => {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this transcription?')) {
      setIsDeleting(true);
      try {
        await onDelete(transcription.id);
      } catch (error) {
        console.error('Error deleting transcription:', error);
      } finally {
        setIsDeleting(false);
      }
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return '#27ae60';
      case 'processing':
        return '#f39c12';
      case 'failed':
        return '#e74c3c';
      case 'pending':
        return '#95a5a6';
      default:
        return '#95a5a6';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDuration = (seconds) => {
    if (!seconds) return 'Unknown';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="transcription-card">
      <div className="card-header">
        <h3 className="card-title">{transcription.original_filename || 'Untitled'}</h3>
        <span 
          className="status-badge" 
          style={{ backgroundColor: getStatusColor(transcription.status) }}
        >
          {transcription.status}
        </span>
      </div>
      
      <div className="card-body">
        <div className="card-info">
          <div className="info-item">
            <span className="info-label">Duration:</span>
            <span className="info-value">{formatDuration(transcription.duration)}</span>
          </div>
          <div className="info-item">
            <span className="info-label">Created:</span>
            <span className="info-value">{formatDate(transcription.created_at)}</span>
          </div>
          {transcription.language && (
            <div className="info-item">
              <span className="info-label">Language:</span>
              <span className="info-value">{transcription.language}</span>
            </div>
          )}
        </div>
        
        {transcription.preview_text && (
          <div className="preview-text">
            <p>"{transcription.preview_text.substring(0, 100)}..."</p>
          </div>
        )}
      </div>
      
      <div className="card-actions">
        <Link 
          to={`/transcription/${transcription.id}`}
          className="btn btn-primary"
        >
          View Details
        </Link>
        
        {transcription.status === 'completed' && (
          <button 
            className="btn btn-secondary"
            onClick={() => window.open(`/api/transcribe/${transcription.id}/export/`, '_blank')}
          >
            Download
          </button>
        )}
        
        <button 
          className="btn btn-danger"
          onClick={handleDelete}
          disabled={isDeleting}
        >
          {isDeleting ? 'Deleting...' : 'Delete'}
        </button>
      </div>
    </div>
  );
};

export default TranscriptionCard;
