import React, { useState } from 'react';
import { 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  FileText, 
  Download, 
  Trash2, 
  Eye, 
  Share2,
  MoreVertical,
  Calendar,
  Volume2,
  Languages
} from 'lucide-react';
import { Link } from 'react-router-dom';
import Button from '../common/Button';
import { transcriptionService } from '../../services/transcriptionService';
import toast from 'react-hot-toast';

const TranscriptionCard = ({ transcription, onDelete }) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [downloading, setDownloading] = useState(false);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'processing':
        return <Clock className="w-5 h-5 text-yellow-500 animate-pulse" />;
      case 'failed':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'processing':
        return 'Processing';
      case 'failed':
        return 'Failed';
      default:
        return 'Pending';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'text-green-700 bg-green-50 dark:text-green-400 dark:bg-green-900/20';
      case 'processing':
        return 'text-yellow-700 bg-yellow-50 dark:text-yellow-400 dark:bg-yellow-900/20';
      case 'failed':
        return 'text-red-700 bg-red-50 dark:text-red-400 dark:bg-red-900/20';
      default:
        return 'text-gray-700 bg-gray-50 dark:text-gray-400 dark:bg-gray-900/20';
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDuration = (seconds) => {
    if (!seconds) return 'Unknown';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getLanguageLabel = (code) => {
    const languages = {
      'en': 'English',
      'es': 'Spanish',
      'fr': 'French',
      'de': 'German',
      'it': 'Italian',
      'pt': 'Portuguese',
      'ru': 'Russian',
      'ja': 'Japanese',
      'ko': 'Korean',
      'zh': 'Chinese',
      'ar': 'Arabic',
      'hi': 'Hindi',
      'auto': 'Auto-detected'
    };
    return languages[code] || code;
  };

  const handleDownload = async (format = 'txt') => {
    try {
      setDownloading(true);
      await transcriptionService.downloadTranscription(transcription.id, format);
      toast.success(`Transcription downloaded as ${format.toUpperCase()}`);
    } catch (error) {
      console.error('Download failed:', error);
      toast.error('Failed to download transcription');
    } finally {
      setDownloading(false);
      setShowDropdown(false);
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this transcription?')) {
      try {
        await onDelete(transcription.id);
      } catch (error) {
        console.error('Delete failed:', error);
      }
    }
    setShowDropdown(false);
  };

  const handleShare = async () => {
    try {
      const shareData = {
        expires_in: 7 * 24 * 60 * 60, // 7 days
        permissions: ['view']
      };
      const result = await transcriptionService.shareTranscription(transcription.id, shareData);
      
      // Copy share link to clipboard
      await navigator.clipboard.writeText(result.share_url);
      toast.success('Share link copied to clipboard!');
    } catch (error) {
      console.error('Share failed:', error);
      toast.error('Failed to create share link');
    } finally {
      setShowDropdown(false);
    }
  };

  const truncateText = (text, maxLength = 150) => {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3">
              <FileText className="w-5 h-5 text-primary flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white truncate">
                  {transcription.title || transcription.filename || 'Untitled'}
                </h3>
                {transcription.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {transcription.description}
                  </p>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2 ml-4">
            {/* Status Badge */}
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(transcription.status)}`}>
              {getStatusIcon(transcription.status)}
              <span className="ml-1">{getStatusText(transcription.status)}</span>
            </span>

            {/* Dropdown Menu */}
            <div className="relative">
              <button
                onClick={() => setShowDropdown(!showDropdown)}
                className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400"
              >
                <MoreVertical className="w-4 h-4" />
              </button>

              {showDropdown && (
                <div className="absolute right-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg z-10 border border-gray-200 dark:border-gray-700">
                  {transcription.status === 'completed' && (
                    <>
                      <Link
                        to={`/transcriptions/${transcription.id}`}
                        className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                        onClick={() => setShowDropdown(false)}
                      >
                        <Eye className="w-4 h-4 mr-3" />
                        View Details
                      </Link>
                      <button
                        onClick={() => handleDownload('txt')}
                        disabled={downloading}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      >
                        <Download className="w-4 h-4 mr-3" />
                        Download as TXT
                      </button>
                      <button
                        onClick={() => handleDownload('pdf')}
                        disabled={downloading}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      >
                        <Download className="w-4 h-4 mr-3" />
                        Download as PDF
                      </button>
                      <button
                        onClick={handleShare}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      >
                        <Share2 className="w-4 h-4 mr-3" />
                        Share
                      </button>
                      <div className="border-t border-gray-200 dark:border-gray-700"></div>
                    </>
                  )}
                  <button
                    onClick={handleDelete}
                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
                  >
                    <Trash2 className="w-4 h-4 mr-3" />
                    Delete
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {/* Metadata */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-sm">
          <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
            <Calendar className="w-4 h-4" />
            <span>{formatDate(transcription.created_at)}</span>
          </div>
          
          {transcription.duration && (
            <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
              <Volume2 className="w-4 h-4" />
              <span>{formatDuration(transcription.duration)}</span>
            </div>
          )}

          {transcription.language && (
            <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
              <Languages className="w-4 h-4" />
              <span>{getLanguageLabel(transcription.language)}</span>
            </div>
          )}

          {transcription.file_size && (
            <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
              <FileText className="w-4 h-4" />
              <span>{(transcription.file_size / (1024 * 1024)).toFixed(1)} MB</span>
            </div>
          )}
        </div>

        {/* Transcript Preview */}
        {transcription.status === 'completed' && transcription.transcript && (
          <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
              Transcript Preview
            </h4>
            <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
              {truncateText(transcription.transcript)}
            </p>
            {transcription.transcript.length > 150 && (
              <Link
                to={`/transcriptions/${transcription.id}`}
                className="inline-flex items-center mt-2 text-sm text-primary hover:text-primary-dark"
              >
                Read more
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            )}
          </div>
        )}

        {/* Processing Status */}
        {transcription.status === 'processing' && (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-yellow-500 animate-pulse" />
              <div className="text-sm text-yellow-700 dark:text-yellow-300">
                <p className="font-medium">Processing in progress...</p>
                <p className="text-xs mt-1">
                  This may take a few minutes depending on the audio length.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Error Status */}
        {transcription.status === 'failed' && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-5 h-5 text-red-500" />
              <div className="text-sm text-red-700 dark:text-red-300">
                <p className="font-medium">Transcription failed</p>
                <p className="text-xs mt-1">
                  {transcription.error_message || 'An error occurred during processing. Please try uploading again.'}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Footer Actions */}
      {transcription.status === 'completed' && (
        <div className="px-4 py-3 bg-gray-50 dark:bg-gray-900 rounded-b-lg border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
              {transcription.word_count && (
                <span>{transcription.word_count.toLocaleString()} words</span>
              )}
              {transcription.confidence_score && (
                <span>{Math.round(transcription.confidence_score * 100)}% confidence</span>
              )}
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDownload('txt')}
                disabled={downloading}
                className="flex items-center space-x-1"
              >
                <Download className="w-3 h-3" />
                <span>Download</span>
              </Button>
              
              <Link to={`/transcriptions/${transcription.id}`}>
                <Button
                  size="sm"
                  className="flex items-center space-x-1"
                >
                  <Eye className="w-3 h-3" />
                  <span>View</span>
                </Button>
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* Click overlay to close dropdown */}
      {showDropdown && (
        <div
          className="fixed inset-0 z-[5]"
          onClick={() => setShowDropdown(false)}
        />
      )}
    </div>
  );
};

export default TranscriptionCard;
