@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #5D5CDE;
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #000000;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

body {
  background-color: #0F0F0F;
  color: white;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1A1A1A;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #FFFFFF;
    background-color: #FFFFFF;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #F5F5F5;
  }
}

#root {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
}

.wave-bg {
  position: relative;
  overflow: hidden;
}

/* Ensure sections take up appropriate space */
section {
  width: 100%;
  position: relative;
}

/* Adjust spacing between sections */
main > section {
  padding: 4rem 0;
}

main > section:first-child {
  padding-top: 0;
}

/* Feature cards */
.feature-card {
  background-color: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* Pricing cards */
.pricing-card {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* Use true black for main sections in dark mode */
section.py-16.bg-white.dark:bg-black {
  /* Add any specific styles for this combination */
}

/* For alternate sections (like in the image) */
section.py-16.bg-gray-50.dark:bg-dark2 {
  /* Add any specific styles for this combination */
}

/* Card backgrounds */
.bg-white.dark:bg-dark3 {
  /* Add any specific styles for this combination */
}

/* Panel backgrounds (like dashboard) */
.bg-gray-50.dark:bg-dark2 {
  /* Add any specific styles for this combination */
}

/* Headings */
h2 {
  font-size: 2em;
  font-weight: bold;
  color: #999999;
}

h2.dark {
  color: #FFFFFF;
}

/* Primary text */
p {
  color: #888888;
}

p.dark {
  color: #FFFFFF;
}

/* Secondary text */
p.text-gray-700 {
  color: #777777;
}

p.text-gray-700.dark {
  color: #FFFFFF;
}

/* Muted text */
span.text-gray-600 {
  color: #666666;
}

span.text-gray-600.dark {
  color: #FFFFFF;
}

/* Buttons */
.bg-primary {
  background-color: var(--primary);
}

.bg-primaryDark {
  background-color: #4A49B8;
}

.hover\:bg-primaryDark:hover {
  background-color: #4A49B8;
}

/* Icons and highlights */
.text-primary {
  color: var(--primary);
}

.fas.fa-check {
  color: var(--primary);
}

/* Accented panels (like in the image) */
.border-l-4.border-primary {
  border-left-color: var(--primary);
}

.bg-primary/5 {
  background-color: rgba(var(--primary), 0.05);
}

.dark\:bg-primary/10 {
  background-color: rgba(var(--primary), 0.1);
}

.p-3 {
  padding: 0.75rem;
}

.rounded-r-lg {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
