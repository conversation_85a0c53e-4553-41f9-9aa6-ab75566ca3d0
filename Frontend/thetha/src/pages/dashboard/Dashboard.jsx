import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Plus, FileText, Clock, CheckCircle, AlertCircle, Search, Filter } from 'lucide-react';
import { transcriptionService } from '../../services/transcriptionService';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import UploadModal from '../../components/transcription/UploadModal';
import TranscriptionCard from '../../components/transcription/TranscriptionCard';
import toast from 'react-hot-toast';

const Dashboard = () => {
  const { user } = useAuth();
  const [transcriptions, setTranscriptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showUploadModal, setShowUploadModal] = useState(false);

  useEffect(() => {
    fetchTranscriptions();
  }, []);

  const fetchTranscriptions = async () => {
    try {
      setLoading(true);
      const data = await transcriptionService.getTranscriptions();
      setTranscriptions(data.results || data);
    } catch (error) {
      console.error('Failed to fetch transcriptions:', error);
      toast.error('Failed to load transcriptions');
    } finally {
      setLoading(false);
    }
  };

  const handleUploadSuccess = (newTranscription) => {
    setTranscriptions(prev => [newTranscription, ...prev]);
    setShowUploadModal(false);
    toast.success('Audio file uploaded successfully!');
  };

  const handleDeleteTranscription = async (id) => {
    try {
      await transcriptionService.deleteTranscription(id);
      setTranscriptions(prev => prev.filter(t => t.id !== id));
      toast.success('Transcription deleted successfully');
    } catch (error) {
      console.error('Failed to delete transcription:', error);
      toast.error('Failed to delete transcription');
    }
  };

  const filteredTranscriptions = transcriptions.filter(transcription => {
    const matchesSearch = transcription.filename?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transcription.transcript?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || transcription.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'processing':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusCount = (status) => {
    return transcriptions.filter(t => t.status === status).length;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6 lg:space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex-1">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-primary via-tertiary to-secondary bg-clip-text text-transparent">
            Welcome back, {user?.first_name || user?.username || 'User'}!
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2 text-sm sm:text-base">
            Manage your audio transcriptions and view your activity
          </p>
          
          {/* Quick Links */}
          <div className="flex flex-wrap gap-3 mt-4">
            <Link 
              to="/transcriptions" 
              className="inline-flex items-center px-3 py-2 text-sm bg-primary/10 text-primary hover:bg-primary/20 rounded-lg transition-colors"
            >
              <FileText className="w-4 h-4 mr-2" />
              View All Transcriptions
            </Link>
            <Link 
              to="/settings" 
              className="inline-flex items-center px-3 py-2 text-sm bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              Account Settings
            </Link>
          </div>
        </div>
        <Button
          onClick={() => setShowUploadModal(true)}
          className="flex items-center space-x-2 w-full sm:w-auto"
          size="lg"
        >
          <Plus className="w-5 h-5" />
          <span>New Transcription</span>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {transcriptions.length}
              </p>
            </div>
            <FileText className="w-8 h-8 text-primary" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Completed</p>
              <p className="text-2xl font-semibold text-green-600">
                {getStatusCount('completed')}
              </p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Processing</p>
              <p className="text-2xl font-semibold text-yellow-600">
                {getStatusCount('processing')}
              </p>
            </div>
            <Clock className="w-8 h-8 text-yellow-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Failed</p>
              <p className="text-2xl font-semibold text-red-600">
                {getStatusCount('failed')}
              </p>
            </div>
            <AlertCircle className="w-8 h-8 text-red-500" />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-4 flex-1">
          <div className="relative flex-1 max-w-md">
            <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Search transcriptions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Filter className="w-5 h-5 text-gray-400" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="completed">Completed</option>
              <option value="processing">Processing</option>
              <option value="failed">Failed</option>
            </select>
          </div>
        </div>
      </div>

      {/* Transcriptions List */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Recent Transcriptions
        </h2>
        
        {filteredTranscriptions.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {transcriptions.length === 0 ? 'No transcriptions yet' : 'No transcriptions match your filters'}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {transcriptions.length === 0 
                ? 'Upload your first audio file to get started with transcription.'
                : 'Try adjusting your search or filter criteria.'
              }
            </p>
            {transcriptions.length === 0 && (
              <Button onClick={() => setShowUploadModal(true)}>
                Upload Audio File
              </Button>
            )}
          </div>
        ) : (
          <div className="grid gap-6">
            {filteredTranscriptions.map((transcription) => (
              <TranscriptionCard
                key={transcription.id}
                transcription={transcription}
                onDelete={handleDeleteTranscription}
              />
            ))}
          </div>
        )}
      </div>

      {/* Upload Modal */}
      {showUploadModal && (
        <UploadModal
          onClose={() => setShowUploadModal(false)}
          onSuccess={handleUploadSuccess}
        />
      )}
    </div>
  );
};

export default Dashboard;
