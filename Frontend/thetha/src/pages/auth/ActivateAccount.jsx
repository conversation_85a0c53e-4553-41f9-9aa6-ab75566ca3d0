import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { authService } from '../../services/authService';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import Button from '../../components/common/Button';

const ActivateAccount = () => {
  const { uid, token } = useParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState('loading'); // loading, success, error
  const [message, setMessage] = useState('');

  useEffect(() => {
    const activateAccount = async () => {
      try {
        if (!uid || !token) {
          setStatus('error');
          setMessage('Invalid activation link. Please check your email and try again.');
          return;
        }

        await authService.verifyEmail(uid, token);
        setStatus('success');
        setMessage('Your account has been successfully activated! You can now log in.');
        toast.success('Account activated successfully!');
      } catch (error) {
        setStatus('error');
        console.error('Activation error:', error);
        
        // Handle different error scenarios
        if (error.response?.status === 400) {
          setMessage('This activation link has already been used or is invalid.');
        } else if (error.response?.status === 403) {
          setMessage('This activation link has expired. Please request a new one.');
        } else {
          setMessage('Failed to activate your account. Please try again or contact support.');
        }
        
        toast.error('Account activation failed');
      }
    };

    activateAccount();
  }, [uid, token]);

  const handleGoToLogin = () => {
    navigate('/login');
  };

  const handleGoToRegister = () => {
    navigate('/register');
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-light dark:bg-dark">
        <div className="max-w-md w-full space-y-8 p-8">
          <div className="text-center">
            <LoadingSpinner size="lg" />
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
              Activating your account...
            </h2>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              Please wait while we verify your email address.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-light dark:bg-dark">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          {status === 'success' ? (
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 dark:bg-green-900">
              <svg
                className="h-6 w-6 text-green-600 dark:text-green-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
          ) : (
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900">
              <svg
                className="h-6 w-6 text-red-600 dark:text-red-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </div>
          )}
          
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
            {status === 'success' ? 'Account Activated!' : 'Activation Failed'}
          </h2>
          
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            {message}
          </p>
        </div>

        <div className="mt-8 space-y-4">
          {status === 'success' ? (
            <Button
              onClick={handleGoToLogin}
              className="w-full"
              variant="primary"
            >
              Go to Login
            </Button>
          ) : (
            <div className="space-y-3">
              <Button
                onClick={handleGoToRegister}
                className="w-full"
                variant="primary"
              >
                Create New Account
              </Button>
              <Button
                onClick={handleGoToLogin}
                className="w-full"
                variant="secondary"
              >
                Try to Login
              </Button>
            </div>
          )}
        </div>

        <div className="text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Need help?{' '}
            <a
              href="mailto:<EMAIL>"
              className="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
            >
              Contact Support
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default ActivateAccount;
