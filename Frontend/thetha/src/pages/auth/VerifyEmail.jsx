import React, { useState, useEffect } from 'react';
import { Link, useSearchParams, useNavigate } from 'react-router-dom';
import { CheckCircle, XCircle, Mail, RefreshCw } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import Button from '../../components/common/Button';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import toast from 'react-hot-toast';

const VerifyEmail = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { verifyEmail, resendVerificationEmail, user } = useAuth();
  
  const [status, setStatus] = useState('verifying'); // 'verifying', 'success', 'error', 'expired'
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [error, setError] = useState('');

  const token = searchParams.get('token');

  useEffect(() => {
    if (token) {
      handleVerification();
    } else {
      setStatus('error');
      setError('Invalid verification link');
    }
  }, [token]);

  const handleVerification = async () => {
    try {
      setLoading(true);
      await verifyEmail(token);
      setStatus('success');
      toast.success('Email verified successfully!');
      
      // Redirect to dashboard after 3 seconds
      setTimeout(() => {
        navigate('/dashboard');
      }, 3000);
    } catch (error) {
      console.error('Email verification error:', error);
      const errorMessage = error.response?.data?.detail || 
                          error.response?.data?.message ||
                          'Email verification failed';
      
      if (errorMessage.includes('expired') || errorMessage.includes('invalid')) {
        setStatus('expired');
      } else {
        setStatus('error');
      }
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleResendVerification = async () => {
    try {
      setResendLoading(true);
      await resendVerificationEmail();
      toast.success('Verification email sent! Please check your inbox.');
    } catch (error) {
      console.error('Resend verification error:', error);
      const errorMessage = error.response?.data?.detail || 
                          error.response?.data?.message ||
                          'Failed to resend verification email';
      toast.error(errorMessage);
    } finally {
      setResendLoading(false);
    }
  };

  const renderContent = () => {
    switch (status) {
      case 'verifying':
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900">
              <LoadingSpinner size="sm" />
            </div>
            <h2 className="mt-6 text-xl font-bold text-gray-900 dark:text-white">
              Verifying your email...
            </h2>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              Please wait while we verify your email address.
            </p>
          </div>
        );

      case 'success':
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 dark:bg-green-900">
              <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <h2 className="mt-6 text-xl font-bold text-gray-900 dark:text-white">
              Email verified successfully!
            </h2>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              Your email address has been verified. You will be redirected to your dashboard shortly.
            </p>
            <div className="mt-6">
              <Link to="/dashboard">
                <Button className="w-full">
                  Go to Dashboard
                </Button>
              </Link>
            </div>
          </div>
        );

      case 'expired':
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 dark:bg-yellow-900">
              <RefreshCw className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <h2 className="mt-6 text-xl font-bold text-gray-900 dark:text-white">
              Verification link expired
            </h2>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              This verification link has expired or is invalid. Please request a new verification email.
            </p>
            <div className="mt-6 space-y-4">
              <Button
                onClick={handleResendVerification}
                disabled={resendLoading}
                className="w-full flex justify-center items-center space-x-2"
              >
                {resendLoading ? (
                  <LoadingSpinner size="sm" color="white" />
                ) : (
                  <>
                    <Mail className="w-4 h-4" />
                    <span>Send new verification email</span>
                  </>
                )}
              </Button>
              <Link to="/login">
                <Button variant="outline" className="w-full">
                  Back to Sign In
                </Button>
              </Link>
            </div>
          </div>
        );

      case 'error':
      default:
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900">
              <XCircle className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
            <h2 className="mt-6 text-xl font-bold text-gray-900 dark:text-white">
              Verification failed
            </h2>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              {error || 'There was an error verifying your email address.'}
            </p>
            <div className="mt-6 space-y-4">
              {user && !user.email_verified && (
                <Button
                  onClick={handleResendVerification}
                  disabled={resendLoading}
                  className="w-full flex justify-center items-center space-x-2"
                >
                  {resendLoading ? (
                    <LoadingSpinner size="sm" color="white" />
                  ) : (
                    <>
                      <Mail className="w-4 h-4" />
                      <span>Resend verification email</span>
                    </>
                  )}
                </Button>
              )}
              <Link to="/login">
                <Button variant="outline" className="w-full">
                  Back to Sign In
                </Button>
              </Link>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">T</span>
            </div>
            <span className="text-2xl font-bold text-gray-900 dark:text-white">Thetha</span>
          </div>
        </div>
        
        <div className="mt-8 bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default VerifyEmail;
