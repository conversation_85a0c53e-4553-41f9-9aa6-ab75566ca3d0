.settings-page {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.settings-container h1 {
  color: #2c3e50;
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.settings-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.settings-section {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.settings-section h2 {
  margin-top: 0;
  color: #2980b9;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.form-help {
  font-size: 0.8rem;
  color: #999;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
}

.info-label {
  font-weight: 500;
  color: #7f8c8d;
}

.info-value {
  font-weight: 500;
  color: #2c3e50;
}

.info-value.status-active {
  color: #27ae60;
}

.info-value.status-inactive {
  color: #e74c3c;
}

.info-value.status-verified {
  color: #3498db;
}

.info-value.status-unverified {
  color: #f39c12;
}

.success-message {
  background-color: #dff0d8;
  color: #3c763d;
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 5px;
}

.error-message {
  background-color: #f2dede;
  color: #a94442;
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 5px;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1.5rem;
  color: #3498db;
}
