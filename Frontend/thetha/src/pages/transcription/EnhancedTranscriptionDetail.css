.transcription-detail {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.transcription-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.header-info h1 {
  margin: 0;
  font-size: 2rem;
  color: #34495e;
}

.status-info {
  display: flex;
  align-items: center;
}

.status-badge {
  display: inline-block;
  padding: 0.2rem 0.5rem;
  border-radius: 8px;
  font-weight: 500;
  color: #fff;
  margin-left: 1rem;
}

.processing-indicator {
  margin-left: 1rem;
  color: #f39c12;
  font-weight: 600;
}

.header-actions .btn {
  margin-left: 0.5rem;
}

.transcription-meta {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  padding: 1rem 0;
  margin-bottom: 1rem;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.meta-item {
  width: 100%;
  max-width: 200px;
  text-align: center;
  padding: 0.5rem;
  border: 1px solid #e1e8ed;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.processing-status {
  background-color: #f9f9f9;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #e1e8ed;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #e1e8ed;
  border-radius: 4px;
  overflow: hidden;
  margin: 1rem 0;
}

.progress-fill {
  height: 100%;
  background-color: #3498db;
  transition: width 0.3s ease;
}

.error-status {
  text-align: center;
  background-color: #f9ebeb;
  padding: 1rem;
  border-radius: 4px;
  color: #e74c3c;
  margin-bottom: 1rem;
}

.segments-container {
  margin-top: 2rem;
}

.segments-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.segment {
  padding: 1rem;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
  background-color: #fff;
  transition: border-color 0.3s ease;
}

.segment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.segment-timestamp {
  font-size: 0.875rem;
  color: #7f8c8d;
}

.segment-actions .btn {
  margin-left: 0.5rem;
}

.segment-content {
  font-size: 1rem;
  color: #34495e;
}

.edit-mode {
  display: flex;
  flex-direction: column;
}

.edit-textarea {
  width: 100%;
  height: auto;
  border: 1px solid #ccd6dd;
  border-radius: 4px;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
}

.edit-actions .btn {
  margin-right: 0.5rem;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.loading-spinner {
  border: 4px solid #f3f3f3;
  border-radius: 50%;
  border-top: 4px solid #3498db;
  width: 40px;
  height: 40px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .header-actions .btn {
    margin-left: 0;
    margin-top: 0.5rem;
  }

  .segments-list {
    gap: 0.75rem;
  }
}
