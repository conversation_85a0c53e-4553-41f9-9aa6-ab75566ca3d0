import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { transcriptionAPI } from '../../services/api';
import toast from 'react-hot-toast';
import './EnhancedTranscriptionDetail.css';

const EnhancedTranscriptionDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [transcription, setTranscription] = useState(null);
  const [segments, setSegments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editingSegment, setEditingSegment] = useState(null);
  const [editText, setEditText] = useState('');
  const [statusPolling, setStatusPolling] = useState(null);

  // Fetch transcription data
  const fetchTranscription = useCallback(async () => {
    try {
      const data = await transcriptionAPI.getStatus(id);
      setTranscription(data);
    } catch (err) {
      setError('Failed to fetch transcription data');
      toast.error('Failed to fetch transcription data');
    }
  }, [id]);

  // Fetch segments data
  const fetchSegments = useCallback(async () => {
    try {
      const data = await transcriptionAPI.getSegments(id);
      setSegments(data);
    } catch (err) {
      console.error('Failed to fetch segments:', err);
    }
  }, [id]);

  // Poll for status updates
  const startStatusPolling = useCallback(() => {
    if (statusPolling) return;

    const interval = setInterval(async () => {
      try {
        const status = await transcriptionAPI.getStatus(id);
        setTranscription(prev => ({ ...prev, ...status }));
        
        if (status.status === 'completed' || status.status === 'failed') {
          clearInterval(interval);
          setStatusPolling(null);
          if (status.status === 'completed') {
            fetchSegments();
          }
        }
      } catch (err) {
        console.error('Status polling error:', err);
      }
    }, 3000);

    setStatusPolling(interval);
  }, [id, statusPolling, fetchSegments]);

  useEffect(() => {
    fetchTranscription();
    fetchSegments();
    setLoading(false);
  }, [fetchTranscription, fetchSegments]);

  useEffect(() => {
    if (transcription && (transcription.status === 'processing' || transcription.status === 'pending')) {
      startStatusPolling();
    }

    return () => {
      if (statusPolling) {
        clearInterval(statusPolling);
      }
    };
  }, [transcription, startStatusPolling, statusPolling]);

  const handleEditSegment = (segment) => {
    setEditingSegment(segment.id);
    setEditText(segment.transcript || '');
  };

  const handleSaveEdit = async () => {
    try {
      await transcriptionAPI.updateSegment(id, editingSegment, { transcript: editText });
      setSegments(prev => prev.map(seg => 
        seg.id === editingSegment 
          ? { ...seg, transcript: editText }
          : seg
      ));
      setEditingSegment(null);
      setEditText('');
      toast.success('Segment updated successfully');
    } catch (err) {
      toast.error('Failed to update segment');
    }
  };

  const handleCancelEdit = () => {
    setEditingSegment(null);
    setEditText('');
  };

  const handleExport = async (format) => {
    try {
      const response = await transcriptionAPI.exportTranscription(id, format);
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `transcription.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      toast.success(`Exported as ${format.toUpperCase()}`);
    } catch (err) {
      toast.error('Failed to export transcription');
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this transcription?')) {
      try {
        await transcriptionAPI.delete(id);
        toast.success('Transcription deleted successfully');
        navigate('/transcriptions');
      } catch (err) {
        toast.error('Failed to delete transcription');
      }
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return '#27ae60';
      case 'processing':
        return '#f39c12';
      case 'failed':
        return '#e74c3c';
      case 'pending':
        return '#95a5a6';
      default:
        return '#95a5a6';
    }
  };

  const formatDuration = (seconds) => {
    if (!seconds) return 'Unknown';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatTimestamp = (timestamp) => {
    const seconds = Math.floor(timestamp);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <div className="transcription-detail-loading">
        <div className="loading-spinner"></div>
        <p>Loading transcription...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="transcription-detail-error">
        <h2>Error</h2>
        <p>{error}</p>
        <button onClick={() => navigate('/transcriptions')} className="btn btn-primary">
          Back to Transcriptions
        </button>
      </div>
    );
  }

  if (!transcription) {
    return (
      <div className="transcription-detail-error">
        <h2>Transcription Not Found</h2>
        <button onClick={() => navigate('/transcriptions')} className="btn btn-primary">
          Back to Transcriptions
        </button>
      </div>
    );
  }

  return (
    <div className="transcription-detail">
      <div className="transcription-header">
        <div className="header-info">
          <h1>{transcription.original_filename || 'Untitled Transcription'}</h1>
          <div className="status-info">
            <span 
              className="status-badge" 
              style={{ backgroundColor: getStatusColor(transcription.status) }}
            >
              {transcription.status}
            </span>
            {transcription.status === 'processing' && (
              <span className="processing-indicator">Processing...</span>
            )}
          </div>
        </div>
        
        <div className="header-actions">
          {transcription.status === 'completed' && (
            <>
              <button 
                className="btn btn-secondary"
                onClick={() => handleExport('txt')}
              >
                Export TXT
              </button>
              <button 
                className="btn btn-secondary"
                onClick={() => handleExport('srt')}
              >
                Export SRT
              </button>
            </>
          )}
          <button 
            className="btn btn-danger"
            onClick={handleDelete}
          >
            Delete
          </button>
        </div>
      </div>

      <div className="transcription-meta">
        <div className="meta-item">
          <span className="meta-label">Duration:</span>
          <span className="meta-value">{formatDuration(transcription.duration)}</span>
        </div>
        <div className="meta-item">
          <span className="meta-label">Created:</span>
          <span className="meta-value">
            {new Date(transcription.created_at).toLocaleString()}
          </span>
        </div>
        {transcription.language && (
          <div className="meta-item">
            <span className="meta-label">Language:</span>
            <span className="meta-value">{transcription.language}</span>
          </div>
        )}
        {transcription.model && (
          <div className="meta-item">
            <span className="meta-label">Model:</span>
            <span className="meta-value">{transcription.model}</span>
          </div>
        )}
      </div>

      {transcription.status === 'processing' || transcription.status === 'pending' ? (
        <div className="processing-status">
          <div className="progress-bar">
            <div 
              className="progress-fill" 
              style={{ width: `${transcription.progress || 0}%` }}
            ></div>
          </div>
          <p>Processing transcription... {transcription.progress || 0}%</p>
          {segments.length > 0 && (
            <p>{segments.length} segments processed so far</p>
          )}
        </div>
      ) : transcription.status === 'failed' ? (
        <div className="error-status">
          <h3>Transcription Failed</h3>
          <p>{transcription.error_message || 'An error occurred during transcription.'}</p>
        </div>
      ) : segments.length > 0 ? (
        <div className="segments-container">
          <h3>Transcription Segments</h3>
          <div className="segments-list">
            {segments.map((segment) => (
              <div key={segment.id} className="segment">
                <div className="segment-header">
                  <span className="segment-timestamp">
                    {formatTimestamp(segment.start_time)} - {formatTimestamp(segment.end_time)}
                  </span>
                  <div className="segment-actions">
                    {editingSegment !== segment.id && (
                      <button 
                        className="btn btn-small btn-secondary"
                        onClick={() => handleEditSegment(segment)}
                      >
                        Edit
                      </button>
                    )}
                  </div>
                </div>
                
                <div className="segment-content">
                  {editingSegment === segment.id ? (
                    <div className="edit-mode">
                      <textarea
                        value={editText}
                        onChange={(e) => setEditText(e.target.value)}
                        className="edit-textarea"
                        rows={3}
                      />
                      <div className="edit-actions">
                        <button 
                          className="btn btn-small btn-primary"
                          onClick={handleSaveEdit}
                        >
                          Save
                        </button>
                        <button 
                          className="btn btn-small btn-secondary"
                          onClick={handleCancelEdit}
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    <p className="segment-text">
                      {segment.transcript || 'No transcript available for this segment.'}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="no-segments">
          <p>No transcription segments available yet.</p>
        </div>
      )}
    </div>
  );
};

export default EnhancedTranscriptionDetail;
