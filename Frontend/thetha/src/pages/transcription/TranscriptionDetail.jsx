import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { 
  ArrowLeft, 
  Download, 
  Share2, 
  Edit, 
  Trash2, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Volume2,
  Languages,
  Calendar,
  FileText,
  Copy,
  Save,
  X,
  Eye,
  EyeOff
} from 'lucide-react';
import { transcriptionService } from '../../services/transcriptionService';
import Button from '../../components/common/Button';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { toast } from 'react-hot-toast';

const TranscriptionDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [transcription, setTranscription] = useState(null);
  const [loading, setLoading] = useState(true);
  const [downloading, setDownloading] = useState(false);
  const [editing, setEditing] = useState(false);
  const [editedTranscript, setEditedTranscript] = useState('');
  const [saving, setSaving] = useState(false);
  const [showTimestamps, setShowTimestamps] = useState(false);

  useEffect(() => {
    fetchTranscription();
  }, [id]);

  const fetchTranscription = async () => {
    try {
      setLoading(true);
      const data = await transcriptionService.getTranscription(id);
      setTranscription(data);
      setEditedTranscript(data.transcript || '');
    } catch (error) {
      console.error('Failed to fetch transcription:', error);
      toast.error('Failed to load transcription');
      navigate('/dashboard');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (format = 'txt') => {
    try {
      setDownloading(true);
      await transcriptionService.downloadTranscription(transcription.id, format);
      toast.success(`Transcription downloaded as ${format.toUpperCase()}`);
    } catch (error) {
      console.error('Download failed:', error);
      toast.error('Failed to download transcription');
    } finally {
      setDownloading(false);
    }
  };

  const handleShare = async () => {
    try {
      const shareData = {
        expires_in: 7 * 24 * 60 * 60, // 7 days
        permissions: ['view']
      };
      const result = await transcriptionService.shareTranscription(transcription.id, shareData);
      
      // Copy share link to clipboard
      await navigator.clipboard.writeText(result.share_url);
      toast.success('Share link copied to clipboard!');
    } catch (error) {
      console.error('Share failed:', error);
      toast.error('Failed to create share link');
    }
  };

  const handleCopyTranscript = async () => {
    try {
      await navigator.clipboard.writeText(transcription.transcript);
      toast.success('Transcript copied to clipboard!');
    } catch (error) {
      console.error('Copy failed:', error);
      toast.error('Failed to copy transcript');
    }
  };

  const handleSaveEdit = async () => {
    try {
      setSaving(true);
      const updatedTranscription = await transcriptionService.updateTranscription(
        transcription.id, 
        { transcript: editedTranscript }
      );
      setTranscription(updatedTranscription);
      setEditing(false);
      toast.success('Transcript updated successfully');
    } catch (error) {
      console.error('Save failed:', error);
      toast.error('Failed to save changes');
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this transcription?')) {
      try {
        await transcriptionService.deleteTranscription(transcription.id);
        toast.success('Transcription deleted successfully');
        navigate('/dashboard');
      } catch (error) {
        console.error('Delete failed:', error);
        toast.error('Failed to delete transcription');
      }
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'processing':
        return <Clock className="w-5 h-5 text-yellow-500 animate-pulse" />;
      case 'failed':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'text-green-700 bg-green-50 dark:text-green-400 dark:bg-green-900/20';
      case 'processing':
        return 'text-yellow-700 bg-yellow-50 dark:text-yellow-400 dark:bg-yellow-900/20';
      case 'failed':
        return 'text-red-700 bg-red-50 dark:text-red-400 dark:bg-red-900/20';
      default:
        return 'text-gray-700 bg-gray-50 dark:text-gray-400 dark:bg-gray-900/20';
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDuration = (seconds) => {
    if (!seconds) return 'Unknown';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getLanguageLabel = (code) => {
    const languages = {
      'en': 'English',
      'es': 'Spanish',
      'fr': 'French',
      'de': 'German',
      'it': 'Italian',
      'pt': 'Portuguese',
      'ru': 'Russian',
      'ja': 'Japanese',
      'ko': 'Korean',
      'zh': 'Chinese',
      'ar': 'Arabic',
      'hi': 'Hindi',
      'auto': 'Auto-detected'
    };
    return languages[code] || code;
  };

  const renderTranscriptWithTimestamps = (transcript, timestamps) => {
    if (!showTimestamps || !timestamps) {
      return <p className="whitespace-pre-wrap leading-relaxed">{transcript}</p>;
    }

    // This is a simplified implementation
    // In a real app, you'd need proper timestamp data structure
    return (
      <div className="space-y-2">
        {transcript.split('\n').map((line, index) => (
          <div key={index} className="flex">
            <span className="text-xs text-gray-500 w-16 flex-shrink-0 mr-4">
              {Math.floor(index * 10 / 60)}:{(index * 10 % 60).toString().padStart(2, '0')}
            </span>
            <span className="flex-1">{line}</span>
          </div>
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" text="Loading transcription..." />
      </div>
    );
  }

  if (!transcription) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Transcription not found
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          The transcription you're looking for doesn't exist or has been deleted.
        </p>
        <Link to="/dashboard">
          <Button>Back to Dashboard</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link to="/dashboard">
            <Button variant="outline" size="sm" className="flex items-center space-x-2">
              <ArrowLeft className="w-4 h-4" />
              <span>Back</span>
            </Button>
          </Link>
          
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {transcription.title || transcription.filename || 'Untitled Transcription'}
            </h1>
            <div className="flex items-center space-x-4 mt-2">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(transcription.status)}`}>
                {getStatusIcon(transcription.status)}
                <span className="ml-2">{transcription.status}</span>
              </span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Created {formatDate(transcription.created_at)}
              </span>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {transcription.status === 'completed' && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyTranscript}
                className="flex items-center space-x-2"
              >
                <Copy className="w-4 h-4" />
                <span>Copy</span>
              </Button>
              
              <div className="relative group">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDownload('txt')}
                  disabled={downloading}
                  className="flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>Download</span>
                </Button>
                
                {/* Dropdown for different formats */}
                <div className="absolute right-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg z-10 border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all">
                  <button
                    onClick={() => handleDownload('txt')}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    Download as TXT
                  </button>
                  <button
                    onClick={() => handleDownload('pdf')}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    Download as PDF
                  </button>
                </div>
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={handleShare}
                className="flex items-center space-x-2"
              >
                <Share2 className="w-4 h-4" />
                <span>Share</span>
              </Button>
            </>
          )}

          <Button
            variant="outline"
            size="sm"
            onClick={handleDelete}
            className="flex items-center space-x-2 text-red-600 hover:text-red-700"
          >
            <Trash2 className="w-4 h-4" />
            <span>Delete</span>
          </Button>
        </div>
      </div>

      {/* Metadata Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {transcription.duration && (
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <Volume2 className="w-5 h-5 text-primary" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Duration</p>
                <p className="font-semibold text-gray-900 dark:text-white">
                  {formatDuration(transcription.duration)}
                </p>
              </div>
            </div>
          </div>
        )}

        {transcription.language && (
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <Languages className="w-5 h-5 text-primary" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Language</p>
                <p className="font-semibold text-gray-900 dark:text-white">
                  {getLanguageLabel(transcription.language)}
                </p>
              </div>
            </div>
          </div>
        )}

        {transcription.word_count && (
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <FileText className="w-5 h-5 text-primary" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Words</p>
                <p className="font-semibold text-gray-900 dark:text-white">
                  {transcription.word_count.toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        )}

        {transcription.confidence_score && (
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <CheckCircle className="w-5 h-5 text-primary" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Confidence</p>
                <p className="font-semibold text-gray-900 dark:text-white">
                  {Math.round(transcription.confidence_score * 100)}%
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Description */}
      {transcription.description && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
            Description
          </h3>
          <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
            {transcription.description}
          </p>
        </div>
      )}

      {/* Transcript */}
      {transcription.status === 'completed' && transcription.transcript && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Transcript
              </h3>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setShowTimestamps(!showTimestamps)}
                  className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                >
                  {showTimestamps ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  <span>{showTimestamps ? 'Hide' : 'Show'} Timestamps</span>
                </button>
                
                {editing ? (
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setEditing(false);
                        setEditedTranscript(transcription.transcript);
                      }}
                      disabled={saving}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleSaveEdit}
                      disabled={saving}
                      className="flex items-center space-x-2"
                    >
                      <Save className="w-4 h-4" />
                      <span>{saving ? 'Saving...' : 'Save'}</span>
                    </Button>
                  </div>
                ) : (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setEditing(true)}
                    className="flex items-center space-x-2"
                  >
                    <Edit className="w-4 h-4" />
                    <span>Edit</span>
                  </Button>
                )}
              </div>
            </div>
          </div>
          
          <div className="p-6">
            {editing ? (
              <textarea
                value={editedTranscript}
                onChange={(e) => setEditedTranscript(e.target.value)}
                className="w-full h-96 p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                placeholder="Enter your transcript here..."
              />
            ) : (
              <div className="text-gray-900 dark:text-white">
                {renderTranscriptWithTimestamps(
                  transcription.transcript, 
                  transcription.timestamps
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Processing Status */}
      {transcription.status === 'processing' && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <Clock className="w-8 h-8 text-yellow-500 animate-pulse" />
            <div>
              <h3 className="text-lg font-medium text-yellow-700 dark:text-yellow-300">
                Processing in Progress
              </h3>
              <p className="text-yellow-600 dark:text-yellow-400 mt-1">
                Your transcription is being processed. This may take a few minutes depending on the audio length and quality.
                You'll receive a notification when it's complete.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Error Status */}
      {transcription.status === 'failed' && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <AlertCircle className="w-8 h-8 text-red-500" />
            <div>
              <h3 className="text-lg font-medium text-red-700 dark:text-red-300">
                Transcription Failed
              </h3>
              <p className="text-red-600 dark:text-red-400 mt-1">
                {transcription.error_message || 
                 'An error occurred during processing. Please try uploading the file again or contact support if the issue persists.'}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TranscriptionDetail;
