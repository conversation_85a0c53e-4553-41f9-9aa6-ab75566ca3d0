import { authAPI } from './api';

export const authService = {
  // Login user
  async login(credentials) {
    try {
      return await authAPI.login(credentials);
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },

  // Register new user
  async register(userData) {
    try {
      return await authAPI.register(userData);
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  },

  // Get current user profile
  async getCurrentUser() {
    try {
      return await authAPI.getCurrentUser();
    } catch (error) {
      console.error('Get current user error:', error);
      throw error;
    }
  },

  // Update user profile
  async updateProfile(userData) {
    try {
      return await authAPI.updateProfile(userData);
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  },

  // Change password
  async changePassword(passwordData) {
    try {
      return await authAPI.changePassword(passwordData);
    } catch (error) {
      console.error('Change password error:', error);
      throw error;
    }
  },

  // Request password reset
  async requestPasswordReset(email) {
    try {
      return await authAPI.requestPasswordReset(email);
    } catch (error) {
      console.error('Password reset request error:', error);
      throw error;
    }
  },

  // Reset password with token
  async resetPassword(token, newPassword) {
    try {
      return await authAPI.confirmPasswordReset({ token, new_password: newPassword });
    } catch (error) {
      console.error('Password reset error:', error);
      throw error;
    }
  },

  // Refresh access token
  async refreshToken(refreshToken) {
    try {
      return await authAPI.refreshToken(refreshToken);
    } catch (error) {
      console.error('Token refresh error:', error);
      throw error;
    }
  },

  // Verify email address
  async verifyEmail(uid, token) {
    try {
      return await authAPI.activateAccount({ uid, token });
    } catch (error) {
      console.error('Email verification error:', error);
      throw error;
    }
  },

  // Resend verification email
  async resendVerificationEmail() {
    try {
      return await authAPI.resendActivation();
    } catch (error) {
      console.error('Resend verification error:', error);
      throw error;
    }
  },

  // Additional methods can be implemented when backend supports them
};

export default authService;
