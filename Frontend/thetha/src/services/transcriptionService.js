import { transcriptionAPI } from './api';

export const transcriptionService = {
  // Get all transcriptions for the authenticated user
  async getTranscriptions() {
    try {
      return await transcriptionAPI.getAll();
    } catch (error) {
      console.error('Error fetching transcriptions:', error);
      throw error;
    }
  },

  // Get a specific transcription by ID
  async getTranscription(id) {
    try {
      return await transcriptionAPI.getResults(id);
    } catch (error) {
      console.error('Error fetching transcription:', error);
      throw error;
    }
  },

  // Upload audio file for transcription
  async uploadAudio(file, metadata = {}) {
    try {
      const formData = new FormData();
      formData.append('audio', file);
      
      // Add optional metadata
      if (metadata.title) {
        formData.append('title', metadata.title);
      }
      if (metadata.description) {
        formData.append('description', metadata.description);
      }
      if (metadata.language) {
        formData.append('language', metadata.language);
      }

      return await transcriptionAPI.upload(formData);
    } catch (error) {
      console.error('Error uploading audio:', error);
      throw error;
    }
  },

  // Delete a transcription
  async deleteTranscription(id) {
    try {
      return await transcriptionAPI.delete(id);
    } catch (error) {
      console.error('Error deleting transcription:', error);
      throw error;
    }
  },

  // Get transcription status
  async getTranscriptionStatus(id) {
    try {
      return await transcriptionAPI.getStatus(id);
    } catch (error) {
      console.error('Error fetching transcription status:', error);
      throw error;
    }
  },

  // Additional methods can be added here when backend supports them
  // For now, we'll use placeholder implementations
  
  async updateTranscription(id, data) {
    // Placeholder - not implemented in current backend
    console.warn('updateTranscription not implemented in backend yet');
    return Promise.resolve({ id, ...data });
  },

  async downloadTranscription(id, format = 'txt') {
    // Placeholder - not implemented in current backend
    console.warn('downloadTranscription not implemented in backend yet');
    return Promise.resolve();
  },

  async shareTranscription(id, shareData) {
    // Placeholder - not implemented in current backend
    console.warn('shareTranscription not implemented in backend yet');
    return Promise.resolve({ share_url: `${window.location.origin}/shared/${id}` });
  }
};

export default transcriptionService;
