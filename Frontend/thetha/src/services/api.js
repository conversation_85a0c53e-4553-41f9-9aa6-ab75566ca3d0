import axios from 'axios';

// In Docker, we use relative URLs that nginx will proxy to the backend
// In development, we use the full backend URL
const API_BASE_URL = import.meta.env.VITE_API_URL || 
  (import.meta.env.VITE_APP_ENVIRONMENT === 'docker' ? '' : 'http://localhost:8000');

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/jwt/refresh/`, {
            refresh: refreshToken,
          });

          const { access } = response.data;
          localStorage.setItem('access_token', access);

          // Retry the original request with new token
          originalRequest.headers.Authorization = `Bearer ${access}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Auth API endpoints
export const authAPI = {
  // User registration
  register: async (userData) => {
    const response = await api.post('/auth/users/', userData);
    return response.data;
  },

  // User login
  login: async (credentials) => {
    const response = await api.post('/auth/jwt/create/', credentials);
    return response.data;
  },

  // Refresh token
  refreshToken: async (refreshToken) => {
    const response = await api.post('/auth/jwt/refresh/', {
      refresh: refreshToken,
    });
    return response.data;
  },

  // Verify token
  verifyToken: async (token) => {
    const response = await api.post('/auth/jwt/verify/', {
      token,
    });
    return response.data;
  },

  // Get current user
  getCurrentUser: async () => {
    const response = await api.get('/auth/users/me/');
    return response.data;
  },

  // Update user profile
  updateProfile: async (userData) => {
    const response = await api.patch('/auth/users/me/', userData);
    return response.data;
  },

  // Change password
  changePassword: async (passwordData) => {
    const response = await api.post('/auth/users/set_password/', passwordData);
    return response.data;
  },

  // Request password reset
  requestPasswordReset: async (email) => {
    const response = await api.post('/auth/users/reset_password/', { email });
    return response.data;
  },

  // Confirm password reset
  confirmPasswordReset: async (data) => {
    const response = await api.post('/auth/users/reset_password_confirm/', data);
    return response.data;
  },

  // Email activation
  activateAccount: async (data) => {
    const response = await api.post('/auth/users/activation/', data);
    return response.data;
  },

  // Resend activation email
  resendActivation: async (email) => {
    const response = await api.post('/auth/users/resend_activation/', { email });
    return response.data;
  },
};

// Transcription API endpoints
export const transcriptionAPI = {
  // Upload audio file
  upload: async (formData) => {
    const response = await api.post('/api/transcribe/upload/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Get transcription status
  getStatus: async (id) => {
    const response = await api.get(`/api/transcribe/${id}/status/`);
    return response.data;
  },

  // Get transcription segments
  getSegments: async (id) => {
    const response = await api.get(`/api/transcribe/${id}/segments/`);
    return response.data;
  },

  // Submit manual transcription for a segment
  submitManualTranscription: async (id, segmentId, transcript) => {
    const response = await api.post(
      `/api/transcribe/${id}/segments/${segmentId}/manual/`,
      { transcript }
    );
    return response.data;
  },

  // Update transcription metadata
  updateTranscription: async (id, data) => {
    const response = await api.patch(`/api/transcribe/${id}/`, data);
    return response.data;
  },

  // Get segment details
  getSegment: async (id, segmentId) => {
    const response = await api.get(`/api/transcribe/${id}/segments/${segmentId}/`);
    return response.data;
  },

  // Update segment transcription
  updateSegment: async (id, segmentId, data) => {
    const response = await api.patch(`/api/transcribe/${id}/segments/${segmentId}/`, data);
    return response.data;
  },

  // Export transcription
  exportTranscription: async (id, format = 'txt') => {
    const response = await api.get(`/api/transcribe/${id}/export/`, {
      params: { format },
      responseType: 'blob'
    });
    return response;
  },

  // Share transcription
  shareTranscription: async (id, shareData) => {
    const response = await api.post(`/api/transcribe/${id}/share/`, shareData);
    return response.data;
  },

  // Get final transcription results
  getResults: async (id) => {
    const response = await api.get(`/api/transcribe/${id}/results/`);
    return response.data;
  },

  // Get all user transcriptions
  getAll: async () => {
    const response = await api.get('/api/transcribe/');
    return response.data;
  },

  // Delete transcription
  delete: async (id) => {
    const response = await api.delete(`/api/transcribe/${id}/`);
    return response.data;
  },
};

// Test endpoints (no authentication required)
export const testAPI = {
  upload: async (formData) => {
    const response = await axios.post(`${API_BASE_URL}/api/transcribe/test/upload/`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  getStatus: async (id) => {
    const response = await axios.get(`${API_BASE_URL}/api/transcribe/test/${id}/status/`);
    return response.data;
  },

  getResults: async (id) => {
    const response = await axios.get(`${API_BASE_URL}/api/transcribe/test/${id}/results/`);
    return response.data;
  },
};

export default api;
