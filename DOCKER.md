# Docker Setup for Thetha

This document explains how to use Docker with the Thetha project.

## Architecture Overview

Thetha uses a streamlined Docker setup with three main services:

1. **thetha-app**: All-in-one backend container
   - Django REST API server
   - Redis server (internal)
   - Celery workers and beat scheduler
   - Static file serving

2. **thetha-frontend**: React frontend
   - Vite-built React application
   - Nginx web server
   - Production-optimized build

3. **thetha-postgres**: Database
   - PostgreSQL 16 with health checks
   - Persistent data storage

## Quick Start

To start all services:

```bash
docker-compose up -d
```

This will:
1. Build all necessary Docker images
2. Start all services in detached mode
3. Set up the database with migrations
4. Collect and serve static files
5. Start the API server, Celery workers, and frontend

## Available Services

After starting the containers, you can access:

- **Frontend (React)**: http://localhost:5173
- **Backend API**: http://localhost:8000
- **API Documentation (Swagger)**: http://localhost:8000/swagger/
- **Admin Panel**: http://localhost:8000/admin/
- **Health Check**: http://localhost:8000/health/

## Development Workflow

1. Make changes to the code
2. Run `docker-compose up --build` to rebuild and restart the services
3. Access the services at the URLs above

## Troubleshooting

If you encounter any issues:

1. Check the logs: `docker-compose logs -f [service_name]`
2. Restart a specific service: `docker-compose restart [service_name]`
3. Rebuild a specific service: `docker-compose up -d --build [service_name]`
4. Reset everything: `docker-compose down -v && docker-compose up --build`

## Notes for Docker Desktop Users

If you're using Docker Desktop:

1. Make sure you have enough resources allocated (at least 4GB RAM)
2. Enable file sharing for the project directory
3. Use WSL2 backend for better performance on Windows
