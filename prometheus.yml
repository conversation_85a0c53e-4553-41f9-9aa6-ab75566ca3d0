global:
  scrape_interval: 15s
  scrape_timeout: 10s
  evaluation_interval: 15s

scrape_configs:
  - job_name: "django"
    honor_timestamps: true
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ["backend:8000"]
  - job_name: "react"
    honor_timestamps: true
    scheme: http
    static_configs:
      - targets: ["frontend:5173"]

    relabel_configs:
      - source_labels: [__address__]
        regex: "([^:]+):\\d+"
        target_label: instance
