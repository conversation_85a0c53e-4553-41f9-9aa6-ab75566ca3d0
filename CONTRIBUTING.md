# Contributing to <PERSON><PERSON>

Thank you for your interest in contributing to Thetha! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites
- <PERSON><PERSON> and <PERSON>er Compose
- Git
- Basic knowledge of Django/React (depending on your contribution area)

### Development Setup
1. Fork the repository on GitHub
2. Clone your fork locally:
   ```bash
   git clone https://github.com/YOUR_USERNAME/Thetha.git
   cd Thetha
   ```
3. Copy the environment file:
   ```bash
   cp .env.example .env
   ```
4. Start the development environment:
   ```bash
   docker-compose up -d
   ```

## 🎯 How to Contribute

### 1. Choose an Issue
- Browse [GitHub Issues](https://github.com/yourusername/Thetha/issues)
- Look for issues labeled `good first issue` for beginners
- Comment on the issue to let others know you're working on it

### 2. Create a Feature Branch
```bash
git checkout -b feature/your-feature-name
# or
git checkout -b fix/your-bug-fix
```

### 3. Make Your Changes
- Write clean, well-documented code
- Follow the existing code style
- Add tests for new functionality
- Update documentation as needed

### 4. Test Your Changes
```bash
# Test backend changes
docker-compose exec thetha python manage.py test

# Test frontend changes
docker-compose exec frontend npm test

# Check if all services still work
curl http://localhost:8000/health/
curl -I http://localhost:5173
```

### 5. Submit a Pull Request
- Push your changes to your fork
- Create a pull request with a clear description
- Link to any related issues
- Wait for code review and address feedback

## 📝 Code Style Guidelines

### Backend (Django)
- Follow PEP 8 guidelines
- Use meaningful variable and function names
- Add docstrings to classes and functions
- Use type hints where appropriate
- Keep functions small and focused

### Frontend (React)
- Use functional components with hooks
- Follow ESLint rules
- Use meaningful component and variable names
- Add PropTypes or TypeScript types
- Keep components small and reusable

### General
- Write clear commit messages
- Use present tense ("Add feature" not "Added feature")
- Keep commits atomic (one logical change per commit)
- Reference issues in commit messages when applicable

## 🧪 Testing

### Backend Testing
```bash
# Run all tests
docker-compose exec thetha python manage.py test

# Run specific app tests
docker-compose exec thetha python manage.py test transcriber

# Run with coverage
docker-compose exec thetha coverage run manage.py test
docker-compose exec thetha coverage report
```

### Frontend Testing
```bash
# Run tests
docker-compose exec frontend npm test

# Run with coverage
docker-compose exec frontend npm run test:coverage
```

## 📚 Documentation

- Update README.md if you add new features
- Update API documentation for backend changes
- Add inline comments for complex logic
- Update STATUS.md if you fix major issues

## 🐛 Bug Reports

When reporting bugs, please include:
- Steps to reproduce the issue
- Expected vs actual behavior
- Your environment (OS, Docker version, etc.)
- Relevant logs or error messages
- Screenshots if applicable

## 💡 Feature Requests

For new features:
- Describe the problem you're trying to solve
- Explain your proposed solution
- Consider alternatives you've explored
- Discuss the impact on existing users

## 🔍 Code Review Process

1. All submissions require review before merging
2. Reviewers will check for:
   - Code quality and style
   - Test coverage
   - Documentation updates
   - Accessibility considerations
   - Performance implications

3. Address reviewer feedback promptly
4. Be open to suggestions and improvements

## 🌍 Accessibility Guidelines

Thetha is committed to accessibility:
- Follow WCAG 2.1 AA guidelines
- Test with screen readers when possible
- Ensure keyboard navigation works
- Use semantic HTML elements
- Provide alt text for images
- Maintain good color contrast

## 📞 Getting Help

- **GitHub Discussions**: For questions and general discussion
- **Issues**: For bug reports and feature requests
- **Documentation**: Check existing READMEs and docs

## 🏆 Recognition

Contributors will be:
- Listed in the project README
- Credited in release notes
- Invited to join the contributors team (for regular contributors)

## 📄 License

By contributing, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to Thetha and helping make technology more accessible and inclusive! 🌟
