# Thetha Performance Optimization Guide

## Overview

This document outlines the comprehensive performance optimizations implemented in the Thetha codebase to improve scalability, reduce memory usage, and enhance overall system performance.

## Key Optimizations Implemented

### 1. Backend Performance Optimizations

#### Database Optimizations
- **Connection Pooling**: Implemented database connection pooling with `CONN_MAX_AGE = 600`
- **Query Optimization**: Added `select_related()` and `prefetch_related()` for efficient database queries
- **Index Optimization**: Proper indexing on frequently queried fields
- **Connection Management**: Automatic cleanup of database connections in Celery tasks

#### Caching Strategy
- **Redis Integration**: Implemented Redis-based caching with compression
- **Session Optimization**: Sessions stored in Redis cache instead of database
- **Model Caching**: Whisper model caching to avoid repeated loading
- **Cache Compression**: ZLIB compression for cached data

#### Memory Management
- **Garbage Collection**: Explicit garbage collection in memory-intensive operations
- **Context Managers**: Memory-efficient loading with automatic cleanup
- **Resource Cleanup**: Proper cleanup of audio processing resources
- **GPU Memory Management**: CUDA memory optimization for GPU-enabled systems

### 2. Audio Processing Optimizations

#### Chunked Processing
- **Large File Handling**: Process large audio files in 60-second chunks
- **Memory-Efficient Loading**: Context managers for audio loading with automatic cleanup
- **Streaming Processing**: Avoid loading entire files into memory simultaneously

#### Segmentation Improvements
- **Smart Segmentation**: Skip very short segments (< 1 second)
- **Optimized Segment Creation**: Efficient segment numbering and metadata handling
- **Memory Cleanup**: Explicit cleanup after each segment processing

### 3. Whisper Model Optimizations

#### Model Caching
- **Global Model Cache**: Reuse loaded models across requests
- **Thread-Safe Caching**: Thread-safe model cache with locks
- **Memory Limits**: Limit cached models to prevent memory exhaustion
- **Automatic Cleanup**: Clean up old models after inactivity

#### Inference Optimizations
- **FP16 Precision**: Use half-precision for GPU inference
- **Batch Processing**: Optimized batch processing for segments
- **Temperature Control**: Use temperature=0.0 for deterministic results
- **GPU Optimization**: CUDA optimizations and memory management

### 4. Celery Task Optimizations

#### Task Configuration
- **Prefetch Multiplier**: Set to 1 for memory-intensive tasks
- **Acks Late**: Enable late acknowledgment for reliability
- **Task Compression**: GZIP compression for task payloads
- **Retry Strategy**: Exponential backoff for retries

#### Queue Management
- **Multiple Queues**: Separate queues for different task types
- **Priority Handling**: Priority-based task execution
- **Resource Limits**: Docker resource limits for Celery workers
- **Health Monitoring**: Regular health checks and metrics

### 5. Docker and Infrastructure Optimizations

#### Resource Management
- **Memory Limits**: Appropriate memory limits for each service
- **CPU Allocation**: Optimized CPU allocation based on workload
- **Volume Optimization**: Efficient volume mounting for temporary files

#### Redis Optimizations
- **Memory Policy**: LRU eviction policy for Redis
- **Persistence**: Optimized save intervals
- **Connection Management**: TCP keepalive and timeout settings

## Performance Monitoring

### Metrics Implemented
- **Request Duration**: HTTP request processing time
- **Queue Size**: Real-time queue monitoring
- **File Size Tracking**: Monitor file sizes being processed
- **Error Rates**: Track processing failures and retries
- **Memory Usage**: System and GPU memory monitoring

### Health Checks
- **Database Connectivity**: Regular database health checks
- **Redis Connectivity**: Cache system health monitoring
- **Disk Space**: Monitor available disk space
- **Service Health**: Container health checks with proper timeouts

## Usage Guide

### Performance Management Command

Run optimization tasks using the management command:

```bash
# Run all optimizations
python manage.py optimize_performance --all

# Individual optimization tasks
python manage.py optimize_performance --cleanup-temp-files
python manage.py optimize_performance --cleanup-models
python manage.py optimize_performance --optimize-db
python manage.py optimize_performance --clear-cache
python manage.py optimize_performance --memory-report
```

### Environment Variables for Tuning

```bash
# Audio processing limits
AUDIO_PROCESSING_MAX_WORKERS=2
MAX_SEGMENT_DURATION=30
MAX_AUDIO_DURATION=1800
AUDIO_CHUNK_SIZE_MB=5

# Celery optimizations
CELERY_WORKER_CONCURRENCY=2
CELERY_WORKER_PREFETCH_MULTIPLIER=1
CELERY_TASK_ACKS_LATE=true
CELERY_WORKER_MAX_TASKS_PER_CHILD=1000

# Whisper model selection
WHISPER_MODEL=small  # Options: tiny, base, small, medium, large
```

### Monitoring and Alerting

#### Grafana Dashboards
- **System Overview**: CPU, memory, and disk usage
- **Transcription Metrics**: Processing times and queue sizes
- **Error Monitoring**: Failed tasks and error rates
- **Redis Performance**: Cache hit rates and memory usage

#### Prometheus Alerts
- **High Memory Usage**: Alert when memory usage exceeds 80%
- **Long Queue Times**: Alert when transcription queue is backed up
- **High Error Rates**: Alert when error rates exceed threshold
- **Disk Space**: Alert when disk space is low

## Performance Tuning Recommendations

### Hardware Requirements

#### Minimum Requirements
- **CPU**: 4 cores
- **RAM**: 8GB
- **Storage**: 50GB SSD
- **Network**: 100 Mbps

#### Recommended for Production
- **CPU**: 8+ cores
- **RAM**: 16GB+ (32GB for large workloads)
- **GPU**: NVIDIA GPU with 6GB+ VRAM (optional but recommended)
- **Storage**: 200GB+ NVMe SSD
- **Network**: 1 Gbps

### Scaling Strategies

#### Horizontal Scaling
- **Multiple Celery Workers**: Scale workers based on workload
- **Load Balancer**: Use nginx for load balancing
- **Database Read Replicas**: Scale read operations
- **Redis Clustering**: Scale cache layer

#### Vertical Scaling
- **Increase Memory**: For larger audio files
- **Add GPU**: For faster transcription
- **Faster Storage**: NVMe for temporary file operations

### Configuration Tuning

#### For High-Volume Workloads
```bash
CELERY_WORKER_CONCURRENCY=4
AUDIO_PROCESSING_MAX_WORKERS=4
MAX_SEGMENT_DURATION=20
CELERY_WORKER_MAX_TASKS_PER_CHILD=500
```

#### For Memory-Constrained Environments
```bash
CELERY_WORKER_CONCURRENCY=1
AUDIO_PROCESSING_MAX_WORKERS=1
MAX_SEGMENT_DURATION=45
WHISPER_MODEL=tiny
```

#### For GPU-Enabled Systems
```bash
WHISPER_MODEL=medium  # or large
CELERY_WORKER_CONCURRENCY=2
# Ensure CUDA is available in containers
```

## Troubleshooting Performance Issues

### Common Issues and Solutions

#### High Memory Usage
1. **Reduce worker concurrency**: Lower `CELERY_WORKER_CONCURRENCY`
2. **Use smaller model**: Switch to `tiny` or `base` Whisper model
3. **Increase cleanup frequency**: Run cleanup tasks more often
4. **Monitor for memory leaks**: Use memory profiling tools

#### Slow Transcription
1. **Check GPU availability**: Ensure GPU is properly configured
2. **Optimize segment size**: Adjust `MAX_SEGMENT_DURATION`
3. **Increase worker count**: Add more Celery workers
4. **Check disk I/O**: Ensure fast storage for temporary files

#### Queue Backup
1. **Scale workers**: Add more Celery worker instances
2. **Optimize task priority**: Use priority queues
3. **Check resource limits**: Ensure adequate CPU/memory
4. **Monitor error rates**: Fix failing tasks

### Performance Profiling

#### Memory Profiling
```bash
# Install memory profiler
pip install memory-profiler

# Profile memory usage
python -m memory_profiler manage.py optimize_performance --memory-report
```

#### CPU Profiling
```bash
# Use cProfile for CPU profiling
python -m cProfile -o profile.stats manage.py process_audio_file <file_id>
```

#### Database Query Analysis
```python
# Enable query logging in settings
LOGGING = {
    'loggers': {
        'django.db.backends': {
            'level': 'DEBUG',
            'handlers': ['console'],
        }
    }
}
```

## Maintenance Tasks

### Regular Maintenance
- **Daily**: Run cleanup tasks to remove old temporary files
- **Weekly**: Clear unused cache entries and optimize database
- **Monthly**: Review performance metrics and adjust configurations
- **Quarterly**: Update dependencies and review resource allocation

### Automated Maintenance
Set up cron jobs or scheduled tasks for regular maintenance:

```bash
# Daily cleanup (add to crontab)
0 2 * * * /path/to/venv/bin/python /path/to/manage.py optimize_performance --cleanup-temp-files

# Weekly optimization
0 3 * * 0 /path/to/venv/bin/python /path/to/manage.py optimize_performance --all
```

## Performance Metrics Baseline

### Expected Performance (with optimizations)
- **Small audio file (< 1 MB)**: 2-5 seconds processing time
- **Medium audio file (1-10 MB)**: 10-30 seconds processing time
- **Large audio file (10-50 MB)**: 1-5 minutes processing time
- **Memory usage**: < 2GB for typical workloads
- **Queue processing**: < 100ms average task pickup time

### Monitoring Thresholds
- **Memory usage**: Alert at 80% of available memory
- **Queue depth**: Alert when > 50 pending tasks
- **Processing time**: Alert when > 2x expected time
- **Error rate**: Alert when > 5% of tasks fail

## Future Optimization Opportunities

### Potential Improvements
1. **Model Quantization**: Use quantized models for faster inference
2. **Streaming Transcription**: Real-time streaming audio processing
3. **Distributed Processing**: Multi-node processing for large workloads
4. **Advanced Caching**: Intelligent caching based on audio fingerprints
5. **Auto-scaling**: Dynamic scaling based on queue depth and system load

### Research Areas
- **Custom Models**: Fine-tuned models for specific languages/domains
- **Edge Computing**: Process audio closer to the source
- **WebAssembly**: Client-side processing for small files
- **Neural Compression**: Advanced audio compression for storage
