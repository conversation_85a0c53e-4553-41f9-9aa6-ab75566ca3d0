from .views import log_user_activity


class UserActivityMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)

        if request.user.is_authenticated:
            activity_type = f'{request.method} {request.path}'
            ip_address = request.META.get('REMOTE_ADDR')
            log_user_activity(request.user, activity_type, ip_address)

        return response
