from django.contrib import admin
from django.contrib.auth.admin import UserAdmin

from .models import User, UserActivity


class CustomUserAdmin(UserAdmin):
    model = User
    list_display = ('email', 'username', 'full_name', 'is_staff', 'is_active',
                    'wallet')
    list_filter = ('is_staff', 'is_active')
    fieldsets = (
        (None, {
            'fields': ('email', 'username', 'password')
        }),
        ('Personal Info', {
            'fields': ('full_name', 'phone_number', 'bio')
        }),
        ('Permissions', {
            'fields': ('is_staff', 'is_active', 'groups', 'user_permissions')
        }),
        ('Important dates', {
            'fields': ('last_login', 'date_joined')
        }),
        ('Wallet', {
            'fields': ('wallet', )
        }),
    )
    add_fieldsets = ((None, {
        'classes': ('wide', ),
        'fields': ('email', 'username', 'password1', 'password2', 'is_staff',
                   'is_active')
    }), )
    search_fields = ('email', 'username', 'full_name')
    ordering = ('email', )


admin.site.register(User, CustomUserAdmin)
admin.site.register(UserActivity)
