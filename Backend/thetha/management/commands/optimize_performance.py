"""
Django management command for performance optimization tasks.
"""
import gc
import os
import time

from django.conf import settings
from django.core.cache import cache
from django.core.management.base import BaseCommand
from django.db import connections
from transcriber.models import AudioFile, AudioSegment
from transcriber.utils.whisper_integration import cleanup_old_models


class Command(BaseCommand):
    help_text = 'Run performance optimization tasks'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cleanup-temp-files',
            action='store_true',
            help='Clean up temporary audio files',
        )
        parser.add_argument(
            '--cleanup-models',
            action='store_true',
            help='Clean up cached Whisper models',
        )
        parser.add_argument(
            '--optimize-db',
            action='store_true',
            help='Optimize database queries',
        )
        parser.add_argument(
            '--clear-cache',
            action='store_true',
            help='Clear Redis cache',
        )
        parser.add_argument(
            '--memory-report',
            action='store_true',
            help='Generate memory usage report',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Run all optimization tasks',
        )

    def handle(self, *args, **options):
        start_time = time.time()

        if options['all']:
            options.update({
                'cleanup_temp_files': True,
                'cleanup_models': True,
                'optimize_db': True,
                'clear_cache': True,
                'memory_report': True,
            })

        self.stdout.write(
            self.style.SUCCESS('Starting performance optimization tasks...'))

        if options['cleanup_temp_files']:
            self.cleanup_temp_files()

        if options['cleanup_models']:
            self.cleanup_models()

        if options['optimize_db']:
            self.optimize_database()

        if options['clear_cache']:
            self.clear_cache()

        if options['memory_report']:
            self.generate_memory_report()

        total_time = time.time() - start_time
        self.stdout.write(
            self.style.SUCCESS(
                f'Performance optimization completed in {total_time:.2f}s'))

    def cleanup_temp_files(self):
        """Clean up temporary audio files."""
        self.stdout.write('Cleaning up temporary files...')

        # Get segments with file paths
        segments = AudioSegment.objects.filter(
            file_path__isnull=False).exclude(file_path='')

        cleaned_files = 0
        freed_space = 0
        current_time = time.time()
        max_age = 24 * 60 * 60  # 24 hours in seconds

        for segment in segments:
            try:
                file_path = segment.file_path
                if os.path.exists(file_path):
                    mtime = os.path.getmtime(file_path)
                    if current_time - mtime > max_age:
                        file_size = os.path.getsize(file_path)
                        os.remove(file_path)
                        segment.file_path = None
                        segment.save(update_fields=['file_path'])
                        cleaned_files += 1
                        freed_space += file_size
                else:
                    # File doesn't exist, clear the path
                    if segment.file_path:
                        segment.file_path = None
                        segment.save(update_fields=['file_path'])
            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(
                        f'Error cleaning file {file_path}: {e}'))

        freed_mb = freed_space / (1024 * 1024)
        self.stdout.write(
            self.style.SUCCESS(
                f'Cleaned {cleaned_files} files, freed {freed_mb:.2f}MB'))

    def cleanup_models(self):
        """Clean up cached Whisper models."""
        self.stdout.write('Cleaning up cached models...')
        try:
            cleanup_old_models(
                max_age_minutes=5)  # Clean models older than 5 minutes
            self.stdout.write(self.style.SUCCESS('Model cleanup completed'))
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error cleaning up models: {e}'))

    def optimize_database(self):
        """Optimize database connections and queries."""
        self.stdout.write('Optimizing database...')

        # Close old connections
        connections.close_all()

        # Analyze database statistics
        try:
            # Count various objects
            total_files = AudioFile.objects.count()
            pending_files = AudioFile.objects.filter(status='pending').count()
            processing_files = AudioFile.objects.filter(
                status='processing').count()
            completed_files = AudioFile.objects.filter(
                status='completed').count()
            failed_files = AudioFile.objects.filter(status='failed').count()

            total_segments = AudioSegment.objects.count()
            pending_segments = AudioSegment.objects.filter(
                status='pending').count()

            self.stdout.write(
                f'Database stats:\n'
                f'  Audio files: {total_files} total, {pending_files} pending, '
                f'{processing_files} processing, {completed_files} completed, {failed_files} failed\n'
                f'  Segments: {total_segments} total, {pending_segments} pending'
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error analyzing database: {e}'))

    def clear_cache(self):
        """Clear Redis cache."""
        self.stdout.write('Clearing cache...')
        try:
            cache.clear()
            self.stdout.write(self.style.SUCCESS('Cache cleared'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error clearing cache: {e}'))

    def generate_memory_report(self):
        """Generate memory usage report."""
        self.stdout.write('Generating memory report...')

        try:
            import psutil
            import torch

            # System memory
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()

            self.stdout.write(
                f'System Memory:\n'
                f'  Total: {memory.total / (1024**3):.2f}GB\n'
                f'  Available: {memory.available / (1024**3):.2f}GB\n'
                f'  Used: {memory.percent:.1f}%\n'
                f'  Swap: {swap.percent:.1f}% used')

            # GPU memory if available
            if torch.cuda.is_available():
                gpu_memory = torch.cuda.get_device_properties(0).total_memory
                gpu_allocated = torch.cuda.memory_allocated(0)
                gpu_cached = torch.cuda.memory_reserved(0)

                self.stdout.write(
                    f'GPU Memory:\n'
                    f'  Total: {gpu_memory / (1024**3):.2f}GB\n'
                    f'  Allocated: {gpu_allocated / (1024**3):.2f}GB\n'
                    f'  Cached: {gpu_cached / (1024**3):.2f}GB')

            # Disk usage for media directory
            if hasattr(settings, 'MEDIA_ROOT') and os.path.exists(
                    settings.MEDIA_ROOT):
                import shutil
                total, used, free = shutil.disk_usage(settings.MEDIA_ROOT)
                self.stdout.write(f'Disk Usage (Media):\n'
                                  f'  Total: {total / (1024**3):.2f}GB\n'
                                  f'  Used: {used / (1024**3):.2f}GB\n'
                                  f'  Free: {free / (1024**3):.2f}GB')

            # Force garbage collection
            collected = gc.collect()
            self.stdout.write(f'Garbage collected: {collected} objects')

        except ImportError:
            self.stdout.write(
                self.style.WARNING(
                    'psutil not available, limited memory report'))
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error generating memory report: {e}'))
