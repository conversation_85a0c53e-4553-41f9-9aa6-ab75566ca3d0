import time

from django.urls import Resolver404, resolve
from prometheus_client import Counter, Histogram

# Define metrics
http_requests_total = Counter('http_requests_total', 'Total HTTP requests',
                              ['method', 'endpoint', 'status'])

http_request_duration_seconds = Histogram('http_request_duration_seconds',
                                          'HTTP request duration',
                                          ['method', 'endpoint'])

db_query_duration_seconds = Histogram('db_query_duration_seconds',
                                      'Database query duration',
                                      ['query_type'])

error_requests_total = Counter('error_requests_total', 'Total error requests',
                               ['method', 'endpoint', 'status'])


class PrometheusMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        start_time = time.time()

        # Get the endpoint name from the URL pattern
        try:
            endpoint = resolve(request.path_info).url_name or request.path_info
        except Resolver404:
            endpoint = request.path_info
        endpoint = request.path_info

        response = self.get_response(request)

        # Record request duration
        duration = time.time() - start_time
        http_request_duration_seconds.labels(
            method=request.method, endpoint=endpoint).observe(duration)

        # Count total requests
        http_requests_total.labels(method=request.method,
                                   endpoint=endpoint,
                                   status=response.status_code).inc()

        # Count error requests
        if 400 <= response.status_code < 600:
            error_requests_total.labels(method=request.method,
                                        endpoint=endpoint,
                                        status=response.status_code).inc()

        return response


class ContentSecurityPolicyMiddleware:
    """Middleware to add Content Security Policy headers."""
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)

        # Add CSP header to allow necessary resources
        response['Content-Security-Policy'] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com; "
            "style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; "
            "img-src 'self' data:; "
            "font-src 'self' https://cdnjs.cloudflare.com; "
            "connect-src 'self' http://localhost:8000; "
            "frame-src 'self'; "
            "object-src 'none'; "
            "base-uri 'self';")

        return response


class DisableCSRFMiddleware:
    """Middleware to disable CSRF protection for testing."""
    def __init__(self, get_response):
        self.get_response = get_response

    def process_request(self, request):
        # Disable CSRF protection
        request._dont_enforce_csrf_checks = True
        return None

    def __call__(self, request):
        self.process_request(request)
        return self.get_response(request)


class AudioProcessingErrorMiddleware:
    """Middleware to handle audio processing errors gracefully."""
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        return response

    def process_exception(self, request, exception):
        """Process exceptions related to audio processing."""
        import logging
        from django.http import JsonResponse
        
        logger = logging.getLogger(__name__)
        
        # Handle specific audio processing errors
        if 'transcribe' in request.path:
            if isinstance(exception, (FileNotFoundError, ValueError)):
                logger.error(f"Audio processing error: {exception}")
                return JsonResponse({
                    'error': 'Audio processing failed',
                    'detail': str(exception),
                    'code': 'AUDIO_PROCESSING_ERROR'
                }, status=400)
            
            elif isinstance(exception, MemoryError):
                logger.error(f"Memory error during audio processing: {exception}")
                return JsonResponse({
                    'error': 'Insufficient memory for audio processing',
                    'detail': 'The audio file is too large to process. Please try a smaller file.',
                    'code': 'MEMORY_ERROR'
                }, status=413)
        
        # Return None to let Django handle other exceptions normally
        return None
