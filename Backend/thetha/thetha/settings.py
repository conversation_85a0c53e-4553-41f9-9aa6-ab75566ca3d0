"""
Django settings for thetha project.

Generated by 'django-admin startproject' using Django 4.2.11.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""
import os
from datetime import timed<PERSON><PERSON>
from pathlib import Path
from dotenv import load_dotenv


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Load environment variables from .env file
load_dotenv()


SECRET_KEY = os.getenv('DJANGO_SECRET_KEY')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.getenv('DEBUG')

ALLOWED_HOSTS = os.getenv('ALLOWED_HOSTS', '').split(',')

# Application definition
INSTALLED_APPS = [
	"jazzmin",
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
	"django.contrib.humanize",
	"rest_framework",
	"drf_yasg",
	"django_prometheus",
	"accounts",
	"djoser",
	"corsheaders",
	"django_celery_results",
	"transcriber",
	"thetha",
]


MIDDLEWARE = [
	"django_prometheus.middleware.PrometheusBeforeMiddleware",
    "thetha.middleware.PrometheusMiddleware",
    "thetha.middleware.ContentSecurityPolicyMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
	"django_prometheus.middleware.PrometheusAfterMiddleware",
]

ROOT_URLCONF = "thetha.urls"
THETHA_TEMPLATES = os.path.join(BASE_DIR, 'thetha', 'templates')


TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [THETHA_TEMPLATES],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "thetha.wsgi.application"

# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    "default": {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('POSTGRES_DB'),
        'USER': os.environ.get('POSTGRES_USER'),
        'PASSWORD': os.environ.get('POSTGRES_PASSWORD'),
        'HOST': 'localhost',
        'PORT': os.environ.get('DB_PORT', '5432'),
        'CONN_MAX_AGE': 600,  # Connection pooling
        'OPTIONS': {
            'MAX_CONNS': 20,
            'MIN_CONNS': 5,
        },
        'TEST': {
            'NAME': 'test_thetha_db',
        },
    }
}

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME":
        "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME":
        "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME":
        "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
	{
		'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
	},
	{
		'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
		'OPTIONS': {
			"min_length": 8,
		}
	},
	{
		"NAME": "accounts.validators.UppercaseValidator",
	},
	{
		"NAME": "accounts.validators.LowercaseValidator",
	},
	{
		"NAME": "accounts.validators.SymbolValidator",
	},
]

# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "Africa/Harare"

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

# settings.py
STATIC_URL = "/static/"
STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")
STATICFILES_DIRS = [BASE_DIR / "thetha/static", ]
STORAGES = {
    "default": {
        "BACKEND": "django.core.files.storage.FileSystemStorage",
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
}





# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"


# Jazzmin Customizations
JAZZMIN_HEADER = "Jazzmin Header"

# Jazzmin UI Tweaks
JAZZMIN_UI_TWEAKS = {
	"navbar_small_text": False,
	"footer_small_text": False,
	"body_small_text": False,
	"brand_small_text": False,
	"brand_colour": "navbar-dark",
	"accent": "#BEADFA",          # Custom primary color
	"navbar": "navbar-dark",
	"no_navbar_border": False,
	"navbar_fixed": False,
	"layout_boxed": False,
	"footer_fixed": False,
	"sidebar_fixed": False,
	"sidebar": "sidebar-dark",
	"sidebar_nav_small_text": False,
	"sidebar_disable_expand": False,
	"sidebar_nav_child_indent": False,
	"sidebar_nav_compact_style": False,
	"sidebar_nav_legacy_style": False,
	"sidebar_nav_flat_style": False,
	"theme": "default",           # Use default theme to simplify customizations
	"dark_mode_theme": None,
	"button_classes": {
		"primary": "btn-primary",
		"secondary": "btn-secondary",
		"info": "btn-info",
		"warning": "btn-warning",
		"danger": "btn-danger",
		"success": "btn-success",
	},
}

# Jazzmin Settings
JAZZMIN_SETTINGS = {
	"site_title": "Thetha Admin",                 # Title of your site
	"site_header": "Admin",                      # Header for your admin interface
	"welcome_message": "Welcome to the Admin Interface",  # Welcome message in the header
	"site_brand": "Thetha",
	"welcome_sign": "Welcome to the Admin Dashboard",
	"copyright": "ThethaHub",
	"search_model": "auth.User",
	"site_logo": "img/thetha_logo.png",          # Custom logo
	# Removed custom CSS and JS
	# Added custom colors to the palette
	"primary_color": "#BEADFA",
	"secondary_color": "#D0BFFF",
	"accent": "#DFCCFB",
}


USE_TZ = True

STATIC_URL = "static/"

# Django REST Framework settings
# Check if we're in testing mode
import sys
import os
TESTING = len(sys.argv) > 1 and sys.argv[1] == 'test' or os.environ.get('DISABLE_AUTH', '').lower() == 'true'

# If DISABLE_AUTH is true, add DisableCSRFMiddleware to the middleware list
if TESTING and 'thetha.middleware.DisableCSRFMiddleware' not in MIDDLEWARE:
    MIDDLEWARE.append('thetha.middleware.DisableCSRFMiddleware')
    # Also remove the CSRF middleware if it exists
    if 'django.middleware.csrf.CsrfViewMiddleware' in MIDDLEWARE:
        MIDDLEWARE.remove('django.middleware.csrf.CsrfViewMiddleware')

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ) if not TESTING else [],
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
    ) if not TESTING else ['rest_framework.permissions.AllowAny'],
    'DEFAULT_RENDERER_CLASSES': (
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ),
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle'
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '100/day',
        'user': '1000/day'
    }
}

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Custom user model
AUTH_USER_MODEL = "accounts.User"

# Rest Framework settings
REST_FRAMEWORK = {
	"DEFAULT_AUTHENTICATION_CLASSES": (
		"rest_framework_simplejwt.authentication.JWTAuthentication",
	),
	"DEFAULT_PERMISSION_CLASSES": [
		"rest_framework.permissions.IsAuthenticated",
	],
	"DEFAULT_THROTTLE_CLASSES": [
		"rest_framework.throttling.AnonRateThrottle",
		"rest_framework.throttling.UserRateThrottle"
	],
	"DEFAULT_THROTTLE_RATES": {
		"anon": "100/day",
		"user": "1000/day"
	}
}

# JWT settings
SIMPLE_JWT = {
	"ACCESS_TOKEN_LIFETIME": timedelta(minutes=15),
	"REFRESH_TOKEN_LIFETIME": timedelta(days=7),
	"ROTATE_REFRESH_TOKENS": True,
	"BLACKLIST_AFTER_ROTATION": True,
	"UPDATE_LAST_LOGIN": True,
	"ALGORITHM": "HS256",
	"SIGNING_KEY": SECRET_KEY,
	"VERIFYING_KEY": None,
	"AUTH_HEADER_TYPES": ("Bearer",),
	"USER_ID_FIELD": "id",
	"USER_ID_CLAIM": "user_id",
	"AUTH_TOKEN_CLASSES": ("rest_framework_simplejwt.tokens.AccessToken",),
	"TOKEN_TYPE_CLAIM": "token_type",
	"JTI_CLAIM": "jti",
	"SLIDING_TOKEN_REFRESH_EXP_CLAIM": "refresh_exp",
	"SLIDING_TOKEN_LIFETIME": timedelta(minutes=5),
	"SLIDING_TOKEN_REFRESH_LIFETIME": timedelta(days=1),
}

# Djoser settings
DJOSER = {
	"LOGIN_FIELD": "email",
	"USER_CREATE_PASSWORD_RETYPE": True,
	"USERNAME_CHANGED_EMAIL_CONFIRMATION": True,
	"PASSWORD_CHANGED_EMAIL_CONFIRMATION": True,
	"SEND_CONFIRMATION_EMAIL": True,
	"SET_USERNAME_RETYPE": True,
	"SET_PASSWORD_RETYPE": True,
	"PASSWORD_RESET_CONFIRM_URL": "password/reset/confirm/{uid}/{token}",
	"USERNAME_RESET_CONFIRM_URL": "email/reset/confirm/{uid}/{token}",
	"ACTIVATION_URL": "activate/{uid}/{token}",
	"SEND_ACTIVATION_EMAIL": True,
	"SERIALIZERS": {
		"user_create": "accounts.serializers.UserCreateSerializer",
		"user": "accounts.serializers.UserSerializer",
		"current_user": "accounts.serializers.UserSerializer",
		"user_delete": "djoser.serializers.UserDeleteSerializer",
	},
}

# Email settings (replace with your SMTP settings)
EMAIL_BACKEND = os.getenv("EMAIL_BACKEND")
EMAIL_HOST = os.getenv("EMAIL_HOST")
EMAIL_PORT = int(os.getenv("EMAIL_PORT", 587))
EMAIL_HOST_USER = os.getenv("EMAIL_HOST_USER")
EMAIL_HOST_PASSWORD = os.getenv("EMAIL_HOST_PASSWORD")
EMAIL_USE_TLS = os.getenv("EMAIL_USE_TLS", "True") == "True"

# CORS settings
CORS_ALLOW_ALL_ORIGINS = True

# Swagger settings
SWAGGER_SETTINGS = {
	'SECURITY_DEFINITIONS': {
		'Bearer': {
			'type': 'apiKey',
			'name': 'Authorization',
			'in': 'header'
		}
	}
}

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'mediafiles')

# Celery settings
CELERY_BROKER_URL = f"redis://{os.environ.get('REDIS_HOST', 'localhost')}:{os.environ.get('REDIS_PORT', '6373')}/0"
CELERY_RESULT_BACKEND = 'django-db'
CELERY_ACCEPT_CONTENT = ['application/json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE

# Celery optimization settings
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_TASK_ACKS_LATE = True
CELERY_WORKER_MAX_TASKS_PER_CHILD = 1000
CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP = True
CELERY_TASK_COMPRESSION = 'gzip'
CELERY_RESULT_COMPRESSION = 'gzip'

# Audio processing specific settings
CELERY_TASK_ROUTES = {
    'transcriber.tasks.process_audio_file': {'queue': 'audio_processing'},
    'transcriber.tasks.transcribe_segment': {'queue': 'transcription'},
    'transcriber.tasks.cleanup_temp_files': {'queue': 'cleanup'},
}

# Maximum file upload size (10MB)
MAX_UPLOAD_SIZE = 10 * 1024 * 1024

# Supported audio formats
SUPPORTED_AUDIO_FORMATS = ['audio/mpeg', 'audio/wav', 'audio/x-wav', 'audio/flac', 'audio/x-flac', 'audio/ogg', 'audio/x-m4a', 'audio/mp4']

# Whisper model settings
WHISPER_MODEL = 'small'  # Options: tiny, base, small, medium, large

# Audio processing optimization settings
AUDIO_PROCESSING_MAX_WORKERS = os.environ.get('AUDIO_PROCESSING_MAX_WORKERS', 2)
AUDIO_CHUNK_SIZE_MB = 5  # Size in MB for audio chunking
MAX_SEGMENT_DURATION = int(os.environ.get('MAX_SEGMENT_DURATION', 30))  # seconds
MAX_AUDIO_DURATION = int(os.environ.get('MAX_AUDIO_DURATION', 1800))  # 30 minutes

# Caching configuration
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': f"redis://{os.environ.get('REDIS_HOST', 'localhost')}:{os.environ.get('REDIS_PORT', '6373')}/1",
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 50,
                'retry_on_timeout': True,
            },
            'SERIALIZER': 'django_redis.serializers.json.JSONSerializer',
            'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
        },
        'KEY_PREFIX': 'thetha',
        'TIMEOUT': 300,  # 5 minutes default
    }
}

# Session optimization
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'
SESSION_COOKIE_AGE = 86400  # 24 hours

# Logging optimization
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/django.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'transcriber': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}
