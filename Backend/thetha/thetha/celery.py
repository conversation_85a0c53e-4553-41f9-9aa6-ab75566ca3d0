"""Celery configuration for the Thetha project."""
import os

# Set the default Django settings module for the 'celery' program.
from celery import Celery

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'thetha.settings')

app = Celery('thetha')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django app configs.
app.autodiscover_tasks()


@app.task(bind=True, ignore_result=True)
def debug_task(self):
    """Debug task to verify <PERSON><PERSON>y is working."""
    print(f'Request: {self.request!r}')
