"""Health check views for the Thetha project."""
import os

import redis  # type: ignore
from django.db import connection
from django.http import JsonResponse
from django.views.decorators.cache import never_cache
from django.views.decorators.http import require_GET


@require_GET
@never_cache
def health_check(request):
    """Check the health of the application and its dependencies.

    Simple health check endpoint that verifies:
    1. The Django application is running
    2. Database connection is working
    3. Redis connection is working

    Returns:
        JsonResponse: Status of the application and its dependencies
    """
    # Check database connection
    db_status = 'ok'
    try:
        with connection.cursor() as cursor:
            cursor.execute('SELECT 1')
            cursor.fetchone()
    except Exception as e:
        db_status = f'error: {str(e)}'

    # Check Redis connection
    redis_status = 'ok'
    try:
        redis_host = os.environ.get('REDIS_HOST', 'localhost')
        redis_port = int(os.environ.get('REDIS_PORT', 6379))
        r = redis.Redis(host=redis_host, port=redis_port)
        r.ping()
    except Exception as e:
        redis_status = f'error: {str(e)}'

    # Overall status
    status = 'healthy' if db_status == 'ok' and redis_status == 'ok' else 'unhealthy'

    return JsonResponse({
        'status': status,
        'database': db_status,
        'redis': redis_status,
        'version': '1.0.0'
    })
