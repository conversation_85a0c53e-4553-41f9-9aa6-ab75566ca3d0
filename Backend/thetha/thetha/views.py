"""
Views for authentication and main pages.
"""
from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.forms import UserCreationForm
from django.contrib import messages
from django.views.decorators.csrf import csrf_protect
from django.contrib.auth.decorators import login_required
from django.contrib.auth import get_user_model
from django import forms


# Get the custom User model
User = get_user_model()

class CustomUserCreationForm(UserCreationForm):
    """Custom user creation form with additional fields."""
    email = forms.EmailField(required=True)
    full_name = forms.CharField(max_length=100, required=False, help_text="Optional. Your full name.")
    phone_number = forms.CharField(max_length=15, required=False, help_text="Optional. Your phone number.")

    class Meta:
        model = User
        fields = ("username", "email", "full_name", "phone_number", "password1", "password2")

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data["email"]
        user.full_name = self.cleaned_data["full_name"]
        user.phone_number = self.cleaned_data["phone_number"]
        if commit:
            user.save()
        return user


def home(request):
    """Home page view."""
    return render(request, 'home.html')


@csrf_protect
def login_view(request):
    """Handle user login."""
    if request.user.is_authenticated:
        return redirect('transcriber-web')
    
    if request.method == 'POST':
        email = request.POST.get('email')
        password = request.POST.get('password')
        
        if email and password:
            user = authenticate(request, username=email, password=password)
            if user is not None:
                login(request, user)
                next_url = request.GET.get('next', 'transcriber-web')
                return redirect(next_url)
            else:
                messages.error(request, 'Invalid email or password.')
        else:
            messages.error(request, 'Please provide both email and password.')
    
    return render(request, 'auth/login.html')


@csrf_protect
def register_view(request):
    """Handle user registration."""
    if request.user.is_authenticated:
        return redirect('transcriber-web')
    
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            email = form.cleaned_data.get('email')
            messages.success(request, f'Account created for {email}! You can now log in with your email address.')
            return redirect('login')
        else:
            # Form errors will be displayed in the template
            pass
    else:
        form = CustomUserCreationForm()
    
    return render(request, 'auth/register.html', {'form': form})


def logout_view(request):
    """Handle user logout."""
    logout(request)
    messages.success(request, 'You have been logged out successfully.')
    return redirect('home')


@login_required
def profile_view(request):
    """Display user profile."""
    return render(request, 'auth/profile.html')
