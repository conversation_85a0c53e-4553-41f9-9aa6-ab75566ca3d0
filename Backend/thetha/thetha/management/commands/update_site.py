import os
from django.core.management.base import BaseCommand
from django.contrib.sites.models import Site


class Command(BaseCommand):
    help = 'Update site domain and name for email links'

    def handle(self, *args, **options):
        try:
            frontend_url = os.getenv('FRONTEND_URL', 'http://localhost:3000')
            domain = frontend_url.replace('http://', '').replace('https://', '')
            
            # Get or create the site
            site, created = Site.objects.get_or_create(
                pk=1,
                defaults={
                    'domain': domain,
                    'name': 'Thetha'
                }
            )
            
            # Update the site if it already existed
            if not created:
                site.domain = domain
                site.name = 'Thetha'
                site.save()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully updated site domain to: {domain}'
                )
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error updating site: {str(e)}')
            )
