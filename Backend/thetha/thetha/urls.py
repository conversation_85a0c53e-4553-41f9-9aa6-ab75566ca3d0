"""URL configuration for thetha project."""
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path, re_path
from django.views.static import serve
from drf_yasg import openapi
from drf_yasg.views import get_schema_view
from rest_framework import permissions

from .health import health_check
from .views import home, login_view, register_view, logout_view, profile_view
from transcriber.views import transcriber_web_view

schema_view = get_schema_view(
    openapi.Info(
        title='Thetha',
        default_version='v1',
        description='Backend Endpoints documentation',
        terms_of_service='https://www.google.com/policies/terms/',
        contact=openapi.Contact(email='<EMAIL>'),
        license=openapi.License(name='BSD License'),
    ),
    public=True,
    permission_classes=[permissions.AllowAny],
)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('auth/', include('djoser.urls')),
    path('auth/', include('djoser.urls.jwt')),
    path('api/transcribe/', include('transcriber.urls')),
    path('swagger/',
         schema_view.with_ui('swagger', cache_timeout=0),
         name='schema-swagger-ui'),
    path('redoc/',
         schema_view.with_ui('redoc', cache_timeout=0),
         name='schema-redoc'),
    path('health/', health_check, name='health_check'),
    
    # Transcriber web interface (outside API namespace to avoid authentication)
    path('transcriber/web/', transcriber_web_view, name='transcriber-web'),
    
    # Authentication URLs
    path('', home, name='home'),
    path('login/', login_view, name='login'),
    path('register/', register_view, name='register'),
    path('logout/', logout_view, name='logout'),
    path('profile/', profile_view, name='profile'),
    
    path('', include('django_prometheus.urls')),
]

# Always serve static and media files (even in production for this setup)
urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# Add explicit static file serving
urlpatterns += [
    re_path(r'^static/(?P<path>.*)$', serve,
            {'document_root': settings.STATIC_ROOT}),
    re_path(r'^media/(?P<path>.*)$', serve,
            {'document_root': settings.MEDIA_ROOT}),
]
