{% extends 'base.html' %}

{% block title %}Login | Thetha{% endblock %}

{% block content %}
<div class="flex items-center justify-center min-h-screen bg-gray-100">
    <div class="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
        <h2 class="text-2xl font-semibold text-center text-gray-700">Login to Thetha</h2>
        <form method="post" class="mt-8 space-y-6">
            {% csrf_token %}
            
            {% if messages %}
                {% for message in messages %}
                    <div class="p-4 rounded-md bg-red-50 border border-red-200">
                        <p class="text-red-800">{{ message }}</p>
                    </div>
                {% endfor %}
            {% endif %}
            <div class="rounded-md shadow-sm -space-y-px">
                <div>
                    <label for="email" class="sr-only">Email</label>
                    <input id="email" name="email" type="email" autocomplete="email" required class="input-field" placeholder="Email address">
                </div>
                <div>
                    <label for="password" class="sr-only">Password</label>
                    <input id="password" name="password" type="password" autocomplete="current-password" required class="input-field" placeholder="Password">
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="text-sm">
                    <a href="#" class="font-medium text-blue-600 hover:text-blue-500">Forgot your password?</a>
                </div>
            </div>

            <div>
                <button type="submit" class="btn-primary">
                    Login
                </button>
            </div>
        </form>
        <p class="mt-2 text-center text-sm text-gray-600">
            Don't have an account? <a href="{% url 'register' %}" class="text-blue-600 hover:text-blue-500">Sign up</a>
        </p>
    </div>
</div>
{% endblock %}

