{% extends 'base.html' %}

{% block title %}Sign Up | Thetha{% endblock %}

{% block content %}
<div class="flex items-center justify-center min-h-screen bg-gray-100">
    <div class="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
        <h2 class="text-2xl font-semibold text-center text-gray-700">Create Your Account</h2>
        <form method="post" class="mt-8 space-y-6">
            {% csrf_token %}
            
            {% if messages %}
                {% for message in messages %}
                    <div class="p-4 rounded-md bg-red-50 border border-red-200">
                        <p class="text-red-800">{{ message }}</p>
                    </div>
                {% endfor %}
            {% endif %}

            <div class="space-y-4">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700">Username</label>
                    <input id="username" name="username" type="text" autocomplete="username" required 
                           class="input-field" placeholder="Choose a username" value="{{ form.username.value|default:'' }}">
                    {% if form.username.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.username.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                    <input id="email" name="email" type="email" autocomplete="email" required 
                           class="input-field" placeholder="<EMAIL>" value="{{ form.email.value|default:'' }}">
                    {% if form.email.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.email.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="full_name" class="block text-sm font-medium text-gray-700">Full Name</label>
                    <input id="full_name" name="full_name" type="text" autocomplete="name" 
                           class="input-field" placeholder="Your full name (optional)" value="{{ form.full_name.value|default:'' }}">
                    {% if form.full_name.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.full_name.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="phone_number" class="block text-sm font-medium text-gray-700">Phone Number</label>
                    <input id="phone_number" name="phone_number" type="tel" autocomplete="tel" 
                           class="input-field" placeholder="Phone number (optional)" value="{{ form.phone_number.value|default:'' }}">
                    {% if form.phone_number.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.phone_number.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="password1" class="block text-sm font-medium text-gray-700">Password</label>
                    <input id="password1" name="password1" type="password" autocomplete="new-password" required 
                           class="input-field" placeholder="Choose a secure password">
                    {% if form.password1.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.password1.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="password2" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                    <input id="password2" name="password2" type="password" autocomplete="new-password" required 
                           class="input-field" placeholder="Confirm your password">
                    {% if form.password2.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.password2.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>

            <div>
                <button type="submit" class="btn-primary w-full">
                    Create Account
                </button>
            </div>
        </form>
        
        <p class="mt-4 text-center text-sm text-gray-600">
            Already have an account? <a href="{% url 'login' %}" class="text-blue-600 hover:text-blue-500">Sign in</a>
        </p>
    </div>
</div>
{% endblock %}
