{% load custom_filters %}
{% if fieldset.name %}
<div class="card card-default {% if 'collapse' in fieldset.classes %}collapsed-card{% endif %}">
    <div class="card-header">
        <h3 class="card-title">
            {% if 'collapse' in fieldset.classes %}
                <span class="float-right">
                    <button type="button" class="btn btn-box-tool" data-card-widget="collapse">
                        <i class="fas fa-plus"></i>
                    </button>
                </span>
            {% endif %}
            {{ fieldset.name }}
        </h3>
    </div>
    {% if fieldset.description %}
    <div class="card-body card-help">
        <div class="card-title">
            {{ fieldset.description }}
        </div>
    </div>
    {% endif %}

    <div class="p-5 card-body">
{% else %}
    <div class="p-5">
{% endif %}

    {% for line in fieldset %}
    <div class="form-group{% if line.fields|length_is:'1' and line.errors %} errors{% endif %}{% if not line.has_visible_field %} hidden{% endif %}{% for field in line %}{% if field.field.name %} field-{{ field.field.name }}{% endif %}{% endfor %}">
        <div class="row">
            {% for field in line %}
                <label class="{% if not line.fields|length_is:'1' and forloop.counter != 1 %}col-auto {% else %}col-sm-3 {% endif %}text-left" for="id_{{ field.field.name }}">
                    {{ field.field.label|capfirst }}
                    {% if field.field.field.required %}
                    <span class="text-red">* </span>
                    {% endif %}
                </label>
                <div class="{% if not line.fields|length_is:'1' %} col-auto  fieldBox {% else %} col-sm-7 {% endif %}
                             {% if field.field.name %} field-{{ field.field.name }}{% endif %}
                             {% if field.field.errors %} errors{% endif %}
                             {% if field.field.field.required %} required{% endif %}">
                    {{ field.field }}
                    {% if field.field.field.help_text %}
                        <div class="help">
                            <i class="fas fa-question-circle"></i>
                            {{ field.field.field.help_text|safe }}
                        </div>
                    {% endif %}
                    {% if field.field.errors %}
                        {% for error in field.field.errors %}
                            <p class="text-red">{{ error }}</p>
                        {% endfor %}
                    {% endif %}
                </div>
            {% endfor %}
        </div>
    </div>
    {% endfor %}

    </div>
{% if fieldset.name %}
</div>
{% endif %}
