{% load custom_filters %}
<div class="form-group{% if line.fields|length_is:'1' and line.errors %} errors{% endif %}{% if not line.has_visible_field %} hidden{% endif %}{% for field in line %}{% if field.field.name %} field-{{ field.field.name }}{% endif %}{% endfor %}">
    <div class="row">
        {% for field in line %}
            <label class="{% if not line.fields|length_is:'1' and forloop.counter != 1 %}col-auto {% else %}col-sm-3 {% endif %}text-left" for="id_{{ field.field.name }}">
                {{ field.field.label|capfirst }}
                {% if field.field.field.required %}
                <span class="text-red">* </span>
                {% endif %}
            </label>
            <div class="{% if not line.fields|length_is:'1' %} col-auto  fieldBox {% else %} col-sm-7 {% endif %}
                         {% if field.field.name %} field-{{ field.field.name }}{% endif %}
                         {% if field.field.errors %} errors{% endif %}
                         {% if field.field.field.required %} required{% endif %}">
                {{ field.field }}
                {% if field.field.field.help_text %}
                    <div class="help">
                        <i class="fas fa-question-circle"></i>
                        {{ field.field.field.help_text|safe }}
                    </div>
                {% endif %}
                {% if field.field.errors %}
                    {% for error in field.field.errors %}
                        <p class="text-red">{{ error }}</p>
                    {% endfor %}
                {% endif %}
            </div>
        {% endfor %}
    </div>
</div>
