{% load custom_filters %}
<div class="form-group {% if field.errors %}error{% endif %} row">
    <label class="{% if field|length_is:'1' %}col-sm-3{% else %}col-sm-2{% endif %} text-left" for="{{ field.id_for_label }}">
        {{ field.label|capfirst }}
        {% if field.field.required %}
            <span class="text-red">* </span>
        {% endif %}
    </label>
    <div class="{% if field|length_is:'1' %}col-sm-7{% else %}col-sm-10{% endif %} field-{{ field.name }}">
        {{ field }}
        {% if field.help_text %}
            <div class="help">
                <i class="fas fa-question-circle"></i>
                {{ field.help_text|safe }}
            </div>
        {% endif %}
        {% if field.errors %}
            {% for error in field.errors %}
                <p class="text-red">{{ error }}</p>
            {% endfor %}
        {% endif %}
    </div>
</div>
