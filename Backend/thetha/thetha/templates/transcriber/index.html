<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thetha Transcriber</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }
        .transcription-container {
            margin-top: 2rem;
        }
        .segment {
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 0.25rem;
        }
        .segment-pending {
            background-color: #f8f9fa;
        }
        .segment-processing {
            background-color: #e2f0fd;
        }
        .segment-completed {
            background-color: #d4edda;
        }
        .segment-failed {
            background-color: #f8d7da;
        }
        .segment-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        .segment-time {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .progress {
            height: 1.5rem;
        }
        #uploadForm {
            margin-bottom: 2rem;
        }
        #transcriptionResults {
            white-space: pre-wrap;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: 0.25rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Thetha Transcriber</h1>

        <div class="card mb-4">
            <div class="card-header">
                <h2 class="h5 mb-0">Upload Audio File</h2>
            </div>
            <div class="card-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="title" class="form-label">Title</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                    <div class="mb-3">
                        <label for="audioFile" class="form-label">Audio File</label>
                        <input type="file" class="form-control" id="audioFile" name="file" accept="audio/*" required>
                        <div class="form-text">Supported formats: MP3, WAV, FLAC, OGG, M4A. Maximum file size: 10MB.</div>
                    </div>
                    <button type="submit" class="btn btn-primary">Upload and Transcribe</button>
                </form>
            </div>
        </div>

        <div id="transcriptionStatus" class="card mb-4 d-none">
            <div class="card-header">
                <h2 class="h5 mb-0">Transcription Status</h2>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h3 id="audioTitle" class="h6"></h3>
                    <div class="progress mb-2">
                        <div id="progressBar" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <small id="statusText">Pending</small>
                        <small id="segmentStats"></small>
                    </div>
                </div>
                <button id="refreshStatus" class="btn btn-sm btn-outline-secondary">Refresh Status</button>
                <button id="viewResults" class="btn btn-sm btn-success d-none">View Results</button>
            </div>
        </div>

        <div id="segmentsContainer" class="card mb-4 d-none">
            <div class="card-header">
                <h2 class="h5 mb-0">Segments</h2>
            </div>
            <div class="card-body">
                <div id="segments"></div>
            </div>
        </div>

        <div id="resultsContainer" class="card mb-4 d-none">
            <div class="card-header">
                <h2 class="h5 mb-0">Transcription Results</h2>
            </div>
            <div class="card-body">
                <div id="transcriptionResults"></div>
                <button id="copyTranscript" class="btn btn-sm btn-outline-primary mt-3">Copy to Clipboard</button>
                <a id="downloadTranscript" class="btn btn-sm btn-outline-secondary mt-3">Download as Text</a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let currentAudioId = null;
        let authToken = null;

        // Get auth token from localStorage or prompt user
        function getAuthToken() {
            const token = localStorage.getItem('authToken');
            if (token) {
                return token;
            }

            const newToken = prompt('Please enter your authentication token:');
            if (newToken) {
                localStorage.setItem('authToken', newToken);
                return newToken;
            }

            return null;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            authToken = getAuthToken();

            // Upload form submission
            document.getElementById('uploadForm').addEventListener('submit', function(e) {
                e.preventDefault();
                uploadAudio();
            });

            // Refresh status button
            document.getElementById('refreshStatus').addEventListener('click', function() {
                if (currentAudioId) {
                    getTranscriptionStatus(currentAudioId);
                }
            });

            // View results button
            document.getElementById('viewResults').addEventListener('click', function() {
                if (currentAudioId) {
                    getTranscriptionResults(currentAudioId);
                }
            });

            // Copy transcript button
            document.getElementById('copyTranscript').addEventListener('click', function() {
                const transcript = document.getElementById('transcriptionResults').textContent;
                navigator.clipboard.writeText(transcript)
                    .then(() => alert('Transcript copied to clipboard!'))
                    .catch(err => console.error('Failed to copy: ', err));
            });

            // Download transcript button
            document.getElementById('downloadTranscript').addEventListener('click', function() {
                const transcript = document.getElementById('transcriptionResults').textContent;
                const blob = new Blob([transcript], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const a = document.getElementById('downloadTranscript');
                a.href = url;
                a.download = 'transcript.txt';
            });
        });

        // Upload audio file
        function uploadAudio() {
            const formData = new FormData(document.getElementById('uploadForm'));

            fetch('/api/transcribe/upload/', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authToken}`
                },
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                console.log('Upload successful:', data);
                currentAudioId = data.id;

                // Show status container
                document.getElementById('transcriptionStatus').classList.remove('d-none');
                document.getElementById('audioTitle').textContent = data.title;

                // Start polling for status
                getTranscriptionStatus(data.id);

                // Reset form
                document.getElementById('uploadForm').reset();
            })
            .catch(error => {
                console.error('Error uploading audio:', error);
                alert('Error uploading audio: ' + error.message);
            });
        }

        // Get transcription status
        function getTranscriptionStatus(audioId) {
            fetch(`/api/transcribe/${audioId}/status/`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                console.log('Status:', data);

                // Update progress bar
                const progressBar = document.getElementById('progressBar');
                progressBar.style.width = `${data.progress_percentage}%`;
                progressBar.textContent = `${data.progress_percentage}%`;
                progressBar.setAttribute('aria-valuenow', data.progress_percentage);

                // Update status text
                document.getElementById('statusText').textContent = data.status;

                // Update segment stats
                document.getElementById('segmentStats').textContent =
                    `Completed: ${data.completed_segments} | Failed: ${data.failed_segments} | Pending: ${data.pending_segments} | Processing: ${data.processing_segments} | Total: ${data.total_segments}`;

                // Show view results button if completed
                if (data.status === 'completed') {
                    document.getElementById('viewResults').classList.remove('d-none');
                } else {
                    document.getElementById('viewResults').classList.add('d-none');

                    // Poll again in 5 seconds if not completed
                    setTimeout(() => getTranscriptionStatus(audioId), 5000);
                }

                // Get segments
                getSegments(audioId);
            })
            .catch(error => {
                console.error('Error getting status:', error);
                alert('Error getting transcription status: ' + error.message);
            });
        }

        // Get segments
        function getSegments(audioId) {
            fetch(`/api/transcribe/${audioId}/segments/`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                console.log('Segments:', data);

                // Show segments container
                document.getElementById('segmentsContainer').classList.remove('d-none');

                // Clear segments
                const segmentsContainer = document.getElementById('segments');
                segmentsContainer.innerHTML = '';

                // Add segments
                data.forEach(segment => {
                    const segmentEl = document.createElement('div');
                    segmentEl.className = `segment segment-${segment.status}`;

                    const header = document.createElement('div');
                    header.className = 'segment-header';

                    const title = document.createElement('strong');
                    title.textContent = `Segment ${segment.id}`;

                    const time = document.createElement('span');
                    time.className = 'segment-time';
                    time.textContent = `${formatTime(segment.start_time)} - ${formatTime(segment.end_time)}`;

                    header.appendChild(title);
                    header.appendChild(time);

                    const status = document.createElement('div');
                    status.className = 'mb-2';
                    status.textContent = `Status: ${segment.status}`;

                    const content = document.createElement('div');

                    if (segment.transcript) {
                        content.textContent = segment.transcript.text;
                    } else if (segment.status === 'failed') {
                        // Create manual transcription form for failed segments
                        const form = document.createElement('form');
                        form.className = 'manual-transcription-form';
                        form.dataset.segmentId = segment.segment_id;

                        const textarea = document.createElement('textarea');
                        textarea.className = 'form-control mb-2';
                        textarea.name = 'text';
                        textarea.placeholder = 'Enter manual transcription here...';
                        textarea.required = true;

                        const submitBtn = document.createElement('button');
                        submitBtn.type = 'submit';
                        submitBtn.className = 'btn btn-sm btn-primary';
                        submitBtn.textContent = 'Submit Manual Transcription';

                        form.appendChild(textarea);
                        form.appendChild(submitBtn);

                        form.addEventListener('submit', function(e) {
                            e.preventDefault();
                            submitManualTranscription(audioId, segment.segment_id, textarea.value);
                        });

                        content.appendChild(form);
                    } else {
                        content.textContent = 'Waiting for transcription...';
                    }

                    segmentEl.appendChild(header);
                    segmentEl.appendChild(status);
                    segmentEl.appendChild(content);

                    segmentsContainer.appendChild(segmentEl);
                });
            })
            .catch(error => {
                console.error('Error getting segments:', error);
            });
        }

        // Submit manual transcription
        function submitManualTranscription(audioId, segmentId, text) {
            fetch(`/api/transcribe/${audioId}/segments/${segmentId}/manual/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ text })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                console.log('Manual transcription submitted:', data);
                alert('Manual transcription submitted successfully!');

                // Refresh status and segments
                getTranscriptionStatus(audioId);
            })
            .catch(error => {
                console.error('Error submitting manual transcription:', error);
                alert('Error submitting manual transcription: ' + error.message);
            });
        }

        // Get transcription results
        function getTranscriptionResults(audioId) {
            fetch(`/api/transcribe/${audioId}/results/`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                console.log('Results:', data);

                // Show results container
                document.getElementById('resultsContainer').classList.remove('d-none');

                // Update results
                document.getElementById('transcriptionResults').textContent = data.full_transcript;
            })
            .catch(error => {
                console.error('Error getting results:', error);
                alert('Error getting transcription results: ' + error.message);
            });
        }

        // Format time (seconds to MM:SS)
        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
    </script>
</body>
</html>
