asgiref==3.8.1
certifi==2024.12.14
cffi>=1.15.0
cfgv==3.4.0
charset-normalizer==3.4.1
cryptography==44.0.0
defusedxml==0.8.0rc2
distlib==0.3.8
dj-database-url==2.3.0
Django==5.1.5
django-cors-headers==4.4.0
django-humanize==0.1.2
django-jazzmin==3.0.0
django-prometheus==2.3.1
django-rest-framework==0.1.0
djangorestframework==3.15.2
djangorestframework-simplejwt==5.3.1
djoser==2.3.1
drf-yasg==1.21.7
filelock==3.16.1
flake8==7.1.1
flake8-docstrings==1.7.0
flake8-isort==6.1.1
humanize==4.11.0
identify==2.6.1
idna==3.10
inflection==0.5.1
isort==5.13.2
mccabe==0.7.0
nodeenv==1.9.1
oauthlib==3.2.2
packaging==24.1
platformdirs==4.3.6
pre-commit==3.8.0
prometheus_client==0.21.0
psycopg2==2.9.9
pycodestyle==2.12.1
pycparser==2.22
pydocstyle==6.3.0
pyflakes==3.2.0
PyJWT==2.9.0
python-decouple==3.8
python-dotenv==1.0.1
python3-openid==3.2.0
pytz==2024.2
PyYAML==6.0.2
requests==2.32.3
requests-oauthlib==2.0.0
setuptools==75.1.0
snowballstemmer==2.2.0
social-auth-app-django==5.4.2
social-auth-core==4.5.4
sqlparse==0.5.1
toml==0.10.2
typing_extensions==4.12.2
tzdata==2024.2
uritemplate==4.1.1
urllib3==2.3.0
virtualenv==20.26.6
# Celery and Redis
celery>=5.3.0
redis>=4.5.0
django-celery-results>=2.5.0
triton==3.2.0
types-cffi==1.17.0.20250326
types-pyOpenSSL==24.1.0.20240722
types-redis==4.6.0.20241004
types-setuptools==80.0.0.20250429

# Audio processing
openai-whisper>=20231117
numpy>=1.24.0
torch>=2.0.0
torchaudio>=2.0.0
pydub>=0.25.0
librosa>=0.10.0
soundfile>=0.12.0

# Monitoring
celery-prometheus-exporter==1.7.0
flower==2.0.1

# Caching and optimization
django-redis>=5.2.0
redis>=4.5.0
hiredis>=2.2.0

# Memory profiling and optimization (development)
memory-profiler>=0.60.0
psutil>=5.9.0
