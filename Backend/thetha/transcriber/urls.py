"""
URL configuration for the transcriber app.
"""
from django.urls import include, path
from rest_framework.routers import Default<PERSON><PERSON>er

from .views import (TranscriberTemplateView, TranscriptionViewSet,
                    test_transcription, test_transcription_results,
                    test_transcription_status)

router = DefaultRouter()
router.register(r'', TranscriptionViewSet, basename='transcription')

urlpatterns = [
    path('', include(router.urls)),
    path('web/', TranscriberTemplateView.as_view(), name='transcriber-web'),

    # Test endpoints that don't require authentication
    path('test/upload/', test_transcription, name='test-transcription'),
    path('test/<uuid:pk>/status/',
         test_transcription_status,
         name='test-transcription-status'),
    path('test/<uuid:pk>/results/',
         test_transcription_results,
         name='test-transcription-results'),
]
