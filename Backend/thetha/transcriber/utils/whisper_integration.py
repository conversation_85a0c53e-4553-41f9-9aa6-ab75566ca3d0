"""Integration with OpenAI's Whisper model for transcription."""
import gc
import logging
import os
import threading
import time
from functools import lru_cache

import torch
import whisper
from django.conf import settings
from django.core.cache import cache

logger = logging.getLogger(__name__)


# Global model cache to avoid loading models repeatedly
_model_cache = {}
_model_lock = threading.Lock()

class WhisperTranscriber:
    """Optimized class for transcribing audio using OpenAI's Whisper model."""
    def __init__(self, model_name=None, device=None):
        """
        Initialize the transcriber with a specific model.

        Args:
            model_name (str, optional): Name of the Whisper model to use.
            device (str, optional): Device to use ('cuda', 'cpu', or 'auto')
        """
        self.model_name = model_name or settings.WHISPER_MODEL
        self.device = self._get_device(device)
        self.model = None
        self.last_used = time.time()
        logger.info(f"Initialized WhisperTranscriber with model: {self.model_name}, device: {self.device}")
    
    def _get_device(self, device=None):
        """Determine the best device to use for inference."""
        if device:
            return device
        
        if torch.cuda.is_available():
            # Check available VRAM
            try:
                torch.cuda.empty_cache()
                memory_gb = torch.cuda.get_device_properties(0).total_memory / 1e9
                if memory_gb >= 6:  # Minimum for larger models
                    logger.info(f"Using CUDA with {memory_gb:.1f}GB VRAM")
                    return "cuda"
            except Exception as e:
                logger.warning(f"CUDA available but error checking memory: {e}")
        
        logger.info("Using CPU for inference")
        return "cpu"

    def load_model(self):
        """Load the Whisper model with caching and optimization."""
        if self.model is not None:
            self.last_used = time.time()
            return
            
        model_key = f"{self.model_name}_{self.device}"
        
        with _model_lock:
            # Check if model is already cached
            if model_key in _model_cache:
                self.model = _model_cache[model_key]
                self.last_used = time.time()
                logger.info(f"Using cached model: {model_key}")
                return
            
            try:
                logger.info(f"Loading Whisper model: {self.model_name} on {self.device}")
                start_time = time.time()
                
                # Load model with device specification
                self.model = whisper.load_model(self.model_name, device=self.device)
                
                # Optimize for inference
                if hasattr(self.model, 'eval'):
                    self.model.eval()
                
                # Enable optimizations for CUDA
                if self.device == "cuda" and hasattr(torch.backends, 'cudnn'):
                    torch.backends.cudnn.benchmark = True
                
                load_time = time.time() - start_time
                logger.info(f"Model loaded in {load_time:.2f}s")
                
                # Cache the model (limit cache size to prevent memory issues)
                if len(_model_cache) < 3:  # Limit to 3 models in cache
                    _model_cache[model_key] = self.model
                else:
                    logger.warning("Model cache full, not caching this model")
                
                self.last_used = time.time()
                
            except Exception as e:
                logger.error(f"Error loading model {self.model_name}: {e}")
                raise RuntimeError(f"Failed to load Whisper model: {e}")

    def transcribe_segment(self, segment_path, language='en', **kwargs):
        """Transcribe an audio segment with optimization.

        Args:
            segment_path (str): Path to the audio segment file
            language (str): Language code for transcription
            **kwargs: Additional options for transcription

        Returns:
            dict: Transcription result containing text and confidence
        """
        try:
            if not os.path.exists(segment_path):
                raise FileNotFoundError(f"Audio segment not found: {segment_path}")
            
            # Load model if not already loaded
            self.load_model()
            
            logger.debug(f"Transcribing segment: {segment_path}")
            start_time = time.time()
            
            # Prepare transcription options
            transcribe_options = {
                'language': language,
                'task': 'transcribe',
                'fp16': self.device == 'cuda',  # Use fp16 for GPU
                'condition_on_previous_text': False,  # Faster for short segments
                'temperature': 0.0,  # More deterministic
                'compression_ratio_threshold': 2.4,
                'logprob_threshold': -1.0,
                'no_speech_threshold': 0.6,
                **kwargs
            }
            
            # Transcribe audio with optimization
            with torch.no_grad():  # Disable gradient computation for inference
                result = self.model.transcribe(segment_path, **transcribe_options)
            
            # Extract text and confidence
            text = result.get('text', '').strip()
            
            # Calculate average confidence if available
            confidence = None
            if 'segments' in result and result['segments']:
                confidences = [
                    seg.get('no_speech_prob', 0) for seg in result['segments']
                    if 'no_speech_prob' in seg
                ]
                if confidences:
                    # Convert no_speech_prob to confidence (inverse)
                    confidence = 1.0 - (sum(confidences) / len(confidences))
                else:
                    # Fallback: use avg_logprob if available
                    logprobs = [
                        seg.get('avg_logprob', -1.0) for seg in result['segments']
                        if 'avg_logprob' in seg
                    ]
                    if logprobs:
                        avg_logprob = sum(logprobs) / len(logprobs)
                        confidence = max(0.0, min(1.0, (avg_logprob + 1.0) / 1.0))
            
            transcription_time = time.time() - start_time
            logger.debug(f"Transcription completed in {transcription_time:.2f}s: '{text[:50]}...'")
            
            self.last_used = time.time()
            
            return {
                'text': text,
                'confidence': confidence,
                'processing_time': transcription_time,
                'language': result.get('language', language)
            }

        except Exception as e:
            logger.error(f"Error transcribing audio segment {segment_path}: {str(e)}")
            raise ValueError(f'Error transcribing audio segment: {str(e)}')

    def transcribe_file(self, file_path, language='en', **kwargs):
        """
        Transcribe a full audio file without segmentation.

        Args:
            file_path (str): Path to the audio file
            language (str): Language code for transcription
            **kwargs: Additional options for transcription

        Returns:
            dict: Transcription result containing text and segments
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Audio file not found: {file_path}")
            
            # Load model if not already loaded
            self.load_model()
            
            logger.info(f"Transcribing full file: {file_path}")
            start_time = time.time()
            
            # Prepare transcription options
            transcribe_options = {
                'language': language,
                'task': 'transcribe',
                'fp16': self.device == 'cuda',
                'verbose': False,
                **kwargs
            }
            
            # Transcribe audio
            with torch.no_grad():
                result = self.model.transcribe(file_path, **transcribe_options)
            
            transcription_time = time.time() - start_time
            logger.info(f"Full file transcription completed in {transcription_time:.2f}s")
            
            self.last_used = time.time()
            
            return {
                'text': result.get('text', '').strip(),
                'segments': result.get('segments', []),
                'language': result.get('language', language),
                'processing_time': transcription_time
            }

        except Exception as e:
            logger.error(f"Error transcribing audio file {file_path}: {str(e)}")
            raise ValueError(f'Error transcribing audio file: {str(e)}')
    
    def cleanup(self):
        """Clean up resources and clear GPU memory if used."""
        if self.device == 'cuda':
            try:
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                logger.debug("Cleaned up GPU memory")
            except Exception as e:
                logger.warning(f"Error cleaning up GPU memory: {e}")
        
        # Force garbage collection
        gc.collect()
    
    def __del__(self):
        """Cleanup when the object is destroyed."""
        self.cleanup()


@lru_cache(maxsize=3)
def get_cached_transcriber(model_name, device):
    """Get a cached transcriber instance."""
    return WhisperTranscriber(model_name, device)

def get_transcriber(model_name=None, device=None, use_cache=True):
    """
    Get a transcriber instance with optional caching.

    Args:
        model_name (str, optional): Name of the Whisper model to use.
        device (str, optional): Device to use for inference.
        use_cache (bool): Whether to use cached instances.

    Returns:
        WhisperTranscriber: A transcriber instance
    """
    model_name = model_name or settings.WHISPER_MODEL
    
    if use_cache:
        # Use cached instance for better performance
        transcriber = get_cached_transcriber(model_name, device or 'auto')
        # Update last used time
        transcriber.last_used = time.time()
        return transcriber
    else:
        # Create new instance
        return WhisperTranscriber(model_name, device)

def cleanup_old_models(max_age_minutes=30):
    """
    Clean up old models from the cache to free memory.
    
    Args:
        max_age_minutes (int): Maximum age in minutes before cleaning up
    """
    current_time = time.time()
    max_age_seconds = max_age_minutes * 60
    
    with _model_lock:
        models_to_remove = []
        for key, model in _model_cache.items():
            # This is a simplified check - in practice you'd need to track usage time
            if hasattr(model, '_last_used'):
                if current_time - model._last_used > max_age_seconds:
                    models_to_remove.append(key)
        
        for key in models_to_remove:
            logger.info(f"Removing old model from cache: {key}")
            del _model_cache[key]
            
        if models_to_remove:
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
