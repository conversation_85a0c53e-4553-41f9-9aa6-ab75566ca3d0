"""
Audio processing utilities for the transcriber app.
"""
import gc
import os
import tempfile
import uuid
from pathlib import Path
from contextlib import contextmanager

import librosa
import numpy as np
import soundfile as sf
from django.conf import settings
from pydub import AudioSegment as PydubSegment
import logging

logger = logging.getLogger(__name__)


class AudioProcessor:
    """
    Class for processing audio files.
    Handles preprocessing, segmentation, and cleanup with memory optimization.
    """
    def __init__(self, file_path):
        """Initialize with the path to the audio file."""
        self.file_path = file_path
        self.temp_dir = tempfile.mkdtemp()
        self.sample_rate = 16000  # Standard sample rate for Whisper
        self.chunk_size = getattr(settings, 'AUDIO_CHUNK_SIZE_MB', 5) * 1024 * 1024  # Convert to bytes
        logger.info(f"Initialized AudioProcessor for file: {file_path}")

    @contextmanager
    def _memory_efficient_load(self, file_path, sr=None, duration=None, offset=0):
        """Context manager for memory-efficient audio loading."""
        y = None
        try:
            y, actual_sr = librosa.load(file_path, sr=sr, duration=duration, offset=offset)
            yield y, actual_sr
        finally:
            if y is not None:
                del y
                gc.collect()

    def get_audio_duration(self):
        """Get the duration of the audio file in seconds using memory-efficient method."""
        try:
            # Use librosa.get_duration for file-based duration calculation
            duration = librosa.get_duration(path=self.file_path)
            logger.info(f"Audio duration: {duration:.2f} seconds")
            return duration
        except Exception as e:
            logger.error(f"Error getting audio duration: {str(e)}")
            raise ValueError(f'Error getting audio duration: {str(e)}')

    def preprocess_audio(self):
        """
        Preprocess the audio file with memory optimization:
        - Convert to WAV format
        - Resample to 16kHz
        - Apply noise reduction
        - Normalize volume

        Returns:
            str: Path to the preprocessed audio file
        """
        try:
            logger.info("Starting audio preprocessing")
            
            # Get file size to determine processing strategy
            file_size = os.path.getsize(self.file_path)
            
            preprocessed_path = os.path.join(
                self.temp_dir, f'preprocessed_{uuid.uuid4()}.wav')
            
            if file_size > self.chunk_size:
                # Process large files in chunks
                logger.info(f"Processing large file in chunks (size: {file_size / (1024*1024):.2f} MB)")
                self._process_large_file_chunked(preprocessed_path)
            else:
                # Process small files normally
                logger.info(f"Processing small file normally (size: {file_size / (1024*1024):.2f} MB)")
                with self._memory_efficient_load(self.file_path, sr=None) as (y, sr):
                    # Resample to 16kHz if needed
                    if sr != self.sample_rate:
                        y = librosa.resample(y, orig_sr=sr, target_sr=self.sample_rate)
                    
                    # Apply noise reduction (simple high-pass filter)
                    y = librosa.effects.preemphasis(y)
                    
                    # Normalize audio
                    y = librosa.util.normalize(y)
                    
                    # Save preprocessed audio
                    sf.write(preprocessed_path, y, self.sample_rate)
            
            logger.info("Audio preprocessing completed")
            return preprocessed_path

        except Exception as e:
            logger.error(f"Error preprocessing audio: {str(e)}")
            raise ValueError(f'Error preprocessing audio: {str(e)}')
    
    def _process_large_file_chunked(self, output_path):
        """Process large audio files in chunks to avoid memory issues."""
        try:
            # Get total duration
            total_duration = self.get_audio_duration()
            chunk_duration = 60  # Process 60 seconds at a time
            
            with sf.SoundFile(output_path, 'w', samplerate=self.sample_rate, channels=1) as output_file:
                for offset in range(0, int(total_duration), chunk_duration):
                    current_duration = min(chunk_duration, total_duration - offset)
                    
                    with self._memory_efficient_load(
                        self.file_path, 
                        sr=None, 
                        duration=current_duration, 
                        offset=offset
                    ) as (y, sr):
                        # Resample if needed
                        if sr != self.sample_rate:
                            y = librosa.resample(y, orig_sr=sr, target_sr=self.sample_rate)
                        
                        # Apply preprocessing
                        y = librosa.effects.preemphasis(y)
                        y = librosa.util.normalize(y)
                        
                        # Write chunk to output file
                        output_file.write(y.reshape(-1, 1))
                    
                    logger.info(f"Processed chunk {offset//chunk_duration + 1}/{int(total_duration)//chunk_duration + 1}")
                    
        except Exception as e:
            logger.error(f"Error in chunked processing: {str(e)}")
            raise

    def segment_audio(self, preprocessed_path, max_duration=None):
        """
        Segment the audio file into smaller chunks with memory optimization.

        Args:
            preprocessed_path (str): Path to the preprocessed audio file
            max_duration (float): Maximum duration of each segment in seconds

        Returns:
            list: List of dictionaries containing segment information
        """
        try:
            if max_duration is None:
                max_duration = getattr(settings, 'MAX_SEGMENT_DURATION', 30.0)
            
            logger.info(f"Starting audio segmentation with max_duration: {max_duration}s")
            
            # Get audio duration efficiently
            duration = librosa.get_duration(path=preprocessed_path)
            
            # Calculate number of segments
            num_segments = int(np.ceil(duration / max_duration))
            logger.info(f"Will create {num_segments} segments")
            
            # Create segments using memory-efficient approach
            segments = []
            
            # Use pydub for efficient segmentation
            audio = PydubSegment.from_file(preprocessed_path)
            
            try:
                for i in range(num_segments):
                    start_time = i * max_duration
                    end_time = min((i + 1) * max_duration, duration)
                    segment_duration = end_time - start_time
                    
                    # Skip very short segments (less than 1 second)
                    if segment_duration < 1.0:
                        logger.info(f"Skipping segment {i} (too short: {segment_duration:.2f}s)")
                        continue
                    
                    # Extract segment
                    start_ms = int(start_time * 1000)
                    end_ms = int(end_time * 1000)
                    segment_audio = audio[start_ms:end_ms]
                    
                    # Save segment
                    segment_path = os.path.join(self.temp_dir,
                                                f'segment_{i:04d}_{uuid.uuid4()}.wav')
                    segment_audio.export(segment_path, format='wav')
                    
                    # Create segment info
                    segment_info = {
                        'index': i,
                        'start_time': start_time,
                        'end_time': end_time,
                        'duration': segment_duration,
                        'file_path': segment_path
                    }
                    
                    segments.append(segment_info)
                    logger.debug(f"Created segment {i+1}/{num_segments}: {segment_duration:.2f}s")
                    
                    # Clear segment audio from memory
                    del segment_audio
                    
            finally:
                # Clear the main audio object
                del audio
                gc.collect()
            
            logger.info(f"Segmentation completed: {len(segments)} segments created")
            return segments

        except Exception as e:
            logger.error(f"Error segmenting audio: {str(e)}")
            raise ValueError(f'Error segmenting audio: {str(e)}')

    def cleanup(self):
        """Remove temporary files and directory with better error handling."""
        import shutil
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info(f"Cleaned up temporary directory: {self.temp_dir}")
        except Exception as e:
            logger.error(f'Error cleaning up temporary files: {str(e)}')
            # Try to remove individual files if directory removal fails
            try:
                for file_path in os.listdir(self.temp_dir):
                    full_path = os.path.join(self.temp_dir, file_path)
                    if os.path.isfile(full_path):
                        os.remove(full_path)
                os.rmdir(self.temp_dir)
                logger.info("Successfully cleaned up files individually")
            except Exception as inner_e:
                logger.error(f"Failed to clean up individual files: {str(inner_e)}")

    def __del__(self):
        """Ensure cleanup when the object is destroyed."""
        self.cleanup()


def get_file_type(file_path):
    """
    Determine the file type of an audio file.

    Args:
        file_path (str): Path to the audio file

    Returns:
        str: File type (e.g., 'mp3', 'wav', 'flac')
    """
    extension = Path(file_path).suffix.lower().lstrip('.')
    return extension


def validate_audio_file(file):
    """
    Validate that the file is a supported audio file.

    Args:
        file: The uploaded file object

    Returns:
        bool: True if valid, False otherwise
    """
    # Check file size
    if file.size > settings.MAX_UPLOAD_SIZE:
        return False

    # Check file type
    content_type = file.content_type
    if content_type not in settings.SUPPORTED_AUDIO_FORMATS:
        return False

    return True
