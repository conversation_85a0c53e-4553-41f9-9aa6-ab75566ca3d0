"""Celery tasks for the transcriber app."""
# All formatting issues have been resolved
import gc
import logging
import os
import time
from contextlib import contextmanager

from celery import shared_task
from django.conf import settings
from django.db import connections, transaction

from .metrics import (TRANSCRIPTION_ATTEMPTS, TRANSCRIPTION_FAILURES,
                      TRANSCRIPTION_FILE_SIZE, TRANSCRIPTION_PROCESSING_TIME,
                      TRANSCRIPTION_QUEUE_SIZE)
from .models import AudioFile, AudioSegment, Transcript
from .utils.audio_processor import AudioProcessor, get_file_type
from .utils.whisper_integration import cleanup_old_models, get_transcriber

logger = logging.getLogger(__name__)


@contextmanager
def database_cleanup():
    """Context manager to ensure database connections are cleaned up."""
    try:
        yield
    finally:
        # Close old database connections
        connections.close_all()
        # Force garbage collection
        gc.collect()


@contextmanager
def audio_processing_context(audio_file_id):
    """Context manager for audio processing with proper cleanup."""
    processor = None
    audio_file = None
    try:
        audio_file = AudioFile.objects.get(id=audio_file_id)
        yield audio_file
    except Exception as e:
        if audio_file:
            audio_file.status = 'failed'
            audio_file.error_message = str(e)
            audio_file.save()
        raise
    finally:
        if processor:
            processor.cleanup()
        gc.collect()


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def process_audio_file(self, audio_file_id):
    """Process an audio file with improved error handling and resource management.

    Process an audio file:
    - Update file metadata
    - Preprocess audio
    - Segment audio
    - Create segment records
    - Queue transcription tasks

    Args:
        audio_file_id (int): ID of the AudioFile to process
    """
    start_time = time.time()

    with database_cleanup():
        try:
            logger.info(f'Starting processing of audio file {audio_file_id}')

            with audio_processing_context(audio_file_id) as audio_file:
                # Check if file is too large
                file_path = audio_file.file.path
                if not os.path.exists(file_path):
                    raise FileNotFoundError(
                        f'Audio file not found: {file_path}')

                file_size = os.path.getsize(file_path)
                max_size = getattr(settings, 'MAX_UPLOAD_SIZE',
                                   50 * 1024 * 1024)  # 50MB default

                if file_size > max_size:
                    raise ValueError(
                        f'File too large: {file_size / (1024 * 1024):.1f}MB > {max_size / (1024 * 1024):.1f}MB'
                    )

                # Update status to processing
                audio_file.status = 'processing'
                audio_file.save()

                # Update file metadata
                file_type = get_file_type(file_path)
                audio_file.file_type = file_type

                # Update metrics - file size
                TRANSCRIPTION_FILE_SIZE.labels(
                    file_id=str(audio_file.id)).set(file_size)

                # Create audio processor
                processor = AudioProcessor(file_path)

                try:
                    logger.info(f'Getting audio duration for file: {file_path}')
                    # Get audio duration and validate
                    duration = processor.get_audio_duration()
                    logger.info(f'Successfully got audio duration: {duration:.2f}s')
                    max_duration = getattr(settings, 'MAX_AUDIO_DURATION',
                                           1800)  # 30 minutes

                    if duration > max_duration:
                        raise ValueError(
                            f'Audio too long: {duration:.1f}s > {max_duration}s'
                        )

                    audio_file.duration = duration
                    audio_file.save()

                    logger.info(
                        f'Audio file {audio_file_id}: {duration:.1f}s, {file_size / (1024 * 1024):.1f}MB'
                    )

                    # Preprocess audio
                    logger.info(f'Preprocessing audio file {audio_file_id}')
                    preprocessed_path = processor.preprocess_audio()

                    # Segment audio
                    logger.info(f'Segmenting audio file {audio_file_id}')
                    segments = processor.segment_audio(preprocessed_path)

                    if not segments:
                        raise ValueError(
                            'No valid segments created from audio file')

                    logger.info(
                        f'Created {len(segments)} segments for audio file {audio_file_id}'
                    )

                    # Create segment records and queue transcription tasks
                    segment_tasks = []
                    with transaction.atomic():
                        for segment_info in segments:
                            # Create segment record
                            segment = AudioSegment.objects.create(
                                audio_file=audio_file,
                                start_time=segment_info['start_time'],
                                end_time=segment_info['end_time'],
                                duration=segment_info['duration'],
                                file_path=segment_info['file_path'],
                                status='pending')

                            # Queue transcription task with priority based on segment index
                            priority = segment_info[
                                'index']  # Earlier segments have higher priority
                            task = transcribe_segment.apply_async(
                                args=[segment.id],
                                priority=priority)

                            # Update segment with task ID
                            segment.task_id = task.id
                            segment.save()

                            segment_tasks.append(task.id)

                        # Update queue size metric
                        pending_segments = AudioSegment.objects.filter(
                            status='pending').count()
                        TRANSCRIPTION_QUEUE_SIZE.set(pending_segments)

                    processing_time = time.time() - start_time
                    logger.info(
                        f'Audio file {audio_file_id} processed successfully in {processing_time:.2f}s'
                    )

                    return {
                        'success': True,
                        'audio_file_id': audio_file_id,
                        'segments_created': len(segments),
                        'task_ids': segment_tasks,
                        'processing_time': processing_time
                    }

                except Exception as e:
                    logger.error(
                        f'Error processing audio file {audio_file_id}: {str(e)}'
                    )
                    audio_file.status = 'failed'
                    audio_file.error_message = str(e)
                    audio_file.save()

                    # Update failure metric
                    TRANSCRIPTION_FAILURES.inc()

                    # Determine if we should retry
                    if isinstance(e, (FileNotFoundError, ValueError)):
                        # Don't retry for these errors
                        raise e

                    # Retry for other errors
                    if self.request.retries < self.max_retries:
                        countdown = min(300, (2**self.request.retries) *
                                        60)  # Exponential backoff
                        logger.warning(
                            f'Retrying audio file {audio_file_id} in {countdown}s (attempt {self.request.retries + 1}/{self.max_retries})'
                        )
                        raise self.retry(exc=e, countdown=countdown)
                    else:
                        raise e

                finally:
                    # Clean up processor
                    if 'processor' in locals():
                        processor.cleanup()

        except AudioFile.DoesNotExist:
            logger.error(f'Audio file {audio_file_id} not found')
            return {
                'success': False,
                'error': f'Audio file {audio_file_id} not found'
            }

        except Exception as e:
            logger.error(
                f'Unhandled error processing audio file {audio_file_id}: {str(e)}'
            )
            TRANSCRIPTION_FAILURES.inc()
            return {
                'success': False,
                'error': str(e),
                'audio_file_id': audio_file_id
            }


@shared_task(bind=True, max_retries=3, default_retry_delay=30)
def transcribe_segment(self, segment_id):
    """
    Transcribe an audio segment using Whisper with optimized resource management.

    Args:
        segment_id (int): ID of the AudioSegment to transcribe
    """
    # Increment attempts metric
    TRANSCRIPTION_ATTEMPTS.inc()

    # Start timing
    start_time = time.time()
    transcriber = None

    with database_cleanup():
        try:
            logger.debug(f'Starting transcription of segment {segment_id}')

            # Get the segment
            segment = AudioSegment.objects.select_related('audio_file').get(
                id=segment_id)

            # Update status
            segment.status = 'processing'
            segment.save(update_fields=['status'])

            # Get file path
            file_path = segment.file_path

            # Check if file exists
            if not file_path or not os.path.exists(file_path):
                raise FileNotFoundError(f'Segment file not found: {file_path}')

            # Validate segment duration
            if segment.duration < 0.5:  # Skip very short segments
                logger.warning(
                    f'Skipping very short segment {segment_id} ({segment.duration:.2f}s)'
                )
                segment.status = 'completed'
                segment.save()

                Transcript.objects.create(segment=segment,
                                          text='',
                                          confidence=0.0)
                return f'Segment {segment_id} skipped (too short)'

            # Create transcriber with caching
            model_name = getattr(settings, 'WHISPER_MODEL', 'small')
            transcriber = get_transcriber(model_name=model_name,
                                          device='auto',
                                          use_cache=True)

            try:
                # Determine language (could be enhanced with language detection)
                language = 'en'  # Default, could be made configurable per audio file

                # Transcribe segment with optimization options
                result = transcriber.transcribe_segment(
                    file_path,
                    language=language,
                    temperature=0.0,  # More deterministic
                    condition_on_previous_text=False,  # Faster for independent segments
                )

                # Create transcript with additional metadata
                Transcript.objects.create(segment=segment,
                                          text=result['text'],
                                          confidence=result.get(
                                              'confidence', 0.0))

                # Update segment status
                segment.status = 'completed'
                segment.save(update_fields=['status'])

                # Check if all segments are completed
                audio_file = segment.audio_file
                incomplete_count = audio_file.segments.exclude(
                    status__in=['completed', 'failed']).count()

                if incomplete_count == 0:
                    # Check if we have any successful segments
                    successful_segments = audio_file.segments.filter(
                        status='completed').count()
                    if successful_segments > 0:
                        audio_file.status = 'completed'
                    else:
                        audio_file.status = 'failed'
                        audio_file.error_message = 'All segments failed to transcribe'

                    audio_file.save(update_fields=['status', 'error_message'])

                    # Clear file size metric when done
                    TRANSCRIPTION_FILE_SIZE.labels(
                        file_id=str(audio_file.id)).set(0)

                    logger.info(
                        f'Audio file {audio_file.id} transcription completed')

                # Update queue size metric
                pending_segments = AudioSegment.objects.filter(
                    status='pending').count()
                TRANSCRIPTION_QUEUE_SIZE.set(pending_segments)

                # Record processing time
                processing_time = time.time() - start_time
                TRANSCRIPTION_PROCESSING_TIME.observe(processing_time)

                logger.debug(
                    f'Segment {segment_id} transcribed successfully in {processing_time:.2f}s'
                )

                return {
                    'success':
                    True,
                    'segment_id':
                    segment_id,
                    'text':
                    result['text'][:100] +
                    '...' if len(result['text']) > 100 else result['text'],
                    'confidence':
                    result.get('confidence', 0.0),
                    'processing_time':
                    processing_time
                }

            except Exception as e:
                logger.error(
                    f'Error transcribing segment {segment_id}: {str(e)}')
                segment.status = 'failed'
                segment.error_message = str(e)
                segment.save(update_fields=['status', 'error_message'])

                # Update failure metric
                TRANSCRIPTION_FAILURES.inc()

                # Determine if we should retry
                if isinstance(e, (FileNotFoundError, ValueError)):
                    # Don't retry for these errors
                    raise e

                # Retry with exponential backoff
                if self.request.retries < self.max_retries:
                    countdown = min(180, (2**self.request.retries) *
                                    30)  # Max 3 minutes
                    logger.warning(
                        f'Retrying segment {segment_id} in {countdown}s (attempt {self.request.retries + 1}/{self.max_retries})'
                    )
                    raise self.retry(exc=e, countdown=countdown)
                else:
                    raise e

            finally:
                # Clean up transcriber resources
                if transcriber:
                    transcriber.cleanup()

        except AudioSegment.DoesNotExist:
            logger.error(f'Segment {segment_id} not found')
            TRANSCRIPTION_FAILURES.inc()
            return {
                'success': False,
                'error': f'Segment {segment_id} not found'
            }

        except Exception as e:
            logger.error(
                f'Unhandled error transcribing segment {segment_id}: {str(e)}')
            TRANSCRIPTION_FAILURES.inc()
            return {
                'success': False,
                'error': str(e),
                'segment_id': segment_id
            }


@shared_task
def cleanup_temp_files(max_age_hours=24):
    """Cleanup temporary files and optimize system resources.

    Args:
        max_age_hours (int): Maximum age in hours before cleaning up files
    """
    try:
        logger.info(
            f'Starting cleanup of temporary files older than {max_age_hours} hours'
        )

        # Current time
        current_time = time.time()
        max_age_seconds = max_age_hours * 60 * 60

        # Get segments with file_path
        segments = AudioSegment.objects.filter(
            file_path__isnull=False).exclude(
                file_path='').select_related('audio_file')

        cleaned_files = 0
        freed_space = 0

        for segment in segments:
            try:
                file_path = segment.file_path

                # Check if file exists
                if os.path.exists(file_path):
                    # Get file modification time
                    mtime = os.path.getmtime(file_path)

                    # Check if file is older than specified time
                    if current_time - mtime > max_age_seconds:
                        # Get file size before deletion
                        file_size = os.path.getsize(file_path)

                        # Remove file
                        os.remove(file_path)

                        # Update segment
                        segment.file_path = None
                        segment.save(update_fields=['file_path'])

                        cleaned_files += 1
                        freed_space += file_size

                        logger.debug(f'Cleaned up segment file: {file_path}')
                else:
                    # File doesn't exist, clear the path
                    if segment.file_path:
                        segment.file_path = None
                        segment.save(update_fields=['file_path'])
                        logger.debug(
                            f'Cleared non-existent file path for segment {segment.id}'
                        )

            except Exception as e:
                logger.error(
                    f'Error cleaning up file for segment {segment.id}: {str(e)}'
                )

        # Also cleanup old model cache
        cleanup_old_models(max_age_minutes=30)

        # Force garbage collection
        gc.collect()

        freed_mb = freed_space / (1024 * 1024)
        logger.info(
            f'Cleanup completed: {cleaned_files} files removed, {freed_mb:.2f}MB freed'
        )

        return {
            'success': True,
            'files_cleaned': cleaned_files,
            'space_freed_mb': freed_mb,
            'max_age_hours': max_age_hours
        }

    except Exception as e:
        logger.error(f'Error in cleanup task: {str(e)}')
        return {'success': False, 'error': str(e)}


@shared_task
def health_check():
    """Periodic health check task to ensure system is functioning properly."""
    try:
        # Check database connectivity
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute('SELECT 1')

        # Check Redis connectivity
        from django.core.cache import cache
        cache.set('health_check', time.time(), 60)
        cache.get('health_check')

        # Check disk space
        import shutil
        media_path = getattr(settings, 'MEDIA_ROOT', '/tmp')
        if os.path.exists(media_path):
            total, used, free = shutil.disk_usage(media_path)
            free_gb = free / (1024**3)

            if free_gb < 1.0:  # Less than 1GB free
                logger.warning(f'Low disk space: {free_gb:.2f}GB free')

        # Update queue metrics
        pending_segments = AudioSegment.objects.filter(
            status='pending').count()
        processing_segments = AudioSegment.objects.filter(
            status='processing').count()

        TRANSCRIPTION_QUEUE_SIZE.set(pending_segments)

        return {
            'success': True,
            'timestamp': time.time(),
            'pending_segments': pending_segments,
            'processing_segments': processing_segments,
            'free_disk_gb': free_gb if 'free_gb' in locals() else None
        }

    except Exception as e:
        logger.error(f'Health check failed: {str(e)}')
        return {'success': False, 'error': str(e)}
