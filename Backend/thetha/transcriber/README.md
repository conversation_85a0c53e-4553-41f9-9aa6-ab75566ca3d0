# Transcriber App

A Django app for transcribing audio files using OpenAI's Whisper model.

## Features

- Audio file upload and processing
- Smart segmentation of audio files
- Asynchronous transcription using Celery and Redis
- Real-time status updates
- Manual correction of failed segments
- Aggregated final transcript

## Installation

The app is designed to be used within the Thetha project. It's automatically installed when you set up the project using Docker Compose.

### Prerequisites

- Docker and Docker Compose
- Python 3.12+
- FFmpeg (installed in the Docker container)

## Usage

### API Endpoints

- `POST /api/transcribe/upload/`: Upload an audio file for transcription
- `GET /api/transcribe/{task_id}/status/`: Get the status of a transcription task
- `GET /api/transcribe/{task_id}/segments/`: List all segments and their status
- `POST /api/transcribe/{task_id}/segments/{segment_id}/manual/`: Manually submit transcript for a failed segment
- `GET /api/transcribe/{task_id}/results/`: Get the aggregated transcription result

### Web Interface

A simple web interface is available at `/api/transcribe/web/` for testing the transcription functionality.

## Architecture

### Models

- `AudioFile`: Stores uploaded audio files and their metadata
- `AudioSegment`: Stores segments of an audio file for processing
- `Transcript`: Stores transcriptions of audio segments

### Components

- **Audio Processor**: Handles preprocessing, segmentation, and cleanup of audio files
- **Whisper Integration**: Integrates with OpenAI's Whisper model for transcription
- **Celery Tasks**: Handles asynchronous processing of audio files and segments
- **API Views**: Provides RESTful endpoints for interacting with the app

## Configuration

The following settings can be configured in the Django settings file:

- `WHISPER_MODEL`: The Whisper model to use (default: 'base')
- `MAX_UPLOAD_SIZE`: Maximum file upload size (default: 10MB)
- `SUPPORTED_AUDIO_FORMATS`: List of supported audio formats

## Development

### Running Tests

```bash
python manage.py test transcriber
```

### Adding New Features

The app is designed to be modular and extensible. To add new features:

1. Update the models if necessary
2. Add new serializers for API responses
3. Add new views or update existing ones
4. Add new Celery tasks if needed
5. Update the URLs configuration

## License

This app is part of the Thetha project and is subject to the same license.
