"""
Serializers for the transcriber app.
"""
from rest_framework import serializers

from .models import AudioFile, AudioSegment, Transcript
from .utils.audio_processor import validate_audio_file


class TranscriptSerializer(serializers.ModelSerializer):
    """Serializer for the Transcript model."""
    class Meta:
        model = Transcript
        fields = [
            'id', 'text', 'confidence', 'is_edited', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'confidence', 'created_at', 'updated_at']


class AudioSegmentSerializer(serializers.ModelSerializer):
    """Serializer for the AudioSegment model."""
    transcript = TranscriptSerializer(read_only=True)

    class Meta:
        model = AudioSegment
        fields = [
            'id', 'segment_id', 'start_time', 'end_time', 'duration', 'status',
            'error_message', 'transcript', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'segment_id', 'start_time', 'end_time', 'duration', 'status',
            'created_at', 'updated_at'
        ]


class AudioFileSerializer(serializers.ModelSerializer):
    """Serializer for the AudioFile model."""
    segments = AudioSegmentSerializer(many=True, read_only=True)

    class Meta:
        model = AudioFile
        fields = [
            'id', 'title', 'file', 'file_type', 'duration', 'status',
            'error_message', 'segments', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'file_type', 'duration', 'status', 'created_at', 'updated_at'
        ]

    def validate_file(self, value):
        """Validate the uploaded audio file."""
        if not validate_audio_file(value):
            raise serializers.ValidationError(
                'Invalid audio file. Supported formats: MP3, WAV, FLAC, OGG, M4A. '
                'Maximum file size: 10MB.')
        return value


class AudioFileUploadSerializer(serializers.ModelSerializer):
    """Serializer for uploading audio files."""
    class Meta:
        model = AudioFile
        fields = ['id', 'title', 'file']

    def validate_file(self, value):
        """Validate the uploaded audio file."""
        if not validate_audio_file(value):
            raise serializers.ValidationError(
                'Invalid audio file. Supported formats: MP3, WAV, FLAC, OGG, M4A. '
                'Maximum file size: 10MB.')
        return value

    def create(self, validated_data):
        """Create a new audio file and set the user."""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class TranscriptionStatusSerializer(serializers.ModelSerializer):
    """Serializer for transcription status."""
    completed_segments = serializers.SerializerMethodField()
    failed_segments = serializers.SerializerMethodField()
    pending_segments = serializers.SerializerMethodField()
    processing_segments = serializers.SerializerMethodField()
    total_segments = serializers.SerializerMethodField()
    progress_percentage = serializers.SerializerMethodField()

    class Meta:
        model = AudioFile
        fields = [
            'id', 'title', 'status', 'error_message', 'completed_segments',
            'failed_segments', 'pending_segments', 'processing_segments',
            'total_segments', 'progress_percentage', 'created_at', 'updated_at'
        ]
        read_only_fields = fields

    def get_completed_segments(self, obj):
        """Get the number of completed segments."""
        return obj.segments.filter(status='completed').count()

    def get_failed_segments(self, obj):
        """Get the number of failed segments."""
        return obj.segments.filter(status='failed').count()

    def get_pending_segments(self, obj):
        """Get the number of pending segments."""
        return obj.segments.filter(status='pending').count()

    def get_processing_segments(self, obj):
        """Get the number of processing segments."""
        return obj.segments.filter(status='processing').count()

    def get_total_segments(self, obj):
        """Get the total number of segments."""
        return obj.segments.count()

    def get_progress_percentage(self, obj):
        """Calculate the progress percentage."""
        total = obj.segments.count()
        if total == 0:
            return 0
        completed = obj.segments.filter(status='completed').count()
        return int((completed / total) * 100)


class ManualTranscriptionSerializer(serializers.Serializer):
    """Serializer for manual transcription of a segment."""
    text = serializers.CharField(required=True)

    def update(self, instance, validated_data):
        """Update the transcript for a segment."""
        text = validated_data.get('text')

        # Get or create transcript
        transcript, created = Transcript.objects.get_or_create(
            segment=instance, defaults={
                'text': text,
                'is_edited': True
            })

        # Update if not created
        if not created:
            transcript.text = text
            transcript.is_edited = True
            transcript.save()

        # Update segment status
        instance.status = 'completed'
        instance.error_message = None
        instance.save()

        # Check if all segments are completed
        audio_file = instance.audio_file
        if not audio_file.segments.exclude(status='completed').exists():
            audio_file.status = 'completed'
            audio_file.save()

        return instance


class TranscriptionResultSerializer(serializers.ModelSerializer):
    """Serializer for the final transcription result."""
    segments = serializers.SerializerMethodField()
    full_transcript = serializers.SerializerMethodField()

    class Meta:
        model = AudioFile
        fields = [
            'id', 'title', 'status', 'duration', 'segments', 'full_transcript',
            'created_at', 'updated_at'
        ]
        read_only_fields = fields

    def get_segments(self, obj):
        """Get all segments with their transcripts."""
        segments = obj.segments.all().order_by('start_time')
        return [{
            'segment_id':
            segment.segment_id,
            'start_time':
            segment.start_time,
            'end_time':
            segment.end_time,
            'duration':
            segment.duration,
            'status':
            segment.status,
            'text':
            getattr(segment.transcript, 'text', '[Untranscribed segment]')
            if hasattr(segment, 'transcript') else '[Untranscribed segment]',
            'is_edited':
            getattr(segment.transcript, 'is_edited', False) if hasattr(
                segment, 'transcript') else False,
        } for segment in segments]

    def get_full_transcript(self, obj):
        """Get the full transcript by combining all segment transcripts."""
        segments = obj.segments.all().order_by('start_time')
        transcript_parts = []

        for segment in segments:
            if hasattr(segment, 'transcript'):
                transcript_parts.append(segment.transcript.text)
            else:
                transcript_parts.append('[Untranscribed segment]')

        return ' '.join(transcript_parts)
