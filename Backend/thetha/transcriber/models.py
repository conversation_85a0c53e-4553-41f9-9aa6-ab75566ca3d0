import os
import uuid

from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _


def audio_file_path(instance, filename):
    """Generate file path for new audio file."""
    ext = filename.split('.')[-1]
    filename = f'{uuid.uuid4()}.{ext}'
    return os.path.join('audio_files', filename)


class AudioFile(models.Model):
    """Model for storing uploaded audio files."""
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    )

    user = models.ForeignKey(settings.AUTH_USER_MODEL,
                             on_delete=models.CASCADE,
                             related_name='audio_files',
                             null=True, blank=True)
    title = models.CharField(max_length=255)
    file = models.FileField(upload_to=audio_file_path)
    file_type = models.CharField(max_length=50)
    duration = models.FloatField(null=True, blank=True)
    status = models.Char<PERSON>ield(max_length=20,
                              choices=STATUS_CHOICES,
                              default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    task_id = models.CharField(max_length=255, null=True, blank=True)
    error_message = models.TextField(null=True, blank=True)

    def __str__(self):
        return self.title

    class Meta:
        ordering = ['-created_at']


class AudioSegment(models.Model):
    """Model for storing segments of an audio file for processing."""
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    )

    audio_file = models.ForeignKey(AudioFile,
                                   on_delete=models.CASCADE,
                                   related_name='segments')
    segment_id = models.UUIDField(default=uuid.uuid4, editable=False)
    start_time = models.FloatField(help_text=_('Start time in seconds'))
    end_time = models.FloatField(help_text=_('End time in seconds'))
    duration = models.FloatField(help_text=_('Duration in seconds'))
    file_path = models.CharField(max_length=255, null=True, blank=True)
    status = models.CharField(max_length=20,
                              choices=STATUS_CHOICES,
                              default='pending')
    task_id = models.CharField(max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    error_message = models.TextField(null=True, blank=True)

    def __str__(self):
        return f'{self.audio_file.title} - Segment {self.segment_id}'

    class Meta:
        ordering = ['start_time']


class Transcript(models.Model):
    """Model for storing transcriptions of audio segments."""
    segment = models.OneToOneField(AudioSegment,
                                   on_delete=models.CASCADE,
                                   related_name='transcript')
    text = models.TextField()
    confidence = models.FloatField(null=True, blank=True)
    is_edited = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f'Transcript for {self.segment}'

    class Meta:
        ordering = ['segment__start_time']
