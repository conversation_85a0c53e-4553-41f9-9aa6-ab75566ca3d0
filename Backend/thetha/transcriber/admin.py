from django.contrib import admin

from .models import AudioFile, AudioSegment, Transcript


class AudioSegmentInline(admin.TabularInline):
    model = AudioSegment
    extra = 0
    readonly_fields = ('segment_id', 'start_time', 'end_time', 'duration',
                       'status', 'created_at', 'updated_at')
    fields = ('segment_id', 'start_time', 'end_time', 'duration', 'status',
              'created_at', 'updated_at')
    can_delete = False
    show_change_link = True


class TranscriptInline(admin.TabularInline):
    model = Transcript
    extra = 0
    readonly_fields = ('text', 'confidence', 'is_edited', 'created_at',
                       'updated_at')
    fields = ('text', 'confidence', 'is_edited', 'created_at', 'updated_at')
    can_delete = False
    show_change_link = True


@admin.register(AudioFile)
class AudioFileAdmin(admin.ModelAdmin):
    list_display = ('title', 'user', 'file_type', 'duration', 'status',
                    'created_at')
    list_filter = ('status', 'file_type', 'created_at')
    search_fields = ('title', 'user__email', 'user__username')
    readonly_fields = ('file_type', 'duration', 'created_at', 'updated_at',
                       'task_id')
    inlines = [AudioSegmentInline]
    fieldsets = (
        (None, {
            'fields': ('user', 'title', 'file')
        }),
        ('Status', {
            'fields': ('status', 'error_message')
        }),
        ('Metadata', {
            'fields':
            ('file_type', 'duration', 'task_id', 'created_at', 'updated_at')
        }),
    )


@admin.register(AudioSegment)
class AudioSegmentAdmin(admin.ModelAdmin):
    list_display = ('segment_id', 'audio_file', 'start_time', 'end_time',
                    'duration', 'status')
    list_filter = ('status', 'created_at')
    search_fields = ('audio_file__title', 'segment_id')
    readonly_fields = ('segment_id', 'audio_file', 'start_time', 'end_time',
                       'duration', 'created_at', 'updated_at', 'task_id')
    inlines = [TranscriptInline]
    fieldsets = (
        (None, {
            'fields':
            ('audio_file', 'segment_id', 'start_time', 'end_time', 'duration')
        }),
        ('Status', {
            'fields': ('status', 'error_message')
        }),
        ('Metadata', {
            'fields': ('file_path', 'task_id', 'created_at', 'updated_at')
        }),
    )


@admin.register(Transcript)
class TranscriptAdmin(admin.ModelAdmin):
    list_display = ('segment', 'confidence', 'is_edited', 'created_at',
                    'updated_at')
    list_filter = ('is_edited', 'created_at')
    search_fields = ('segment__audio_file__title', 'text')
    readonly_fields = ('segment', 'confidence', 'created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('segment', 'text', 'confidence', 'is_edited')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at')
        }),
    )
