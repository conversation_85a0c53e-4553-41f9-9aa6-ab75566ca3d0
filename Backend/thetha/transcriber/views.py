"""Views for the transcriber application."""

import os

from django.shortcuts import get_object_or_404
from django.views.generic import TemplateView
from rest_framework import status, viewsets
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>, MultiPartParser
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response

from .metrics import TRANSCRIPTION_FILE_SIZE, TRANSCRIPTION_QUEUE_SIZE
from .models import AudioFile, AudioSegment
from .serializers import (AudioFileSerializer, AudioFileUploadSerializer,
                          AudioSegmentSerializer,
                          ManualTranscriptionSerializer,
                          TranscriptionResultSerializer,
                          TranscriptionStatusSerializer)
from .tasks import process_audio_file


@api_view(['GET'])
@permission_classes([AllowAny])
def transcriber_web_view(request):
    """Function-based view for the transcriber web interface.
    
    This explicitly allows anonymous access and serves the HTML template.
    Authentication is handled by JavaScript for API calls.
    """
    from django.template.response import TemplateResponse
    return TemplateResponse(request, 'transcriber/index.html')


class TranscriptionViewSet(viewsets.ModelViewSet):
    """ViewSet for transcription operations."""

    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Return only the user's audio files."""
        return AudioFile.objects.filter(user=self.request.user)

    def get_serializer_class(self):
        """Return the appropriate serializer based on the action."""
        if self.action == 'upload':
            return AudioFileUploadSerializer
        elif self.action == 'status':
            return TranscriptionStatusSerializer
        elif self.action == 'segments':
            return AudioSegmentSerializer
        elif self.action == 'manual_transcription':
            return ManualTranscriptionSerializer
        elif self.action == 'results':
            return TranscriptionResultSerializer
        return AudioFileSerializer

    @action(detail=False, methods=['post'])
    def upload(self, request):
        """Upload an audio file for transcription."""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            # Save the audio file
            audio_file = serializer.save()

            # Update file size metric
            file_path = audio_file.file.path
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                TRANSCRIPTION_FILE_SIZE.labels(
                    file_id=str(audio_file.id)).set(file_size)

            # Queue processing task
            task = process_audio_file.delay(audio_file.id)

            # Update task ID
            audio_file.task_id = task.id
            audio_file.save()

            # Update queue size metric
            pending_segments = AudioSegment.objects.filter(
                status='pending').count()
            TRANSCRIPTION_QUEUE_SIZE.set(pending_segments)

            # Return response
            return Response(
                {
                    'id':
                    audio_file.id,
                    'title':
                    audio_file.title,
                    'status':
                    audio_file.status,
                    'message':
                    'Audio file uploaded successfully. Transcription in progress.'
                },
                status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def status(self, request, pk=None):
        """Get the status of a transcription task."""
        audio_file = self.get_object()
        serializer = self.get_serializer(audio_file)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def segments(self, request, pk=None):
        """Get all segments of an audio file."""
        audio_file = self.get_object()
        segments = audio_file.segments.all().order_by('start_time')
        serializer = self.get_serializer(segments, many=True)
        return Response(serializer.data)

    @action(detail=True,
            methods=['post'],
            url_path='segments/(?P<segment_id>[^/.]+)/manual')
    def manual_transcription(self, request, pk=None, segment_id=None):
        """Manually submit a transcript for a failed segment."""
        audio_file = self.get_object()
        segment = get_object_or_404(AudioSegment,
                                    audio_file=audio_file,
                                    segment_id=segment_id)

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            serializer.update(segment, serializer.validated_data)

            # Update queue size metric
            pending_segments = AudioSegment.objects.filter(
                status='pending').count()
            TRANSCRIPTION_QUEUE_SIZE.set(pending_segments)

            return Response(
                {
                    'message': 'Transcript updated successfully.',
                    'segment_id': segment_id
                },
                status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def results(self, request, pk=None):
        """Get the final transcription results."""
        audio_file = self.get_object()
        serializer = self.get_serializer(audio_file)
        return Response(serializer.data)


# Test views that don't require authentication
@api_view(['POST'])
@permission_classes([AllowAny])
def test_transcription(request):
    """Test endpoint for transcription without authentication.

    This endpoint allows testing the transcription functionality without JWT authentication.
    It accepts the same parameters as the regular transcription endpoint.
    """
    if 'file' not in request.FILES:
        return Response({'error': 'No file provided'},
                        status=status.HTTP_400_BAD_REQUEST)

    serializer = AudioFileUploadSerializer(data=request.data, context={'request': request})
    if serializer.is_valid():
        audio_file = serializer.save(user=None)  # No user association for test

        # Update metrics
        file_path = audio_file.file.path
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            TRANSCRIPTION_FILE_SIZE.labels(file_id=str(audio_file.id)).set(file_size)
            
        # Update queue size metric
        pending_segments = AudioSegment.objects.filter(status='pending').count()
        TRANSCRIPTION_QUEUE_SIZE.set(pending_segments)

        # Process the file asynchronously
        process_audio_file.delay(audio_file.id)

        return Response(
            {
                'id': audio_file.id,
                'status': 'processing',
                'message': 'Audio file uploaded and processing started'
            },
            status=status.HTTP_202_ACCEPTED)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([AllowAny])
def test_transcription_status(request, pk):
    """Get the status of a test transcription job."""
    try:
        audio_file = AudioFile.objects.get(pk=pk)
        serializer = TranscriptionStatusSerializer(audio_file)
        return Response(serializer.data)
    except AudioFile.DoesNotExist:
        return Response({'error': 'Transcription job not found'},
                        status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
@permission_classes([AllowAny])
def test_transcription_results(request, pk):
    """Get the results of a test transcription job."""
    try:
        audio_file = AudioFile.objects.get(pk=pk)
        serializer = TranscriptionResultSerializer(audio_file)
        return Response(serializer.data)
    except AudioFile.DoesNotExist:
        return Response({'error': 'Transcription job not found'},
                        status=status.HTTP_404_NOT_FOUND)
