"""
Prometheus metrics for the transcriber app.
"""
from prometheus_client import Counter, Gauge, Histogram

# Transcription attempts and failures
TRANSCRIPTION_ATTEMPTS = Counter('transcription_attempts_total',
                                 'Total number of transcription attempts')

TRANSCRIPTION_FAILURES = Counter('transcription_failures_total',
                                 'Total number of transcription failures')

# Transcription queue size
TRANSCRIPTION_QUEUE_SIZE = Gauge('transcription_queue_size',
                                 'Number of transcription tasks in the queue')

# Transcription processing time
TRANSCRIPTION_PROCESSING_TIME = Histogram(
    'transcription_processing_time_seconds',
    'Time taken to process a transcription',
    buckets=(1, 5, 10, 30, 60, 120, 300, 600))

# File size
TRANSCRIPTION_FILE_SIZE = Gauge('transcription_file_size_bytes',
                                'Size of audio files being transcribed',
                                ['file_id'])
