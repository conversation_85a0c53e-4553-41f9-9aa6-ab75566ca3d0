from django.core.management.base import BaseCommand
from django.core.mail import send_mail
from django.conf import settings
import sys


class Command(BaseCommand):
    help = 'Test email configuration by sending a test email'

    def add_arguments(self, parser):
        parser.add_argument(
            '--to',
            type=str,
            default='<EMAIL>',
            help='Email address to send test email to'
        )

    def handle(self, *args, **options):
        recipient_email = options['to']
        
        self.stdout.write(
            self.style.SUCCESS(f'Testing email configuration...')
        )
        
        # Display current email settings (without password)
        self.stdout.write(f'Email Backend: {settings.EMAIL_BACKEND}')
        self.stdout.write(f'Email Host: {settings.EMAIL_HOST}')
        self.stdout.write(f'Email Port: {settings.EMAIL_PORT}')
        self.stdout.write(f'Email User: {settings.EMAIL_HOST_USER}')
        self.stdout.write(f'Use TLS: {settings.EMAIL_USE_TLS}')
        self.stdout.write(f'Use SSL: {getattr(settings, "EMAIL_USE_SSL", False)}')
        self.stdout.write(f'Default From: {settings.DEFAULT_FROM_EMAIL}')
        
        try:
            # Send test email
            send_mail(
                subject='Thetha Email Configuration Test',
                message='This is a test email from your Thetha application. If you receive this, your email configuration is working correctly!',
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[recipient_email],
                fail_silently=False,
            )
            
            self.stdout.write(
                self.style.SUCCESS(f'✅ Test email sent successfully to {recipient_email}')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Failed to send email: {str(e)}')
            )
            
            # Provide troubleshooting suggestions
            self.stdout.write(
                self.style.WARNING('\n🔧 Troubleshooting suggestions:')
            )
            self.stdout.write('1. Check that EMAIL_HOST_PASSWORD is set correctly in .env')
            self.stdout.write('2. Ensure you are using a Zoho App Password (not regular password)')
            self.stdout.write('3. Verify your Zoho account allows SMTP access')
            self.stdout.write('4. Try alternative settings: PORT=465, USE_SSL=True, USE_TLS=False')
            
            sys.exit(1)
