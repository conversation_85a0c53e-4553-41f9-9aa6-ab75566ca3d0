#!/bin/bash

# =============================================================================
# THETHA ENVIRONMENT VALIDATION SCRIPT
# =============================================================================
# This script validates that all required environment variables are properly
# configured in the unified .env file
# =============================================================================

echo "🔍 Validating Thetha Environment Configuration..."
echo "================================================="

# Load environment variables
set -a
if [ -f .env ]; then
    source .env
    echo "✅ Found .env file"
else
    echo "❌ .env file not found!"
    exit 1
fi
set +a

# Check critical variables
ERRORS=0

check_var() {
    local var_name=$1
    local var_value=${!var_name}
    local is_required=${2:-true}
    
    if [ -z "$var_value" ]; then
        if [ "$is_required" = true ]; then
            echo "❌ Missing required variable: $var_name"
            ((ERRORS++))
        else
            echo "⚠️  Optional variable not set: $var_name"
        fi
    else
        echo "✅ $var_name is configured"
    fi
}

echo ""
echo "📊 Database Configuration"
echo "------------------------"
check_var "POSTGRES_DB"
check_var "POSTGRES_USER"
check_var "POSTGRES_PASSWORD"
check_var "POSTGRES_HOST"
check_var "POSTGRES_PORT"

echo ""
echo "🐍 Django Configuration"
echo "----------------------"
check_var "DJANGO_SECRET_KEY"
check_var "DJANGO_DEBUG"
check_var "DJANGO_ALLOWED_HOSTS"

echo ""
echo "📧 Email Configuration"
echo "---------------------"
check_var "EMAIL_HOST_USER"
check_var "EMAIL_HOST_PASSWORD"
check_var "EMAIL_HOST"

echo ""
echo "⚛️  Frontend Configuration"
echo "-------------------------"
check_var "VITE_API_URL"
check_var "VITE_APP_NAME"
check_var "FRONTEND_PORT"

echo ""
echo "🎵 Audio Processing Configuration"
echo "--------------------------------"
check_var "WHISPER_MODEL"
check_var "MAX_AUDIO_DURATION"
check_var "MAX_SEGMENT_DURATION"

echo ""
echo "🔄 Redis & Celery Configuration"
echo "------------------------------"
check_var "REDIS_HOST"
check_var "REDIS_PORT"
check_var "CELERY_BROKER_URL"
check_var "CELERY_RESULT_BACKEND"

echo ""
echo "📈 Optional Monitoring Configuration"
echo "-----------------------------------"
check_var "GRAFANA_ADMIN_USER" false
check_var "GRAFANA_ADMIN_PASSWORD" false
check_var "PROMETHEUS_PORT" false

echo ""
echo "================================================="
if [ $ERRORS -eq 0 ]; then
    echo "🎉 All required environment variables are configured!"
    echo "✅ Environment validation passed."
else
    echo "❌ Environment validation failed with $ERRORS error(s)."
    echo "Please check the missing variables above."
    exit 1
fi

echo ""
echo "📋 Configuration Summary:"
echo "  - Database: $POSTGRES_DB on $POSTGRES_HOST:$POSTGRES_PORT"
echo "  - Backend: Django on port $BACKEND_PORT"
echo "  - Frontend: $VITE_APP_NAME on port $FRONTEND_PORT"
echo "  - Email: $EMAIL_HOST_USER via $EMAIL_HOST"
echo "  - Whisper Model: $WHISPER_MODEL"
echo "  - Debug Mode: $DJANGO_DEBUG"
echo ""
