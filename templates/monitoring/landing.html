<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hydro System Guard - Advanced Water Management SCADA</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 25%, #06b6d4 50%, #10b981 75%, #059669 100%);
        }
        .water-animation {
            background: linear-gradient(-45deg, #06b6d4, #0891b2, #0e7490, #155e75);
            background-size: 400% 400%;
            animation: wave 4s ease-in-out infinite;
        }
        @keyframes wave {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        .float-animation {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .pulse-glow {
            animation: pulse-glow 2s ease-in-out infinite alternate;
        }
        @keyframes pulse-glow {
            from { box-shadow: 0 0 20px rgba(59, 130, 246, 0.5); }
            to { box-shadow: 0 0 30px rgba(59, 130, 246, 0.8), 0 0 40px rgba(59, 130, 246, 0.3); }
        }
        .feature-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .stats-counter {
            background: linear-gradient(145deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg border-b border-white border-opacity-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center pulse-glow">
                        <i class="fas fa-tint text-white text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-white">Hydro System Guard</h1>
                        <p class="text-sm text-blue-100">Advanced SCADA Technology</p>
                    </div>
                </div>
                <a href="{% url 'monitoring:login' %}" 
                   class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-semibold py-2 px-6 rounded-lg transition duration-300 border border-white border-opacity-30">
                    <i class="fas fa-sign-in-alt mr-2"></i>Access System
                </a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative overflow-hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <!-- Left Content -->
                <div class="text-white space-y-8">
                    <div class="space-y-4">
                        <h1 class="text-5xl lg:text-6xl font-bold leading-tight">
                            <span class="block">Smart Water</span>
                            <span class="block text-cyan-300">Management</span>
                            <span class="block">Revolution</span>
                        </h1>
                        <p class="text-xl text-blue-100 leading-relaxed">
                            Experience next-generation SCADA technology designed for 
                            <span class="text-cyan-300 font-semibold">Nyamandlovu Water Station</span>. 
                            Real-time monitoring, intelligent alerts, and automated protection systems 
                            ensuring optimal water pump operations.
                        </p>
                    </div>
                    
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="{% url 'monitoring:login' %}" 
                           class="bg-cyan-500 hover:bg-cyan-600 text-white font-bold py-4 px-8 rounded-lg transition duration-300 transform hover:scale-105 pulse-glow">
                            <i class="fas fa-rocket mr-2"></i>Launch Dashboard
                        </a>
                        <button onclick="scrollToFeatures()" 
                                class="bg-white bg-opacity-10 hover:bg-opacity-20 text-white font-semibold py-4 px-8 rounded-lg transition duration-300 border border-white border-opacity-30">
                            <i class="fas fa-info-circle mr-2"></i>Learn More
                        </button>
                    </div>
                </div>

                <!-- Right Visual -->
                <div class="relative">
                    <div class="float-animation">
                        <!-- Main Control Panel -->
                        <div class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-2xl p-8 border border-white border-opacity-20">
                            <div class="space-y-6">
                                <!-- Status Header -->
                                <div class="flex items-center justify-between">
                                    <h3 class="text-white font-semibold text-lg">System Status</h3>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                                        <span class="text-green-300 text-sm">Online</span>
                                    </div>
                                </div>

                                <!-- Pump Status Cards -->
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="feature-card rounded-lg p-4">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="text-white text-sm">Pump 1</span>
                                            <i class="fas fa-play text-green-400"></i>
                                        </div>
                                        <div class="text-2xl font-bold text-white">23.5 L/s</div>
                                        <div class="text-cyan-300 text-xs">Flow Rate</div>
                                    </div>
                                    <div class="feature-card rounded-lg p-4">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="text-white text-sm">Pump 2</span>
                                            <i class="fas fa-pause text-yellow-400"></i>
                                        </div>
                                        <div class="text-2xl font-bold text-white">0.0 L/s</div>
                                        <div class="text-cyan-300 text-xs">Standby</div>
                                    </div>
                                </div>

                                <!-- Water Quality Indicator -->
                                <div class="feature-card rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <span class="text-white font-medium">Water Quality</span>
                                        <i class="fas fa-flask text-cyan-400"></i>
                                    </div>
                                    <div class="grid grid-cols-3 gap-3 text-center">
                                        <div>
                                            <div class="text-lg font-bold text-white">320</div>
                                            <div class="text-cyan-300 text-xs">Hardness</div>
                                        </div>
                                        <div>
                                            <div class="text-lg font-bold text-white">7.2</div>
                                            <div class="text-cyan-300 text-xs">pH</div>
                                        </div>
                                        <div>
                                            <div class="text-lg font-bold text-white">2.1</div>
                                            <div class="text-cyan-300 text-xs">Turbidity</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Floating Elements -->
                    <div class="absolute -top-4 -right-4 w-20 h-20 water-animation rounded-full opacity-70"></div>
                    <div class="absolute -bottom-8 -left-8 w-32 h-32 water-animation rounded-full opacity-50"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="stats-counter rounded-xl p-6 mb-4">
                        <div class="text-4xl font-bold text-white mb-2">24/7</div>
                        <div class="text-cyan-300">Monitoring</div>
                    </div>
                </div>
                <div class="text-center">
                    <div class="stats-counter rounded-xl p-6 mb-4">
                        <div class="text-4xl font-bold text-white mb-2">99.9%</div>
                        <div class="text-cyan-300">Uptime</div>
                    </div>
                </div>
                <div class="text-center">
                    <div class="stats-counter rounded-xl p-6 mb-4">
                        <div class="text-4xl font-bold text-white mb-2">3</div>
                        <div class="text-cyan-300">Active Pumps</div>
                    </div>
                </div>
                <div class="text-center">
                    <div class="stats-counter rounded-xl p-6 mb-4">
                        <div class="text-4xl font-bold text-white mb-2">Real-time</div>
                        <div class="text-cyan-300">Alerts</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl lg:text-5xl font-bold text-white mb-6">
                    Powerful <span class="text-cyan-300">SCADA Features</span>
                </h2>
                <p class="text-xl text-blue-100 max-w-3xl mx-auto">
                    Advanced monitoring and control capabilities designed specifically for water management operations
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Real-time Monitoring -->
                <div class="feature-card rounded-xl p-8 transform hover:scale-105 transition duration-300">
                    <div class="w-16 h-16 bg-cyan-500 rounded-lg flex items-center justify-center mb-6 pulse-glow">
                        <i class="fas fa-tachometer-alt text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">Real-time Monitoring</h3>
                    <p class="text-blue-100">
                        Monitor pump performance, flow rates, pressure levels, and motor temperatures in real-time with instant updates.
                    </p>
                </div>

                <!-- Dry Running Protection -->
                <div class="feature-card rounded-xl p-8 transform hover:scale-105 transition duration-300">
                    <div class="w-16 h-16 bg-red-500 rounded-lg flex items-center justify-center mb-6 pulse-glow">
                        <i class="fas fa-shield-alt text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">Dry Running Protection</h3>
                    <p class="text-blue-100">
                        Advanced algorithms detect and prevent dry running conditions, protecting expensive pump equipment from damage.
                    </p>
                </div>

                <!-- Water Quality Analysis -->
                <div class="feature-card rounded-xl p-8 transform hover:scale-105 transition duration-300">
                    <div class="w-16 h-16 bg-green-500 rounded-lg flex items-center justify-center mb-6 pulse-glow">
                        <i class="fas fa-flask text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">Water Quality Analysis</h3>
                    <p class="text-blue-100">
                        Monitor critical parameters like hardness, pH, and turbidity - essential for Nyamandlovu's challenging water conditions.
                    </p>
                </div>

                <!-- ESP32 Integration -->
                <div class="feature-card rounded-xl p-8 transform hover:scale-105 transition duration-300">
                    <div class="w-16 h-16 bg-purple-500 rounded-lg flex items-center justify-center mb-6 pulse-glow">
                        <i class="fas fa-microchip text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">ESP32 Integration</h3>
                    <p class="text-blue-100">
                        Seamless integration with ESP32 sensors for reliable data collection from remote monitoring points.
                    </p>
                </div>

                <!-- Intelligent Alerts -->
                <div class="feature-card rounded-xl p-8 transform hover:scale-105 transition duration-300">
                    <div class="w-16 h-16 bg-yellow-500 rounded-lg flex items-center justify-center mb-6 pulse-glow">
                        <i class="fas fa-bell text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">Intelligent Alerts</h3>
                    <p class="text-blue-100">
                        Smart notification system with customizable thresholds for temperature, pressure, flow rate, and system health.
                    </p>
                </div>

                <!-- Professional Interface -->
                <div class="feature-card rounded-xl p-8 transform hover:scale-105 transition duration-300">
                    <div class="w-16 h-16 bg-blue-500 rounded-lg flex items-center justify-center mb-6 pulse-glow">
                        <i class="fas fa-desktop text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">Professional Interface</h3>
                    <p class="text-blue-100">
                        Modern, responsive design optimized for industrial environments with role-based access control.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-20">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <div class="feature-card rounded-2xl p-12">
                <h2 class="text-4xl font-bold text-white mb-6">
                    Ready to Experience Advanced Water Management?
                </h2>
                <p class="text-xl text-blue-100 mb-8">
                    Access the Hydro System Guard dashboard and take control of your water infrastructure with cutting-edge SCADA technology.
                </p>
                <a href="{% url 'monitoring:login' %}" 
                   class="inline-block bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white font-bold py-4 px-12 rounded-lg transition duration-300 transform hover:scale-105 pulse-glow text-lg">
                    <i class="fas fa-rocket mr-3"></i>Launch SCADA Dashboard
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-black bg-opacity-20 border-t border-white border-opacity-10 py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-8 h-8 bg-cyan-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-tint text-white"></i>
                        </div>
                        <span class="text-white font-bold text-lg">Hydro System Guard</span>
                    </div>
                    <p class="text-blue-100">
                        Advanced SCADA technology for water management and pump protection systems.
                    </p>
                </div>
                <div>
                    <h4 class="text-white font-semibold mb-4">Location</h4>
                    <p class="text-blue-100">
                        <i class="fas fa-map-marker-alt mr-2"></i>
                        Nyamandlovu Water Station<br>
                        Bulawayo, Zimbabwe
                    </p>
                </div>
                <div>
                    <h4 class="text-white font-semibold mb-4">Technology</h4>
                    <p class="text-blue-100">
                        <i class="fas fa-code mr-2"></i>
                        Django + REST Framework<br>
                        ESP32 IoT Integration
                    </p>
                </div>
            </div>
            <div class="border-t border-white border-opacity-10 mt-8 pt-8 text-center">
                <p class="text-blue-100">
                    &copy; 2024 Hydro System Guard SCADA. Professional water management technology.
                </p>
            </div>
        </div>
    </footer>

    <script>
        function scrollToFeatures() {
            document.getElementById('features').scrollIntoView({ behavior: 'smooth' });
        }

        // Add some interactive animations
        document.addEventListener('DOMContentLoaded', function() {
            // Animate stats on scroll
            const observerOptions = {
                threshold: 0.5,
                rootMargin: '0px 0px -100px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.transform = 'translateY(0)';
                        entry.target.style.opacity = '1';
                    }
                });
            }, observerOptions);

            // Observe feature cards
            document.querySelectorAll('.feature-card').forEach(card => {
                card.style.transform = 'translateY(30px)';
                card.style.opacity = '0';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>