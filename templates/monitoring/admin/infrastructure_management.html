{% extends "monitoring/base.html" %}

{% block title %}Infrastructure Management - Hydro System Guard SCADA{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-3xl font-bold text-white">Infrastructure Management</h2>
            <p class="text-blue-200">Manage and monitor all water infrastructure</p>
        </div>
        <button onclick="showAddInfrastructureModal()" class="bg-cyan-500 hover:bg-cyan-600 text-white px-4 py-2 rounded-lg transition duration-300 transform hover:scale-105">
            <i class="fas fa-plus mr-2"></i>Add New Infrastructure
        </button>
    </div>

    <!-- Infrastructure List -->
    <div class="glass-panel mb-8">
        <div class="px-6 py-4 border-b border-white border-opacity-20">
            <h3 class="text-lg font-semibold text-white">Infrastructure List</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="infrastructure-list">
                <!-- Infrastructure cards will be dynamically inserted here -->
            </div>
        </div>
    </div>

    <!-- Add Infrastructure Modal -->
    <div id="add-infrastructure-modal" class="fixed inset-0 bg-black bg-opacity-75 hidden items-center justify-center z-50">
        <div class="glass-panel w-full max-w-2xl mx-4 p-6 relative">
            <button onclick="closeAddInfrastructureModal()" class="absolute top-4 right-4 text-white hover:text-cyan-300">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            
            <h3 class="text-xl font-semibold text-white mb-6">Add New Infrastructure</h3>
            
            <form id="add-infrastructure-form" class="space-y-4">
                <div>
                    <label class="block text-cyan-300 mb-2">Name</label>
                    <input type="text" name="name" class="w-full bg-gray-800 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-cyan-500" required>
                </div>
                
                <div>
                    <label class="block text-cyan-300 mb-2">Type</label>
                    <select name="type" class="w-full bg-gray-800 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-cyan-500" required>
                        <option value="PUMP_HOUSE">Pump House</option>
                        <option value="RESERVOIR">Reservoir</option>
                        <option value="TREATMENT_PLANT">Treatment Plant</option>
                        <option value="DISTRIBUTION_NETWORK">Distribution Network</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-cyan-300 mb-2">Location</label>
                    <input type="text" name="location" class="w-full bg-gray-800 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-cyan-500" required>
                </div>
                
                <div>
                    <label class="block text-cyan-300 mb-2">Description</label>
                    <textarea name="description" class="w-full bg-gray-800 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-cyan-500" rows="3"></textarea>
                </div>
                
                <div>
                    <label class="block text-cyan-300 mb-2">Installation Date</label>
                    <input type="date" name="installation_date" class="w-full bg-gray-800 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-cyan-500" required>
                </div>
                
                <div class="flex justify-end space-x-4">
                    <button type="button" onclick="closeAddInfrastructureModal()" class="px-4 py-2 text-white hover:text-cyan-300">
                        Cancel
                    </button>
                    <button type="submit" class="bg-cyan-500 hover:bg-cyan-600 text-white px-4 py-2 rounded-lg">
                        Add Infrastructure
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Infrastructure Details Modal -->
    <div id="infrastructure-details-modal" class="fixed inset-0 bg-black bg-opacity-75 hidden items-center justify-center z-50">
        <div class="glass-panel w-full max-w-4xl mx-4 p-6 relative">
            <button onclick="closeInfrastructureDetailsModal()" class="absolute top-4 right-4 text-white hover:text-cyan-300">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Infrastructure Details -->
                <div class="glass-card p-6">
                    <h3 class="text-xl font-semibold text-white mb-4">Infrastructure Details</h3>
                    <div class="space-y-4" id="infrastructure-details">
                        <!-- Details will be dynamically inserted here -->
                    </div>
                </div>
                
                <!-- Control Panel -->
                <div class="glass-card p-6">
                    <h3 class="text-xl font-semibold text-white mb-4">Control Panel</h3>
                    <div class="space-y-4" id="control-panel">
                        <!-- Controls will be dynamically inserted here -->
                    </div>
                </div>
            </div>
            
            <!-- Monitoring Data -->
            <div class="glass-card p-6 mt-6">
                <h3 class="text-xl font-semibold text-white mb-4">Monitoring Data</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="monitoring-data">
                    <!-- Monitoring data will be dynamically inserted here -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load infrastructure list
    loadInfrastructureList();
    
    // Set up form submission
    document.getElementById('add-infrastructure-form').addEventListener('submit', function(e) {
        e.preventDefault();
        addNewInfrastructure(this);
    });
});

function loadInfrastructureList() {
    fetch('/api/infrastructure/')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('infrastructure-list');
            container.innerHTML = '';
            
            data.forEach(infrastructure => {
                container.appendChild(createInfrastructureCard(infrastructure));
            });
        })
        .catch(error => {
            console.error('Error loading infrastructure:', error);
            showNotification('Failed to load infrastructure list', 'error');
        });
}

function createInfrastructureCard(infrastructure) {
    const card = document.createElement('div');
    card.className = 'glass-card p-6 cursor-pointer hover:bg-opacity-20 transition-all duration-300';
    card.onclick = () => showInfrastructureDetails(infrastructure.id);
    
    card.innerHTML = `
        <div class="flex justify-between items-start mb-4">
            <div>
                <h4 class="text-white text-lg font-semibold">${infrastructure.name}</h4>
                <p class="text-blue-200 text-sm">${infrastructure.location}</p>
            </div>
            <div class="px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(infrastructure.status)}">
                ${infrastructure.status}
            </div>
        </div>
        
        <div class="space-y-2">
            <p class="text-blue-200 text-sm">
                <i class="fas fa-industry mr-2"></i>${infrastructure.type}
            </p>
            <p class="text-blue-200 text-sm">
                <i class="fas fa-calendar mr-2"></i>Installed: ${new Date(infrastructure.installation_date).toLocaleDateString()}
            </p>
        </div>
    `;
    
    return card;
}

function getStatusColor(status) {
    switch(status) {
        case 'ACTIVE':
            return 'bg-green-500 bg-opacity-20 text-green-300';
        case 'MAINTENANCE':
            return 'bg-yellow-500 bg-opacity-20 text-yellow-300';
        case 'INACTIVE':
            return 'bg-red-500 bg-opacity-20 text-red-300';
        default:
            return 'bg-gray-500 bg-opacity-20 text-gray-300';
    }
}

function showAddInfrastructureModal() {
    document.getElementById('add-infrastructure-modal').classList.remove('hidden');
    document.getElementById('add-infrastructure-modal').classList.add('flex');
}

function closeAddInfrastructureModal() {
    document.getElementById('add-infrastructure-modal').classList.add('hidden');
    document.getElementById('add-infrastructure-modal').classList.remove('flex');
}

function addNewInfrastructure(form) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    fetch('/api/infrastructure/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        closeAddInfrastructureModal();
        loadInfrastructureList();
        showNotification('Infrastructure added successfully', 'success');
    })
    .catch(error => {
        console.error('Error adding infrastructure:', error);
        showNotification('Failed to add infrastructure', 'error');
    });
}

function showInfrastructureDetails(infrastructureId) {
    fetch(`/api/infrastructure/${infrastructureId}/`)
        .then(response => response.json())
        .then(data => {
            updateInfrastructureDetails(data);
            document.getElementById('infrastructure-details-modal').classList.remove('hidden');
            document.getElementById('infrastructure-details-modal').classList.add('flex');
        })
        .catch(error => {
            console.error('Error loading infrastructure details:', error);
            showNotification('Failed to load infrastructure details', 'error');
        });
}

function closeInfrastructureDetailsModal() {
    document.getElementById('infrastructure-details-modal').classList.add('hidden');
    document.getElementById('infrastructure-details-modal').classList.remove('flex');
}

function updateInfrastructureDetails(data) {
    // Update details section
    const detailsContainer = document.getElementById('infrastructure-details');
    detailsContainer.innerHTML = `
        <div>
            <label class="text-cyan-300">Name</label>
            <p class="text-white">${data.name}</p>
        </div>
        <div>
            <label class="text-cyan-300">Type</label>
            <p class="text-white">${data.type}</p>
        </div>
        <div>
            <label class="text-cyan-300">Location</label>
            <p class="text-white">${data.location}</p>
        </div>
        <div>
            <label class="text-cyan-300">Status</label>
            <p class="${getStatusColor(data.status)}">${data.status}</p>
        </div>
        <div>
            <label class="text-cyan-300">Installation Date</label>
            <p class="text-white">${new Date(data.installation_date).toLocaleDateString()}</p>
        </div>
        <div>
            <label class="text-cyan-300">Description</label>
            <p class="text-white">${data.description}</p>
        </div>
    `;
    
    // Update control panel based on infrastructure type
    updateControlPanel(data);
    
    // Update monitoring data
    updateMonitoringData(data);
}

function updateControlPanel(data) {
    const controlPanel = document.getElementById('control-panel');
    
    switch(data.type) {
        case 'PUMP_HOUSE':
            controlPanel.innerHTML = `
                <div class="space-y-4">
                    <div>
                        <label class="text-cyan-300">Pump Control</label>
                        <div class="flex space-x-4 mt-2">
                            <button onclick="controlPump(${data.id}, 'start')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                                <i class="fas fa-play mr-2"></i>Start
                            </button>
                            <button onclick="controlPump(${data.id}, 'stop')" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg">
                                <i class="fas fa-stop mr-2"></i>Stop
                            </button>
                        </div>
                    </div>
                    <div>
                        <label class="text-cyan-300">Flow Rate</label>
                        <input type="range" min="0" max="100" value="50" class="w-full" onchange="adjustFlowRate(${data.id}, this.value)">
                    </div>
                </div>
            `;
            break;
            
        case 'RESERVOIR':
            controlPanel.innerHTML = `
                <div class="space-y-4">
                    <div>
                        <label class="text-cyan-300">Valve Control</label>
                        <div class="flex space-x-4 mt-2">
                            <button onclick="controlValve(${data.id}, 'open')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                                <i class="fas fa-faucet mr-2"></i>Open
                            </button>
                            <button onclick="controlValve(${data.id}, 'close')" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg">
                                <i class="fas fa-faucet mr-2"></i>Close
                            </button>
                        </div>
                    </div>
                </div>
            `;
            break;
            
        default:
            controlPanel.innerHTML = '<p class="text-white">No controls available for this infrastructure type.</p>';
    }
}

function updateMonitoringData(data) {
    const monitoringContainer = document.getElementById('monitoring-data');
    
    // Fetch real-time monitoring data
    fetch(`/api/infrastructure/${data.id}/monitoring/`)
        .then(response => response.json())
        .then(monitoringData => {
            let html = '';
            
            // Add monitoring cards based on infrastructure type
            switch(data.type) {
                case 'PUMP_HOUSE':
                    html += createMonitoringCard('Flow Rate', `${monitoringData.flow_rate} L/s`, 'fa-tachometer-alt');
                    html += createMonitoringCard('Pressure', `${monitoringData.pressure} bar`, 'fa-compress-alt');
                    html += createMonitoringCard('Motor Temp', `${monitoringData.motor_temp}°C`, 'fa-thermometer-half');
                    break;
                    
                case 'RESERVOIR':
                    html += createMonitoringCard('Level', `${monitoringData.level}%`, 'fa-tint');
                    html += createMonitoringCard('Volume', `${monitoringData.volume} L`, 'fa-flask');
                    html += createMonitoringCard('Inflow', `${monitoringData.inflow} L/s`, 'fa-arrow-down');
                    break;
            }
            
            monitoringContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading monitoring data:', error);
            monitoringContainer.innerHTML = '<p class="text-white">Failed to load monitoring data</p>';
        });
}

function createMonitoringCard(title, value, icon) {
    return `
        <div class="glass-card p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-cyan-300 text-sm">${title}</p>
                    <p class="text-white text-xl font-semibold">${value}</p>
                </div>
                <i class="fas ${icon} text-cyan-300 text-xl"></i>
            </div>
        </div>
    `;
}

// Control functions
function controlPump(infrastructureId, action) {
    fetch(`/api/infrastructure/${infrastructureId}/control/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({ action: action })
    })
    .then(response => response.json())
    .then(data => {
        showNotification(`Pump ${action} command sent successfully`, 'success');
        updateMonitoringData({ id: infrastructureId });
    })
    .catch(error => {
        console.error('Error controlling pump:', error);
        showNotification('Failed to control pump', 'error');
    });
}

function adjustFlowRate(infrastructureId, value) {
    fetch(`/api/infrastructure/${infrastructureId}/control/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({ action: 'adjust_flow', value: value })
    })
    .then(response => response.json())
    .then(data => {
        showNotification('Flow rate adjusted successfully', 'success');
        updateMonitoringData({ id: infrastructureId });
    })
    .catch(error => {
        console.error('Error adjusting flow rate:', error);
        showNotification('Failed to adjust flow rate', 'error');
    });
}

function controlValve(infrastructureId, action) {
    fetch(`/api/infrastructure/${infrastructureId}/control/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({ action: action })
    })
    .then(response => response.json())
    .then(data => {
        showNotification(`Valve ${action} command sent successfully`, 'success');
        updateMonitoringData({ id: infrastructureId });
    })
    .catch(error => {
        console.error('Error controlling valve:', error);
        showNotification('Failed to control valve', 'error');
    });
}
</script>
{% endblock %} 