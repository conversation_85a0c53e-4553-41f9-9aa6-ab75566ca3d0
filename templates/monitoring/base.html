<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Hydro System Guard SCADA - Nyamandlovu Station{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary-dark': '#1e3a8a',
                        'primary': '#3b82f6',
                        'primary-light': '#93c5fd',
                        'secondary': '#06b6d4',
                        'accent': '#10b981',
                        'accent-dark': '#059669'
                    }
                }
            }
        }
    </script>
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 25%, #1e40af 50%, #1d4ed8 75%, #2563eb 100%);
        }
        .water-animation {
            background: linear-gradient(-45deg, #0c4a6e, #0369a1, #0284c7, #0ea5e9);
            background-size: 400% 400%;
            animation: wave 4s ease-in-out infinite;
        }
        @keyframes wave {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        .float-animation {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .pulse-glow {
            animation: pulse-glow 2s ease-in-out infinite alternate;
        }
        @keyframes pulse-glow {
            from { box-shadow: 0 0 20px rgba(14, 165, 233, 0.5); }
            to { box-shadow: 0 0 30px rgba(14, 165, 233, 0.8), 0 0 40px rgba(14, 165, 233, 0.3); }
        }
        .feature-card {
            backdrop-filter: blur(10px);
            background: rgba(15, 23, 42, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glass-card {
            background: rgba(15, 23, 42, 0.7);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 0.5rem;
        }
        .glass-panel {
            background: rgba(15, 23, 42, 0.7);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 0.75rem;
        }
        /* Enhanced text contrast */
        .text-white {
            color: rgba(255, 255, 255, 0.95) !important;
        }
        .text-blue-200 {
            color: rgba(191, 219, 254, 0.95) !important;
        }
        .text-cyan-300 {
            color: rgba(103, 232, 249, 0.95) !important;
        }
        /* Enhanced card backgrounds */
        .bg-white.bg-opacity-10 {
            background-color: rgba(15, 23, 42, 0.7) !important;
        }
        .bg-white.bg-opacity-5 {
            background-color: rgba(15, 23, 42, 0.5) !important;
        }
        /* Enhanced borders */
        .border-white.border-opacity-20 {
            border-color: rgba(255, 255, 255, 0.3) !important;
        }
        /* Enhanced button styles */
        .bg-cyan-500 {
            background-color: #0ea5e9 !important;
        }
        .hover\:bg-cyan-600:hover {
            background-color: #0284c7 !important;
        }
        /* Enhanced status indicators */
        .bg-green-500.bg-opacity-20 {
            background-color: rgba(34, 197, 94, 0.3) !important;
        }
        .text-green-300 {
            color: rgba(74, 222, 128, 0.95) !important;
        }
        .bg-red-500.bg-opacity-20 {
            background-color: rgba(239, 68, 68, 0.3) !important;
        }
        .text-red-300 {
            color: rgba(248, 113, 113, 0.95) !important;
        }
        .bg-yellow-500.bg-opacity-20 {
            background-color: rgba(234, 179, 8, 0.3) !important;
        }
        .text-yellow-300 {
            color: rgba(253, 224, 71, 0.95) !important;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="gradient-bg min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg border-b border-white border-opacity-20">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                    <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center pulse-glow">
                        <i class="fas fa-tint text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-white">Hydro System Guard</h1>
                        <p class="text-sm text-blue-200">Nyamandlovu Water Station SCADA</p>
                    </div>
                </div>
                
                {% if user.is_authenticated %}
                <div class="flex items-center space-x-6">
                    <a href="{% url 'monitoring:dashboard' %}" class="text-white hover:text-blue-200 {% if request.resolver_match.url_name == 'dashboard' %}text-cyan-300 font-semibold{% endif %}">
                        <i class="fas fa-tachometer-alt mr-1"></i> Dashboard
                    </a>
                    <a href="{% url 'monitoring:alerts' %}" class="text-white hover:text-blue-200 {% if request.resolver_match.url_name == 'alerts' %}text-cyan-300 font-semibold{% endif %}">
                        <i class="fas fa-exclamation-triangle mr-1"></i> Alerts
                    </a>
                    <a href="{% url 'monitoring:water-quality' %}" class="text-white hover:text-blue-200 {% if request.resolver_match.url_name == 'water-quality' %}text-cyan-300 font-semibold{% endif %}">
                        <i class="fas fa-flask mr-1"></i> Water Quality
                    </a>
                    <a href="{% url 'monitoring:reports' %}" class="text-white hover:text-blue-200 {% if request.resolver_match.url_name == 'reports' %}text-cyan-300 font-semibold{% endif %}">
                        <i class="fas fa-chart-bar mr-1"></i> Reports
                    </a>
                    
                    <div class="flex items-center space-x-2 ml-6 pl-6 border-l border-blue-600">
                        <i class="fas fa-user text-white"></i>
                        <span class="text-white">{{ user.username }}</span>
                        <a href="{% url 'monitoring:logout' %}" class="bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-sm text-white">
                            <i class="fas fa-sign-out-alt mr-1"></i> Logout
                        </a>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="min-h-screen">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-black bg-opacity-30 backdrop-filter backdrop-blur-lg text-white py-6 mt-8 border-t border-white border-opacity-10">
        <div class="max-w-7xl mx-auto px-4 text-center">
            <p>&copy; 2025 Hydro System Guard SCADA - Nyamandlovu Water Pumping Station</p>
            <p class="text-sm text-blue-200 mt-1">Real-time monitoring and control system for water infrastructure</p>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 backdrop-filter backdrop-blur-sm flex items-center justify-center hidden z-50">
        <div class="bg-white bg-opacity-20 backdrop-filter backdrop-blur-lg rounded-lg p-6 text-center border border-white border-opacity-30">
            <i class="fas fa-spinner fa-spin text-3xl text-cyan-300 mb-4"></i>
            <p class="text-lg font-semibold text-white">Updating data...</p>
        </div>
    </div>

    <!-- Notification Area -->
    <div id="notification-area" class="fixed top-4 right-4 z-40"></div>

    <!-- JavaScript -->
    <script>
        // Global CSRF token for AJAX requests
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        
        const csrftoken = getCookie('csrftoken');
        
        // Set CSRF token for all AJAX requests
        function setupCSRF() {
            const token = document.querySelector('[name=csrfmiddlewaretoken]');
            if (token) {
                document.querySelector('meta[name="csrf-token"]').setAttribute('content', token.value);
            }
        }

        // Custom notification function
        function showNotification(message, type = 'info') {
            const notificationArea = document.getElementById('notification-area');
            const notificationId = 'notification-' + Date.now();
            
            const colors = {
                'success': 'bg-green-500 bg-opacity-20 border-green-400',
                'error': 'bg-red-500 bg-opacity-20 border-red-400',
                'warning': 'bg-yellow-500 bg-opacity-20 border-yellow-400',
                'info': 'bg-blue-500 bg-opacity-20 border-blue-400'
            };
            
            const icons = {
                'success': 'fa-check-circle',
                'error': 'fa-exclamation-circle',
                'warning': 'fa-exclamation-triangle',
                'info': 'fa-info-circle'
            };
            
            const notification = document.createElement('div');
            notification.id = notificationId;
            notification.className = `border px-4 py-3 rounded-lg mb-2 backdrop-filter backdrop-blur-lg text-white ${colors[type] || colors.info}`;
            notification.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas ${icons[type] || icons.info} mr-2"></i>
                        <span>${message}</span>
                    </div>
                    <button onclick="document.getElementById('${notificationId}').remove()" class="text-xl leading-none">&times;</button>
                </div>
            `;
            
            notificationArea.appendChild(notification);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (document.getElementById(notificationId)) {
                    document.getElementById(notificationId).remove();
                }
            }, 5000);
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>