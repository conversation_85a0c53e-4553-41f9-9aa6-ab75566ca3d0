{% extends "monitoring/base.html" %}

{% block title %}Reports - Hydro System Guard SCADA{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-3xl font-bold text-white">System Reports</h2>
            <p class="text-blue-200">Generate and view system performance reports</p>
        </div>
        <div>
            <button id="generate-report-btn" class="bg-cyan-500 hover:bg-cyan-600 text-white px-4 py-2 rounded-lg transition duration-300 transform hover:scale-105 pulse-glow">
                <i class="fas fa-file-export mr-2"></i>Generate Report
            </button>
        </div>
    </div>

    <!-- Report Configuration -->
    <div class="glass-panel mb-8">
        <div class="px-6 py-4 border-b border-white border-opacity-20">
            <h3 class="text-lg font-semibold text-white">Report Configuration</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="report-type" class="block text-sm font-medium text-blue-200 mb-1">Report Type</label>
                    <select id="report-type" class="bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg block w-full px-3 py-2 text-white">
                        <option value="performance">System Performance</option>
                        <option value="water-quality">Water Quality</option>
                        <option value="alerts">Alert History</option>
                        <option value="maintenance">Maintenance Log</option>
                        <option value="compliance">Compliance Report</option>
                    </select>
                </div>
                <div>
                    <label for="date-range" class="block text-sm font-medium text-blue-200 mb-1">Date Range</label>
                    <select id="date-range" class="bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg block w-full px-3 py-2 text-white">
                        <option value="today">Today</option>
                        <option value="yesterday">Yesterday</option>
                        <option value="last-7-days" selected>Last 7 Days</option>
                        <option value="last-30-days">Last 30 Days</option>
                        <option value="this-month">This Month</option>
                        <option value="last-month">Last Month</option>
                        <option value="custom">Custom Range</option>
                    </select>
                </div>
                <div>
                    <label for="report-format" class="block text-sm font-medium text-blue-200 mb-1">Format</label>
                    <select id="report-format" class="bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg block w-full px-3 py-2 text-white">
                        <option value="pdf">PDF Document (summary, tables, charts, alerts)</option>
                        <option value="csv">CSV (raw data, for Excel)</option>
                    </select>
                </div>
            </div>
            <div class="mt-4 text-blue-200 text-sm">
                <strong>PDF:</strong> Includes summary tables, charts, and recent alerts.<br>
                <strong>CSV:</strong> Includes raw pump/system data for further analysis.
            </div>
            
            <!-- Custom date range (hidden by default) -->
            <div id="custom-date-container" class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 hidden">
                <div>
                    <label for="start-date" class="block text-sm font-medium text-blue-200 mb-1">Start Date</label>
                    <input type="date" id="start-date" class="bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg block w-full px-3 py-2 text-white">
                </div>
                <div>
                    <label for="end-date" class="block text-sm font-medium text-blue-200 mb-1">End Date</label>
                    <input type="date" id="end-date" class="bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg block w-full px-3 py-2 text-white">
                </div>
            </div>
            
            <!-- Additional options based on report type -->
            <div id="performance-options" class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="pump-selection" class="block text-sm font-medium text-blue-200 mb-1">Pump Selection</label>
                    <select id="pump-selection" class="bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg block w-full px-3 py-2 text-white">
                        <option value="all">All Pumps</option>
                        <option value="pump-1">Pump 1</option>
                        <option value="pump-2">Pump 2</option>
                        <option value="pump-3">Pump 3</option>
                    </select>
                </div>
                <div>
                    <label for="metrics-selection" class="block text-sm font-medium text-blue-200 mb-1">Metrics to Include</label>
                    <div class="mt-1 space-y-2">
                        <div class="flex items-center">
                            <input id="metric-flow" type="checkbox" checked class="h-4 w-4 text-cyan-500 focus:ring-cyan-400 border-white border-opacity-20 rounded bg-white bg-opacity-10">
                            <label for="metric-flow" class="ml-2 text-sm text-white">Flow Rate</label>
                        </div>
                        <div class="flex items-center">
                            <input id="metric-pressure" type="checkbox" checked class="h-4 w-4 text-cyan-500 focus:ring-cyan-400 border-white border-opacity-20 rounded bg-white bg-opacity-10">
                            <label for="metric-pressure" class="ml-2 text-sm text-white">Pressure</label>
                        </div>
                        <div class="flex items-center">
                            <input id="metric-temperature" type="checkbox" checked class="h-4 w-4 text-cyan-500 focus:ring-cyan-400 border-white border-opacity-20 rounded bg-white bg-opacity-10">
                            <label for="metric-temperature" class="ml-2 text-sm text-white">Temperature</label>
                        </div>
                        <div class="flex items-center">
                            <input id="metric-power" type="checkbox" checked class="h-4 w-4 text-cyan-500 focus:ring-cyan-400 border-white border-opacity-20 rounded bg-white bg-opacity-10">
                            <label for="metric-power" class="ml-2 text-sm text-white">Power Consumption</label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div id="water-quality-options" class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 hidden">
                <div>
                    <label for="quality-parameters" class="block text-sm font-medium text-blue-200 mb-1">Parameters to Include</label>
                    <div class="mt-1 space-y-2">
                        <div class="flex items-center">
                            <input id="param-hardness" type="checkbox" checked class="h-4 w-4 text-cyan-500 focus:ring-cyan-400 border-white border-opacity-20 rounded bg-white bg-opacity-10">
                            <label for="param-hardness" class="ml-2 text-sm text-white">Water Hardness</label>
                        </div>
                        <div class="flex items-center">
                            <input id="param-ph" type="checkbox" checked class="h-4 w-4 text-cyan-500 focus:ring-cyan-400 border-white border-opacity-20 rounded bg-white bg-opacity-10">
                            <label for="param-ph" class="ml-2 text-sm text-white">pH Level</label>
                        </div>
                        <div class="flex items-center">
                            <input id="param-turbidity" type="checkbox" checked class="h-4 w-4 text-cyan-500 focus:ring-cyan-400 border-white border-opacity-20 rounded bg-white bg-opacity-10">
                            <label for="param-turbidity" class="ml-2 text-sm text-white">Turbidity</label>
                        </div>
                        <div class="flex items-center">
                            <input id="param-conductivity" type="checkbox" checked class="h-4 w-4 text-cyan-500 focus:ring-cyan-400 border-white border-opacity-20 rounded bg-white bg-opacity-10">
                            <label for="param-conductivity" class="ml-2 text-sm text-white">Conductivity</label>
                        </div>
                    </div>
                </div>
                <div>
                    <label for="quality-comparison" class="block text-sm font-medium text-blue-200 mb-1">Include Comparison</label>
                    <select id="quality-comparison" class="bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg block w-full px-3 py-2 text-white">
                        <option value="none">No Comparison</option>
                        <option value="previous-period">Previous Period</option>
                        <option value="standards">Water Quality Standards</option>
                    </select>
                </div>
            </div>
            
            <div id="alerts-options" class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 hidden">
                <div>
                    <label for="alert-severity" class="block text-sm font-medium text-blue-200 mb-1">Alert Severity</label>
                    <select id="alert-severity" class="bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg block w-full px-3 py-2 text-white">
                        <option value="all">All Severities</option>
                        <option value="critical">Critical Only</option>
                        <option value="warning">Warning & Critical</option>
                    </select>
                </div>
                <div>
                    <label for="alert-status" class="block text-sm font-medium text-blue-200 mb-1">Alert Status</label>
                    <select id="alert-status" class="bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg block w-full px-3 py-2 text-white">
                        <option value="all">All Statuses</option>
                        <option value="active">Active</option>
                        <option value="acknowledged">Acknowledged</option>
                        <option value="resolved">Resolved</option>
                    </select>
                </div>
            </div>
            
            <div id="compliance-options" class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 hidden">
                <div>
                    <label for="compliance-standard" class="block text-sm font-medium text-blue-200 mb-1">Compliance Standard</label>
                    <select id="compliance-standard" class="bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg block w-full px-3 py-2 text-white">
                        <option value="local">Local Water Authority</option>
                        <option value="national">National Standards</option>
                        <option value="who">WHO Guidelines</option>
                    </select>
                </div>
                <div>
                    <label for="compliance-detail" class="block text-sm font-medium text-blue-200 mb-1">Detail Level</label>
                    <select id="compliance-detail" class="bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg block w-full px-3 py-2 text-white">
                        <option value="summary">Summary</option>
                        <option value="detailed">Detailed</option>
                        <option value="comprehensive">Comprehensive</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Preview -->
    <div class="glass-panel mb-8">
        <div class="px-6 py-4 border-b border-white border-opacity-20">
            <h3 class="text-lg font-semibold text-white">Report Preview</h3>
        </div>
        <div class="p-6">
            <div id="report-preview" class="bg-white bg-opacity-5 rounded-lg p-6 min-h-[400px]">
                <div class="text-center py-20">
                    <i class="fas fa-file-alt text-blue-300 text-5xl mb-4"></i>
                    <p class="text-blue-200">Configure your report and click "Generate Report" to preview</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Reports -->
    <div class="glass-panel">
        <div class="px-6 py-4 border-b border-white border-opacity-20">
            <h3 class="text-lg font-semibold text-white">Recent Reports</h3>
        </div>
        <div class="p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-white divide-opacity-20">
                    <thead>
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-blue-200 uppercase tracking-wider">Date Generated</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-blue-200 uppercase tracking-wider">Report Type</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-blue-200 uppercase tracking-wider">Date Range</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-blue-200 uppercase tracking-wider">Format</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-blue-200 uppercase tracking-wider">Generated By</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-blue-200 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-white divide-opacity-10" id="recent-reports-table">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-white">2024-05-28 14:32</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-white">System Performance</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-white">Last 7 Days</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-white">PDF</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-white">admin</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <a href="#" class="text-cyan-300 hover:text-cyan-400 mr-3">
                                    <i class="fas fa-download mr-1"></i>Download
                                </a>
                                <a href="#" class="text-blue-300 hover:text-blue-400">
                                    <i class="fas fa-eye mr-1"></i>View
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-white">2024-05-27 09:15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-white">Water Quality</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-white">Last 30 Days</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-white">HTML</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-white">operator1</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <a href="#" class="text-blue-300 hover:text-blue-400">
                                    <i class="fas fa-eye mr-1"></i>View
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-white">2024-05-25 16:48</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-white">Compliance Report</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-white">This Month</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-white">PDF</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-white">admin</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <a href="#" class="text-cyan-300 hover:text-cyan-400 mr-3">
                                    <i class="fas fa-download mr-1"></i>Download
                                </a>
                                <a href="#" class="text-blue-300 hover:text-blue-400">
                                    <i class="fas fa-eye mr-1"></i>View
                                </a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get DOM elements
        const reportTypeSelect = document.getElementById('report-type');
        const dateRangeSelect = document.getElementById('date-range');
        const customDateContainer = document.getElementById('custom-date-container');
        const generateReportBtn = document.getElementById('generate-report-btn');
        const reportPreview = document.getElementById('report-preview');
        
        // Report type specific option containers
        const performanceOptions = document.getElementById('performance-options');
        const waterQualityOptions = document.getElementById('water-quality-options');
        const alertsOptions = document.getElementById('alerts-options');
        const complianceOptions = document.getElementById('compliance-options');
        
        // Event listeners
        dateRangeSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                customDateContainer.classList.remove('hidden');
            } else {
                customDateContainer.classList.add('hidden');
            }
        });
        
        reportTypeSelect.addEventListener('change', function() {
            // Hide all option containers first
            performanceOptions.classList.add('hidden');
            waterQualityOptions.classList.add('hidden');
            alertsOptions.classList.add('hidden');
            complianceOptions.classList.add('hidden');
            
            // Show the relevant container based on selection
            switch(this.value) {
                case 'performance':
                    performanceOptions.classList.remove('hidden');
                    break;
                case 'water-quality':
                    waterQualityOptions.classList.remove('hidden');
                    break;
                case 'alerts':
                    alertsOptions.classList.remove('hidden');
                    break;
                case 'compliance':
                    complianceOptions.classList.remove('hidden');
                    break;
            }
        });
        
        generateReportBtn.addEventListener('click', function() {
            // Show loading state
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Generating...';
            reportPreview.innerHTML = `
                <div class="text-center py-20">
                    <i class="fas fa-spinner fa-spin text-blue-300 text-5xl mb-4"></i>
                    <p class="text-blue-200">Generating report, please wait...</p>
                </div>
            `;
            
            // Collect report parameters
            const reportParams = {
                type: reportTypeSelect.value,
                dateRange: dateRangeSelect.value,
                format: document.getElementById('report-format').value
            };
            
            // Add custom date range if selected
            if (reportParams.dateRange === 'custom') {
                reportParams.startDate = document.getElementById('start-date').value;
                reportParams.endDate = document.getElementById('end-date').value;
            }
            
            // Add type-specific parameters
            switch(reportParams.type) {
                case 'performance':
                    reportParams.pump = document.getElementById('pump-selection').value;
                    reportParams.metrics = {
                        flow: document.getElementById('metric-flow').checked,
                        pressure: document.getElementById('metric-pressure').checked,
                        temperature: document.getElementById('metric-temperature').checked,
                        power: document.getElementById('metric-power').checked
                    };
                    break;
                case 'water-quality':
                    reportParams.parameters = {
                        hardness: document.getElementById('param-hardness').checked,
                        ph: document.getElementById('param-ph').checked,
                        turbidity: document.getElementById('param-turbidity').checked,
                        conductivity: document.getElementById('param-conductivity').checked
                    };
                    reportParams.comparison = document.getElementById('quality-comparison').value;
                    break;
                case 'alerts':
                    reportParams.severity = document.getElementById('alert-severity').value;
                    reportParams.status = document.getElementById('alert-status').value;
                    break;
                case 'compliance':
                    reportParams.standard = document.getElementById('compliance-standard').value;
                    reportParams.detail = document.getElementById('compliance-detail').value;
                    break;
            }
            
            // Send request to generate report
            fetch('/api/reports/generate/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify(reportParams)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Update preview with report data
                generateReportPreview(data);
                
                // Reset button state
                generateReportBtn.disabled = false;
                generateReportBtn.innerHTML = '<i class="fas fa-file-export mr-2"></i>Generate Report';
                
                // Show success notification
                showNotification('Report generated successfully', 'success');
            })
            .catch(error => {
                console.error('Error generating report:', error);
                
                // Show error in preview
                reportPreview.innerHTML = `
                    <div class="text-center py-20">
                        <i class="fas fa-exclamation-circle text-red-400 text-5xl mb-4"></i>
                        <p class="text-red-300">Failed to generate report. Please try again.</p>
                    </div>
                `;
                
                // Reset button state
                generateReportBtn.disabled = false;
                generateReportBtn.innerHTML = '<i class="fas fa-file-export mr-2"></i>Generate Report';
                
                // Show error notification
                showNotification('Failed to generate report', 'error');
            });
        });
        
        // Function to generate report preview based on data
        function generateReportPreview(data) {
            // For demonstration, we'll create a simple preview
            // In a real implementation, this would render the actual report data
            
            let previewHTML = `
                <div class="text-center mb-6">
                    <h2 class="text-2xl font-bold text-white">${getReportTypeName(data.type)} Report</h2>
                    <p class="text-blue-200">${getDateRangeText(data.dateRange, data.startDate, data.endDate)}</p>
                </div>
            `;
            
            // Add report content based on type
            switch(data.type) {
                case 'performance':
                    previewHTML += generatePerformancePreview(data);
                    break;
                case 'water-quality':
                    previewHTML += generateWaterQualityPreview(data);
                    break;
                case 'alerts':
                    previewHTML += generateAlertsPreview(data);
                    break;
                case 'compliance':
                    previewHTML += generateCompliancePreview(data);
                    break;
            }
            
            // Add download button if not HTML format
            if (data.format !== 'html') {
                previewHTML += `
                    <div class="text-center mt-8">
                        <a href="${data.downloadUrl}" class="bg-cyan-500 hover:bg-cyan-600 text-white px-4 py-2 rounded-lg transition duration-300">
                            <i class="fas fa-download mr-2"></i>Download ${data.format.toUpperCase()}
                        </a>
                    </div>
                `;
            }
            
            reportPreview.innerHTML = previewHTML;
        }
        
        // Helper functions for report preview generation
        function getReportTypeName(type) {
            const typeNames = {
                'performance': 'System Performance',
                'water-quality': 'Water Quality',
                'alerts': 'Alert History',
                'maintenance': 'Maintenance Log',
                'compliance': 'Compliance'
            };
            return typeNames[type] || type;
        }
        
        function getDateRangeText(range, startDate, endDate) {
            const rangeTexts = {
                'today': 'Today',
                'yesterday': 'Yesterday',
                'last-7-days': 'Last 7 Days',
                'last-30-days': 'Last 30 Days',
                'this-month': 'This Month',
                'last-month': 'Last Month'
            };
            
            if (range === 'custom' && startDate && endDate) {
                return `${formatDate(startDate)} to ${formatDate(endDate)}`;
            }
            
            return rangeTexts[range] || range;
        }
        
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString();
        }
        
        // Placeholder functions for different report type previews
        function generatePerformancePreview(data) {
            return `
                <div class="bg-white bg-opacity-10 rounded-lg p-4 mb-4">
                    <h3 class="text-lg font-semibold text-white mb-2">Summary</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-white bg-opacity-5 p-3 rounded">
                            <p class="text-blue-200 text-sm">Average Flow Rate</p>
                            <p class="text-white text-2xl font-bold">52.7 L/s</p>
                        </div>
                        <div class="bg-white bg-opacity-5 p-3 rounded">
                            <p class="text-blue-200 text-sm">Average Pressure</p>
                            <p class="text-white text-2xl font-bold">4.8 bar</p>
                        </div>
                        <div class="bg-white bg-opacity-5 p-3 rounded">
                            <p class="text-blue-200 text-sm">Total Running Hours</p>
                            <p class="text-white text-2xl font-bold">156.3 hrs</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white bg-opacity-10 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-white mb-2">Performance Charts</h3>
                    <p class="text-blue-200 text-sm mb-4">Charts would be displayed here in the actual report</p>
                    <div class="h-40 bg-white bg-opacity-5 rounded flex items-center justify-center">
                        <p class="text-blue-200"><i class="fas fa-chart-line mr-2"></i>Flow Rate Chart</p>
                    </div>
                </div>
            `;
        }
        
        function generateWaterQualityPreview(data) {
            return `
                <div class="bg-white bg-opacity-10 rounded-lg p-4 mb-4">
                    <h3 class="text-lg font-semibold text-white mb-2">Water Quality Summary</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-white bg-opacity-5 p-3 rounded">
                            <p class="text-blue-200 text-sm">Average Hardness</p>
                            <p class="text-white text-2xl font-bold">345 mg/L</p>
                        </div>
                        <div class="bg-white bg-opacity-5 p-3 rounded">
                            <p class="text-blue-200 text-sm">Average pH</p>
                            <p class="text-white text-2xl font-bold">7.6</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white bg-opacity-10 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-white mb-2">Quality Trends</h3>
                    <p class="text-blue-200 text-sm mb-4">Trend charts would be displayed here in the actual report</p>
                    <div class="h-40 bg-white bg-opacity-5 rounded flex items-center justify-center">
                        <p class="text-blue-200"><i class="fas fa-chart-area mr-2"></i>Water Quality Trend Chart</p>
                    </div>
                </div>
            `;
        }
        
        function generateAlertsPreview(data) {
            return `
                <div class="bg-white bg-opacity-10 rounded-lg p-4 mb-4">
                    <h3 class="text-lg font-semibold text-white mb-2">Alert Summary</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-white bg-opacity-5 p-3 rounded">
                            <p class="text-blue-200 text-sm">Total Alerts</p>
                            <p class="text-white text-2xl font-bold">24</p>
                        </div>
                        <div class="bg-white bg-opacity-5 p-3 rounded">
                            <p class="text-blue-200 text-sm">Critical Alerts</p>
                            <p class="text-white text-2xl font-bold">3</p>
                        </div>
                        <div class="bg-white bg-opacity-5 p-3 rounded">
                            <p class="text-blue-200 text-sm">Average Resolution Time</p>
                            <p class="text-white text-2xl font-bold">1.2 hrs</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white bg-opacity-10 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-white mb-2">Alert Distribution</h3>
                    <p class="text-blue-200 text-sm mb-4">Alert distribution charts would be displayed here in the actual report</p>
                    <div class="h-40 bg-white bg-opacity-5 rounded flex items-center justify-center">
                        <p class="text-blue-200"><i class="fas fa-chart-pie mr-2"></i>Alert Distribution Chart</p>
                    </div>
                </div>
            `;
        }
        
        function generateCompliancePreview(data) {
            return `
                <div class="bg-white bg-opacity-10 rounded-lg p-4 mb-4">
                    <h3 class="text-lg font-semibold text-white mb-2">Compliance Summary</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-white bg-opacity-5 p-3 rounded">
                            <p class="text-blue-200 text-sm">Compliance Rate</p>
                            <p class="text-white text-2xl font-bold">98.5%</p>
                        </div>
                        <div class="bg-white bg-opacity-5 p-3 rounded">
                            <p class="text-blue-200 text-sm">Non-Compliance Events</p>
                            <p class="text-white text-2xl font-bold">2</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white bg-opacity-10 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-white mb-2">Compliance Details</h3>
                    <table class="min-w-full divide-y divide-white divide-opacity-20">
                        <thead>
                            <tr>
                                <th class="px-3 py-2 text-left text-xs font-medium text-blue-200 uppercase">Parameter</th>
                                <th class="px-3 py-2 text-left text-xs font-medium text-blue-200 uppercase">Standard</th>
                                <th class="px-3 py-2 text-left text-xs font-medium text-blue-200 uppercase">Actual</th>
                                <th class="px-3 py-2 text-left text-xs font-medium text-blue-200 uppercase">Status</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-white divide-opacity-10">
                            <tr>
                                <td class="px-3 py-2 text-sm text-white">pH</td>
                                <td class="px-3 py-2 text-sm text-white">6.5-8.5</td>
                                <td class="px-3 py-2 text-sm text-white">7.6</td>
                                <td class="px-3 py-2 text-sm"><span class="px-2 py-1 bg-green-500 bg-opacity-20 text-green-300 rounded-full text-xs">Compliant</span></td>
                            </tr>
                            <tr>
                                <td class="px-3 py-2 text-sm text-white">Hardness</td>
                                <td class="px-3 py-2 text-sm text-white">< 500 mg/L</td>
                                <td class="px-3 py-2 text-sm text-white">345 mg/L</td>
                                <td class="px-3 py-2 text-sm"><span class="px-2 py-1 bg-green-500 bg-opacity-20 text-green-300 rounded-full text-xs">Compliant</span></td>
                            </tr>
                            <tr>
                                <td class="px-3 py-2 text-sm text-white">Turbidity</td>
                                <td class="px-3 py-2 text-sm text-white">< 5 NTU</td>
                                <td class="px-3 py-2 text-sm text-white">1.2 NTU</td>
                                <td class="px-3 py-2 text-sm"><span class="px-2 py-1 bg-green-500 bg-opacity-20 text-green-300 rounded-full text-xs">Compliant</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            `;
        }
        
        // Function to show notifications
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 transform transition-transform duration-300 ease-in-out translate-x-0`;
            
            // Set background color based on type
            if (type === 'success') {
                notification.classList.add('bg-green-500', 'text-white');
                notification.innerHTML = `<i class="fas fa-check-circle mr-2"></i>${message}`;
            } else if (type === 'error') {
                notification.classList.add('bg-red-500', 'text-white');
                notification.innerHTML = `<i class="fas fa-exclamation-circle mr-2"></i>${message}`;
            } else {
                notification.classList.add('bg-blue-500', 'text-white');
                notification.innerHTML = `<i class="fas fa-info-circle mr-2"></i>${message}`;
            }
            
            // Add to DOM
            document.body.appendChild(notification);
            
            // Remove after 3 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
        
        // Initialize the page - show performance options by default
        performanceOptions.classList.remove('hidden');
    });
</script>
{% endblock %}
