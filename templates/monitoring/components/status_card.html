{% comment %}
Usage: {% include "monitoring/components/status_card.html" with title="Pump Status" value="Running" icon="fa-cogs" status="success" %}
Statuses: success, warning, danger, info, neutral
{% endcomment %}

<div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center">
        <div class="p-2 rounded-lg
            {% if status == 'success' %}bg-green-100{% elif status == 'warning' %}bg-yellow-100
            {% elif status == 'danger' %}bg-red-100{% elif status == 'info' %}bg-blue-100
            {% else %}bg-gray-100{% endif %}">
            <i class="fas {{ icon|default:'fa-info-circle' }} text-xl
                {% if status == 'success' %}text-green-600{% elif status == 'warning' %}text-yellow-600
                {% elif status == 'danger' %}text-red-600{% elif status == 'info' %}text-blue-600
                {% else %}text-gray-600{% endif %}"></i>
        </div>
        <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">{{ title }}</p>
            <p class="text-2xl font-bold text-gray-900">{{ value|default:"--" }}</p>
            {% if subtitle %}<p class="text-xs text-gray-500">{{ subtitle }}</p>{% endif %}
        </div>
    </div>
</div>