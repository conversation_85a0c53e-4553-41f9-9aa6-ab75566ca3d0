{% extends 'monitoring/base.html' %}
{% block title %}System Performance Report{% endblock %}
{% block content %}
<div class="report-container" style="max-width: 800px; margin: 0 auto; padding: 32px; background: #fff; color: #222;">
    <h1 style="font-size: 2em; margin-bottom: 0.5em;">System Performance Report</h1>
    <p style="color: #666;">Date: {{ date }}</p>
    <hr style="margin: 1.5em 0;">
    <h2>Summary</h2>
    <table border="1" cellpadding="8" cellspacing="0" style="width: 100%; margin-bottom: 2em;">
        <tr><th>Metric</th><th>Value</th></tr>
        <tr><td>Average Flow Rate (L/s)</td><td>{{ summary.avg_flow_rate }}</td></tr>
        <tr><td>Average Pressure (bar)</td><td>{{ summary.avg_pressure }}</td></tr>
        <tr><td>Total Running Hours</td><td>{{ summary.total_running_hours }}</td></tr>
    </table>
    <h2>Recent Alerts</h2>
    <table border="1" cellpadding="8" cellspacing="0" style="width: 100%; margin-bottom: 2em;">
        <tr><th>Time</th><th>Type</th><th>Message</th></tr>
        {% for alert in alerts %}
        <tr>
            <td>{{ alert.created_at|date:'Y-m-d H:i' }}</td>
            <td>{{ alert.get_type_display }}</td>
            <td>{{ alert.message }}</td>
        </tr>
        {% empty %}
        <tr><td colspan="3">No recent alerts</td></tr>
        {% endfor %}
    </table>
    <h2>Performance Chart (Last 24h)</h2>
    {% if chart_path %}
    <img src="{{ chart_path }}" alt="Performance Chart" style="width: 100%; max-width: 600px; margin-bottom: 2em;" />
    {% endif %}
</div>
{% endblock %} 