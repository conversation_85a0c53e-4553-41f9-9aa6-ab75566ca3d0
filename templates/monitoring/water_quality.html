{% extends "monitoring/base.html" %}

{% block title %}Water Quality - Hydro System Guard SCADA{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-3xl font-bold text-white">Water Quality Monitoring</h2>
            <p class="text-blue-200">Monitor water quality parameters critical for pump longevity</p>
        </div>
        <div class="flex items-center space-x-4">
            <div class="text-sm text-blue-200" id="water-quality-last-updated">
                Last updated: --:--:--
            </div>
            <button id="refresh-btn" class="bg-cyan-500 hover:bg-cyan-600 text-white px-4 py-2 rounded-lg transition duration-300 transform hover:scale-105 pulse-glow">
                <i class="fas fa-sync-alt mr-2"></i>Refresh
            </button>
        </div>
    </div>

    <!-- Current Readings Panel -->
    <div class="glass-panel mb-8">
        <div class="px-6 py-4 border-b border-white border-opacity-20">
            <h3 class="text-lg font-semibold text-white">Current Readings</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                <!-- Hardness -->
                <div class="bg-white bg-opacity-10 rounded-lg p-4 border border-white border-opacity-10">
                    <div class="flex items-center justify-between mb-2">
                        <div class="text-blue-200">
                            <i class="fas fa-tint text-xl"></i>
                        </div>
                        <div id="hardness-status" class="px-3 py-1 rounded-full text-xs font-medium bg-green-500 bg-opacity-20 text-green-300">
                            Normal
                        </div>
                    </div>
                    <p class="text-blue-200 text-sm">Hardness</p>
                    <p class="text-white text-2xl font-bold" id="current-hardness">-- mg/L</p>
                    <p class="text-blue-200 text-xs">Threshold: <span id="hardness-threshold">400 mg/L</span></p>
                </div>
                
                <!-- pH Level -->
                <div class="bg-white bg-opacity-10 rounded-lg p-4 border border-white border-opacity-10">
                    <div class="flex items-center justify-between mb-2">
                        <div class="text-blue-200">
                            <i class="fas fa-vial text-xl"></i>
                        </div>
                        <div id="ph-status" class="px-3 py-1 rounded-full text-xs font-medium bg-green-500 bg-opacity-20 text-green-300">
                            Normal
                        </div>
                    </div>
                    <p class="text-blue-200 text-sm">pH Level</p>
                    <p class="text-white text-2xl font-bold" id="current-ph">--</p>
                    <p class="text-blue-200 text-xs">Ideal Range: <span id="ph-range">6.5 - 8.5</span></p>
                </div>
                
                <!-- Turbidity -->
                <div class="bg-white bg-opacity-10 rounded-lg p-4 border border-white border-opacity-10">
                    <div class="flex items-center justify-between mb-2">
                        <div class="text-blue-200">
                            <i class="fas fa-water text-xl"></i>
                        </div>
                        <div id="turbidity-status" class="px-3 py-1 rounded-full text-xs font-medium bg-green-500 bg-opacity-20 text-green-300">
                            Normal
                        </div>
                    </div>
                    <p class="text-blue-200 text-sm">Turbidity</p>
                    <p class="text-white text-2xl font-bold" id="current-turbidity">-- NTU</p>
                    <p class="text-blue-200 text-xs">Threshold: <span id="turbidity-threshold">5 NTU</span></p>
                </div>
                
                <!-- Temperature -->
                <div class="bg-white bg-opacity-10 rounded-lg p-4 border border-white border-opacity-10">
                    <div class="flex items-center justify-between mb-2">
                        <div class="text-blue-200">
                            <i class="fas fa-temperature-high text-xl"></i>
                        </div>
                        <div id="temperature-status" class="px-3 py-1 rounded-full text-xs font-medium bg-green-500 bg-opacity-20 text-green-300">
                            Normal
                        </div>
                    </div>
                    <p class="text-blue-200 text-sm">Temperature</p>
                    <p class="text-white text-2xl font-bold" id="current-temperature">-- °C</p>
                    <p class="text-blue-200 text-xs">Ideal Range: <span id="temperature-range">15 - 25°C</span></p>
                </div>
                
                <!-- Conductivity -->
                <div class="bg-white bg-opacity-10 rounded-lg p-4 border border-white border-opacity-10">
                    <div class="flex items-center justify-between mb-2">
                        <div class="text-blue-200">
                            <i class="fas fa-bolt text-xl"></i>
                        </div>
                        <div id="conductivity-status" class="px-3 py-1 rounded-full text-xs font-medium bg-green-500 bg-opacity-20 text-green-300">
                            Normal
                        </div>
                    </div>
                    <p class="text-blue-200 text-sm">Conductivity</p>
                    <p class="text-white text-2xl font-bold" id="current-conductivity">-- µS/cm</p>
                    <p class="text-blue-200 text-xs">Threshold: <span id="conductivity-threshold">500 µS/cm</span></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Historical Data Chart -->
    <div class="glass-panel">
        <div class="px-6 py-4 border-b border-white border-opacity-20">
            <h3 class="text-lg font-semibold text-white">Historical Trends</h3>
        </div>
        <div class="p-6">
            <div class="bg-white bg-opacity-5 rounded-lg p-4">
                <div class="flex justify-end mb-4">
                    <div class="inline-flex rounded-md shadow-sm" role="group">
                        <button type="button" class="time-range-btn active px-4 py-2 text-sm font-medium text-blue-200 bg-white bg-opacity-10 rounded-l-lg border border-white border-opacity-10 hover:bg-white hover:bg-opacity-20 focus:z-10 focus:ring-2 focus:ring-blue-500 focus:text-white" data-range="24h">24h</button>
                        <button type="button" class="time-range-btn px-4 py-2 text-sm font-medium text-blue-200 bg-white bg-opacity-10 border-t border-b border-white border-opacity-10 hover:bg-white hover:bg-opacity-20 focus:z-10 focus:ring-2 focus:ring-blue-500 focus:text-white" data-range="7d">7d</button>
                        <button type="button" class="time-range-btn px-4 py-2 text-sm font-medium text-blue-200 bg-white bg-opacity-10 rounded-r-lg border border-white border-opacity-10 hover:bg-white hover:bg-opacity-20 focus:z-10 focus:ring-2 focus:ring-blue-500 focus:text-white" data-range="30d">30d</button>
                    </div>
                </div>
                <div class="chart-container" style="position: relative; height: 300px;">
                    <canvas id="water-quality-chart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // DOM elements
        const refreshBtn = document.getElementById('refresh-btn');
        const lastUpdatedEl = document.getElementById('water-quality-last-updated');
        const timeRangeBtns = document.querySelectorAll('.time-range-btn');
        
        // Chart instance
        let qualityChart;
        
        // Current time range
        let currentTimeRange = '24h';
        
        // Initialize the page
        initWaterQualityPage();
        
        // Set up auto-refresh (every 30 seconds)
        const refreshInterval = setInterval(fetchWaterQualityData, 30000);
        
        // Manual refresh button
        refreshBtn.addEventListener('click', function() {
            fetchWaterQualityData();
            showNotification('Water quality data refreshed', 'info');
        });
        
        // Time range buttons
        timeRangeBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // Update active button
                timeRangeBtns.forEach(b => b.classList.remove('active', 'bg-cyan-500', 'bg-opacity-20', 'text-white'));
                this.classList.add('active', 'bg-cyan-500', 'bg-opacity-20', 'text-white');
                
                // Update time range and fetch data
                currentTimeRange = this.dataset.range;
                fetchHistoricalData(currentTimeRange);
            });
        });
        
        // Initialize page
        function initWaterQualityPage() {
            fetchWaterQualityData();
            initQualityChart();
        }
        
        // Fetch current water quality data
        function fetchWaterQualityData() {
            fetch('/api/water-quality/current/')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    updateCurrentReadings(data);
                    
                    // Update last updated timestamp
                    const now = new Date();
                    lastUpdatedEl.textContent = `Last updated: ${now.toLocaleTimeString()}`;
                })
                .catch(error => {
                    console.error('Error fetching water quality data:', error);
                    showNotification('Failed to fetch water quality data', 'error');
                });
                
            // Also fetch historical data for the chart
            fetchHistoricalData(currentTimeRange);
        }
        
        // Fetch historical water quality data
        function fetchHistoricalData(timeRange) {
            fetch(`/api/water-quality/historical/?range=${timeRange}`)
                .then(response => response.json())
                .then(data => {
                    updateQualityChart(data);
                })
                .catch(error => {
                    console.error('Error fetching historical data:', error);
                });
        }
        
        // Update current readings display
        function updateCurrentReadings(data) {
            // For demonstration, we'll use sample data if API doesn't return real data
            const qualityData = data || {
                hardness_mg_l: 350,
                ph: 7.8,
                turbidity_ntu: 1.2,
                temperature_c: 22.5,
                conductivity_ms_cm: 450
            };
            
            // Update hardness
            document.getElementById('current-hardness').textContent = `${qualityData.hardness_mg_l || '--'} mg/L`;
            updateParameterStatus('hardness', qualityData.hardness_mg_l, 400);
            
            // Update pH
            document.getElementById('current-ph').textContent = qualityData.ph || '--';
            updateParameterStatus('ph', qualityData.ph, null, true);
            
            // Update turbidity
            document.getElementById('current-turbidity').textContent = `${qualityData.turbidity_ntu || '--'} NTU`;
            updateParameterStatus('turbidity', qualityData.turbidity_ntu, 5);
            
            // Update temperature
            document.getElementById('current-temperature').textContent = `${qualityData.temperature_c || '--'} °C`;
            updateParameterStatus('temperature', qualityData.temperature_c, null, true);
            
            // Update conductivity
            document.getElementById('current-conductivity').textContent = `${qualityData.conductivity_ms_cm || '--'} µS/cm`;
            updateParameterStatus('conductivity', qualityData.conductivity_ms_cm, 500);
        }
        
        // Update parameter status indicator
        function updateParameterStatus(parameter, value, threshold, isRange = false) {
            const statusEl = document.getElementById(`${parameter}-status`);
            
            if (!statusEl || value === null || value === undefined) return;
            
            statusEl.className = 'px-3 py-1 rounded-full text-xs font-medium';
            
            if (isRange) {
                // For parameters with ideal ranges (like pH and temperature)
                if (parameter === 'ph') {
                    if (value < 6.5 || value > 8.5) {
                        statusEl.classList.add('bg-red-500', 'bg-opacity-20', 'text-red-300');
                        statusEl.textContent = 'Critical';
                    } else if (value < 7.0 || value > 8.0) {
                        statusEl.classList.add('bg-yellow-500', 'bg-opacity-20', 'text-yellow-300');
                        statusEl.textContent = 'Warning';
                    } else {
                        statusEl.classList.add('bg-green-500', 'bg-opacity-20', 'text-green-300');
                        statusEl.textContent = 'Normal';
                    }
                } else if (parameter === 'temperature') {
                    if (value < 10 || value > 30) {
                        statusEl.classList.add('bg-red-500', 'bg-opacity-20', 'text-red-300');
                        statusEl.textContent = 'Critical';
                    } else if (value < 15 || value > 25) {
                        statusEl.classList.add('bg-yellow-500', 'bg-opacity-20', 'text-yellow-300');
                        statusEl.textContent = 'Warning';
                    } else {
                        statusEl.classList.add('bg-green-500', 'bg-opacity-20', 'text-green-300');
                        statusEl.textContent = 'Normal';
                    }
                }
            } else {
                // For parameters with thresholds
                const warningThreshold = threshold * 0.8;
                
                if (value >= threshold) {
                    statusEl.classList.add('bg-red-500', 'bg-opacity-20', 'text-red-300');
                    statusEl.textContent = 'Critical';
                } else if (value >= warningThreshold) {
                    statusEl.classList.add('bg-yellow-500', 'bg-opacity-20', 'text-yellow-300');
                    statusEl.textContent = 'Warning';
                } else {
                    statusEl.classList.add('bg-green-500', 'bg-opacity-20', 'text-green-300');
                    statusEl.textContent = 'Normal';
                }
            }
        }
        
        // Initialize quality chart
        function initQualityChart() {
            const ctx = document.getElementById('water-quality-chart').getContext('2d');
            
            qualityChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [
                        {
                            label: 'Hardness (mg/L)',
                            data: [],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            pointRadius: 3,
                            tension: 0.4
                        },
                        {
                            label: 'pH',
                            data: [],
                            borderColor: '#06b6d4',
                            backgroundColor: 'rgba(6, 182, 212, 0.1)',
                            borderWidth: 2,
                            pointRadius: 3,
                            tension: 0.4
                        },
                        {
                            label: 'Turbidity (NTU)',
                            data: [],
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            borderWidth: 2,
                            pointRadius: 3,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                color: 'rgba(148, 163, 184, 0.8)',
                                font: {
                                    size: 12
                                },
                                boxWidth: 12
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(15, 23, 42, 0.8)',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            borderColor: 'rgba(148, 163, 184, 0.2)',
                            borderWidth: 1,
                            padding: 10
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(148, 163, 184, 0.1)',
                                borderColor: 'rgba(148, 163, 184, 0.2)'
                            },
                            ticks: {
                                color: 'rgba(148, 163, 184, 0.8)'
                            }
                        },
                        y: {
                            grid: {
                                color: 'rgba(148, 163, 184, 0.1)',
                                borderColor: 'rgba(148, 163, 184, 0.2)'
                            },
                            ticks: {
                                color: 'rgba(148, 163, 184, 0.8)'
                            },
                            // Set a fixed min and max to prevent stretching
                            min: 0,
                            max: 500,
                            suggestedMax: 500
                        }
                    }
                }
            });
        }
        
        // Update quality chart with new data
        function updateQualityChart(data) {
            // For demonstration, we'll use sample data if API doesn't return real data
            const chartData = data || {
                timestamps: generateTimestamps(24),
                hardness: generateRandomData(24, 300, 400),
                ph: generateRandomData(24, 7.2, 8.0),
                turbidity: generateRandomData(24, 0.8, 2.5)
            };
            
            // Update chart data
            qualityChart.data.labels = chartData.timestamps || [];
            
            // Update datasets
            if (chartData.hardness) {
                qualityChart.data.datasets[0].data = chartData.hardness;
            }
            
            if (chartData.ph) {
                qualityChart.data.datasets[1].data = chartData.ph;
                // Set a different y-axis for pH since it has a different scale
                qualityChart.options.scales.y1 = {
                    position: 'right',
                    grid: {
                        drawOnChartArea: false
                    },
                    min: 0,
                    max: 14,
                    ticks: {
                        color: 'rgba(148, 163, 184, 0.8)'
                    }
                };
                qualityChart.data.datasets[1].yAxisID = 'y1';
            }
            
            if (chartData.turbidity) {
                qualityChart.data.datasets[2].data = chartData.turbidity;
            }
            
            // Update chart
            qualityChart.update();
        }
        
        // Helper function to generate timestamps for demo data
        function generateTimestamps(count) {
            const timestamps = [];
            const now = new Date();
            
            for (let i = count - 1; i >= 0; i--) {
                const time = new Date(now - i * 3600000); // hourly intervals
                timestamps.push(time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }));
            }
            
            return timestamps;
        }
        
        // Helper function to generate random data for demo
        function generateRandomData(count, min, max) {
            return Array(count).fill(0).map(() => {
                return parseFloat((Math.random() * (max - min) + min).toFixed(1));
            });
        }
        
        // Show notification
        function showNotification(message, type = 'info') {
            // Create notification container if it doesn't exist
            let notificationArea = document.getElementById('notification-area');
            
            if (!notificationArea) {
                notificationArea = document.createElement('div');
                notificationArea.id = 'notification-area';
                notificationArea.className = 'fixed bottom-4 right-4 z-50 flex flex-col items-end';
                document.body.appendChild(notificationArea);
            }
            
            // Create notification element
            const notification = document.createElement('div');
            notification.className = 'mb-3 p-4 rounded-lg shadow-lg transform transition-all duration-300 ease-in-out translate-x-0';
            
            // Set styles based on notification type
            if (type === 'success') {
                notification.classList.add('bg-green-500', 'bg-opacity-90', 'text-white');
                notification.innerHTML = `<i class="fas fa-check-circle mr-2"></i>${message}`;
            } else if (type === 'error') {
                notification.classList.add('bg-red-500', 'bg-opacity-90', 'text-white');
                notification.innerHTML = `<i class="fas fa-exclamation-circle mr-2"></i>${message}`;
            } else if (type === 'warning') {
                notification.classList.add('bg-yellow-500', 'bg-opacity-90', 'text-white');
                notification.innerHTML = `<i class="fas fa-exclamation-triangle mr-2"></i>${message}`;
            } else {
                notification.classList.add('bg-cyan-500', 'bg-opacity-90', 'text-white');
                notification.innerHTML = `<i class="fas fa-info-circle mr-2"></i>${message}`;
            }
            
            // Add to DOM
            notificationArea.appendChild(notification);
            
            // Remove after 3 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    notificationArea.removeChild(notification);
                }, 300);
            }, 3000);
        }
        
        // Clean up on page unload
        window.addEventListener('beforeunload', function() {
            clearInterval(refreshInterval);
        });
    });
</script>

<!-- Chart.js for data visualization -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}