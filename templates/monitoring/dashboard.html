{% extends "monitoring/base.html" %}

{% block title %}Dashboard - Hydro System Guard SCADA{% endblock %}

{% block content %}
    <div class="max-w-7xl mx-auto px-4 py-6">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <h2 class="text-3xl font-bold text-white">System Dashboard</h2>
                <p class="text-blue-200">Real-time monitoring of Nyamandlovu Pump Station</p>
            </div>
            <div class="flex items-center space-x-4">
                <div class="text-sm text-blue-200" id="last-updated">
                    Last updated: --:--:--
                </div>
                <button id="refresh-btn" class="bg-cyan-500 hover:bg-cyan-600 text-white px-4 py-2 rounded-lg transition duration-300 transform hover:scale-105 pulse-glow">
                    <i class="fas fa-sync-alt mr-2"></i>Refresh
                </button>
            </div>
        </div>

        <!-- System Status Overview -->
        <div class="glass-panel mb-8">
            <div class="px-6 py-4 border-b border-white border-opacity-20">
                <h3 class="text-lg font-semibold text-white">System Status</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- System Health -->
                    <div class="bg-white bg-opacity-10 rounded-lg p-6 border border-white border-opacity-10">
                        <div class="flex items-center justify-between mb-4">
                            <div class="text-blue-200">
                                <i class="fas fa-heartbeat text-xl"></i>
                            </div>
                            <div id="system-health-indicator" class="px-3 py-1 rounded-full text-xs font-medium bg-green-500 bg-opacity-20 text-green-300">
                                Good
                            </div>
                        </div>
                        <h4 class="text-white text-lg font-semibold mb-1">System Health</h4>
                        <p class="text-blue-200 text-sm">All systems operational</p>
                    </div>

                    <!-- Pump Status -->
                    <div class="bg-white bg-opacity-10 rounded-lg p-6 border border-white border-opacity-10">
                        <div class="flex items-center justify-between mb-4">
                            <div class="text-blue-200">
                                <i class="fas fa-cogs text-xl"></i>
                            </div>
                            <div id="pump-status-count" class="text-white text-2xl font-bold">
                                0/0
                            </div>
                        </div>
                        <h4 class="text-white text-lg font-semibold mb-1">Pumps Online</h4>
                        <p class="text-blue-200 text-sm">Active pumps / Total pumps</p>
                    </div>

                    <!-- System Efficiency -->
                    <div class="bg-white bg-opacity-10 rounded-lg p-6 border border-white border-opacity-10">
                        <div class="flex items-center justify-between mb-4">
                            <div class="text-blue-200">
                                <i class="fas fa-tachometer-alt text-xl"></i>
                            </div>
                            <div id="system-efficiency" class="text-white text-2xl font-bold">
                                0%
                            </div>
                        </div>
                        <h4 class="text-white text-lg font-semibold mb-1">System Efficiency</h4>
                        <p class="text-blue-200 text-sm">Overall system performance</p>
                    </div>

                    <!-- Active Alerts -->
                    <div class="bg-white bg-opacity-10 rounded-lg p-6 border border-white border-opacity-10">
                        <div class="flex items-center justify-between mb-4">
                            <div class="text-blue-200">
                                <i class="fas fa-exclamation-triangle text-xl"></i>
                            </div>
                            <div id="active-alerts-count" class="text-white text-2xl font-bold">
                                0
                            </div>
                        </div>
                        <h4 class="text-white text-lg font-semibold mb-1">Active Alerts</h4>
                        <p class="text-blue-200 text-sm">
                            <a href="{% url 'monitoring:alerts' %}" class="text-cyan-300 hover:text-cyan-200">
                                View all alerts <i class="fas fa-arrow-right text-xs ml-1"></i>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Process Flow Visualization -->
        <div class="glass-panel mb-8">
            <div class="px-6 py-4 border-b border-white border-opacity-20">
                <h3 class="text-lg font-semibold text-white">Process Flow</h3>
            </div>
            <div class="p-6">
                <div id="process-flow-card" class="relative h-64 bg-opacity-10 rounded-lg border border-white border-opacity-10 overflow-hidden">
                    <!-- SVG Flow Lines (Must be first for proper z-index) -->
                    <svg class="absolute inset-0 w-full h-full" xmlns="http://www.w3.org/2000/svg">
                        <!-- Flow line gradients -->
                        <defs>
                            <linearGradient id="activeFlowGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.7" />
                                <stop offset="100%" style="stop-color:#10b981;stop-opacity:0.3" />
                            </linearGradient>
                            <linearGradient id="inactiveFlowGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#94a3b8;stop-opacity:0.5" />
                                <stop offset="100%" style="stop-color:#94a3b8;stop-opacity:0.2" />
                            </linearGradient>
                        </defs>
                        
                        <!-- Main horizontal flow line -->
                        <line id="main-flow-line" x1="10%" y1="45%" x2="90%" y2="45%" stroke="url(#inactiveFlowGradient)" stroke-width="4" stroke-linecap="round" class="flow-path" />
                        
                        <!-- Stage connection lines -->
                        <line id="line-1-2" x1="15%" y1="45%" x2="26%" y2="45%" stroke="url(#inactiveFlowGradient)" stroke-width="4" stroke-linecap="round" class="flow-path" />
                        <line id="line-2-3" x1="36%" y1="45%" x2="47%" y2="45%" stroke="url(#inactiveFlowGradient)" stroke-width="4" stroke-linecap="round" class="flow-path" />
                        <line id="line-3-4" x1="57%" y1="45%" x2="68%" y2="45%" stroke="url(#inactiveFlowGradient)" stroke-width="4" stroke-linecap="round" class="flow-path" />
                        <line id="line-4-5" x1="78%" y1="45%" x2="89%" y2="45%" stroke="url(#inactiveFlowGradient)" stroke-width="4" stroke-linecap="round" class="flow-path" />
                        
                        <!-- Vertical connection lines -->
                        <line id="extraction-pump-line" x1="15%" y1="45%" x2="15%" y2="65%" stroke="url(#inactiveFlowGradient)" stroke-width="4" stroke-linecap="round" class="flow-path" />
                        <line id="storage-pump-line" x1="78%" y1="45%" x2="78%" y2="65%" stroke="url(#inactiveFlowGradient)" stroke-width="4" stroke-linecap="round" class="flow-path" />
                    </svg>
                    
                    <!-- Stage 1: Extraction -->
                    <div id="stage-extraction" class="process-stage absolute" style="top: 40%; left: 5%;">
                        <div class="stage-icon-container">
                            <i class="fas fa-water text-2xl"></i>
                        </div>
                        <div class="stage-label">Extraction</div>
                        <div class="stage-details">
                            <div class="text-xs text-blue-200">Level: <span id="level-raw">0.0</span>m</div>
                            <div class="text-xs text-blue-200">TDS: <span id="tds-raw">0</span>ppm</div>
                        </div>
                    </div>
                    
                    <!-- Stage 2: Softening -->
                    <div id="stage-softening" class="process-stage absolute" style="top: 40%; left: 26%;">
                        <div class="stage-icon-container">
                            <i class="fas fa-flask text-2xl"></i>
                        </div>
                        <div class="stage-label">Softening</div>
                        <div class="stage-details">
                            <div class="text-xs text-blue-200">Lime Pump: <span id="pump-lime-status">OFF</span></div>
                        </div>
                    </div>
                    
                    <!-- Stage 3: Treatment -->
                    <div id="stage-treatment" class="process-stage absolute" style="top: 40%; left: 47%;">
                        <div class="stage-icon-container">
                            <i class="fas fa-filter text-2xl"></i>
                        </div>
                        <div class="stage-label">Treatment</div>
                        <div class="stage-details">
                            <div class="text-xs text-blue-200">Motor: <span id="motor-status">OFF</span></div>
                        </div>
                    </div>
                    
                    <!-- Stage 4: Storage -->
                    <div id="stage-storage" class="process-stage absolute" style="top: 40%; left: 68%;">
                        <div class="stage-icon-container">
                            <i class="fas fa-database text-2xl"></i>
                        </div>
                        <div class="stage-label">Storage</div>
                        <div class="stage-details">
                            <div class="text-xs text-blue-200">Level: <span id="level-treated">0.0</span>m</div>
                            <div class="text-xs text-blue-200">TDS: <span id="tds-treated">0</span>ppm</div>
                        </div>
                    </div>
                    
                    <!-- Stage 5: Distribution -->
                    <div id="stage-distribution" class="process-stage absolute" style="top: 40%; left: 89%;">
                        <div class="stage-icon-container">
                            <i class="fas fa-tint text-2xl"></i>
                        </div>
                        <div class="stage-label">Distribution</div>
                        <div class="stage-details">
                            <div class="text-xs text-blue-200">Main Pump: <span id="pump-main-status">OFF</span></div>
                        </div>
                    </div>
                    
                    <!-- Pump Icons -->
                    <div id="pump-aquifer" class="pump-icon absolute" style="top: 65%; left: 15%;">
                        <i class="fas fa-cogs"></i>
                        <div class="pump-label">Aquifer</div>
                    </div>
                    
                    <div id="pump-reservoir" class="pump-icon absolute" style="top: 65%; left: 78%;">
                        <i class="fas fa-cogs"></i>
                        <div class="pump-label">Reservoir</div>
                    </div>
                    
                    <!-- Current Stage Indicator -->
                    <div class="absolute bottom-4 left-4">
                        <div class="bg-cyan-500 bg-opacity-20 rounded-full px-4 py-1 text-sm text-cyan-300 border border-cyan-500 border-opacity-30">
                            <span>Current Stage: <span id="current-stage-name">--</span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pump and Motor Status Cards -->
        <div class="glass-panel mb-8">
            <div class="px-6 py-4 border-b border-white border-opacity-20">
                <h3 class="text-lg font-semibold text-white">Equipment Status</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-3 gap-6">
                    <!-- Pumps Section (2/3 width) -->
                    <div class="col-span-2">
                        <h4 class="text-cyan-300 font-semibold mb-4">
                            <i class="fas fa-cogs mr-2"></i>Pumps
                        </h4>
                        <div id="pump-cards-container" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Pump cards will be dynamically inserted here -->
                            <div class="text-center text-blue-200 py-8">
                                <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                                <p>Loading pump data...</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Motors Section (1/3 width) -->
                    <div class="col-span-1">
                        <h4 class="text-cyan-300 font-semibold mb-4">
                            <i class="fas fa-bolt mr-2"></i>Motors
                        </h4>
                        <div id="motor-cards-container" class="space-y-4">
                            <!-- Motor cards will be dynamically inserted here -->
                            <div class="text-center text-blue-200 py-8">
                                <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                                <p>Loading motor data...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Water Quality & Reservoir Levels -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Water Quality -->
            <div class="glass-panel">
                <div class="px-6 py-4 border-b border-white border-opacity-20">
                    <h3 class="text-lg font-semibold text-white">Water Quality</h3>
                </div>
                <div class="p-6">
                    <div id="water-quality-container" class="grid grid-cols-2 gap-4">
                        <!-- Water quality parameters will be dynamically inserted here -->
                        <div class="text-center text-blue-200 py-8 col-span-2">
                            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                            <p>Loading water quality data...</p>
                        </div>
                    </div>
                    <div class="mt-4 text-right">
                        <a href="{% url 'monitoring:water-quality' %}" class="text-cyan-300 hover:text-cyan-200 text-sm">
                            View detailed water quality <i class="fas fa-arrow-right text-xs ml-1"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Reservoir Levels -->
            <div class="glass-panel p-6">
                <h2 class="text-xl font-semibold text-white mb-4">Reservoir Levels</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="reservoir-levels">
                    <!-- Reservoir level indicators will be dynamically inserted here -->
                    <div class="glass-card p-4 cursor-pointer hover:bg-opacity-20 transition-all duration-300" onclick="showReservoirDetails('main')">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="text-lg font-medium text-white">Main Reservoir</h3>
                            <span class="text-cyan-300">75%</span>
                        </div>
                        <div class="relative h-4 bg-gray-700 rounded-full overflow-hidden">
                            <div class="absolute top-0 left-0 h-full bg-cyan-500 rounded-full" style="width: 75%"></div>
                        </div>
                    </div>
                    <!-- Loading state -->
                    <div class="glass-card p-4 animate-pulse">
                        <div class="h-6 bg-gray-700 rounded w-3/4 mb-2"></div>
                        <div class="h-4 bg-gray-700 rounded-full"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reservoir Visualization Modal -->
        <div id="reservoir-modal" class="fixed inset-0 bg-black bg-opacity-75 hidden items-center justify-center z-50">
            <div class="glass-panel w-full max-w-4xl mx-4 p-6 relative">
                <button onclick="closeReservoirModal()" class="absolute top-4 right-4 text-white hover:text-cyan-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Reservoir Diagram -->
                    <div class="glass-card p-6">
                        <h3 class="text-xl font-semibold text-white mb-4">Reservoir Diagram</h3>
                        <div class="relative h-64 bg-gray-800 rounded-lg overflow-hidden">
                            <div class="absolute bottom-0 left-0 w-full bg-cyan-500 transition-all duration-500" id="water-level" style="height: 75%"></div>
                            <div class="absolute inset-0 flex flex-col justify-between p-4">
                                <div class="text-white text-right">100%</div>
                                <div class="text-white text-right">75%</div>
                                <div class="text-white text-right">50%</div>
                                <div class="text-white text-right">25%</div>
                                <div class="text-white text-right">0%</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Reservoir Details -->
                    <div class="glass-card p-6">
                        <h3 class="text-xl font-semibold text-white mb-4">Reservoir Details</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="text-cyan-300">Current Level</label>
                                <p class="text-white text-2xl font-semibold" id="current-level">75%</p>
                            </div>
                            <div>
                                <label class="text-cyan-300">Capacity</label>
                                <p class="text-white" id="reservoir-capacity">1,000,000 L</p>
                            </div>
                            <div>
                                <label class="text-cyan-300">Last Updated</label>
                                <p class="text-white" id="last-updated">2024-02-20 15:30:00</p>
                            </div>
                            <div>
                                <label class="text-cyan-300">Status</label>
                                <p class="text-green-300" id="reservoir-status">Normal</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Historical Data -->
                <div class="glass-card p-6 mt-6">
                    <h3 class="text-xl font-semibold text-white mb-4">Historical Data</h3>
                    <canvas id="reservoir-chart" class="w-full h-64"></canvas>
                </div>
            </div>
        </div>

        <!-- Recent Activity & System Performance -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Recent Activity -->
            <div class="glass-panel">
                <div class="px-6 py-4 border-b border-white border-opacity-20">
                    <h3 class="text-lg font-semibold text-white">Recent Activity</h3>
                </div>
                <div class="p-6">
                    <div id="recent-activity-container" class="space-y-4">
                        <!-- Activity items will be dynamically inserted here -->
                        <div class="text-center text-blue-200 py-8">
                            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                            <p>Loading activity data...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Performance Chart -->
            <div class="glass-panel">
                <div class="px-6 py-4 border-b border-white border-opacity-20">
                    <h3 class="text-lg font-semibold text-white">System Performance</h3>
                </div>
                <div class="p-6">
                    <div class="bg-white bg-opacity-5 rounded-lg p-4">
                        <canvas id="performance-chart" height="250"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Area -->
    <div id="notification-area" class="fixed bottom-4 right-4 z-50"></div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // DOM elements
        const refreshBtn = document.getElementById('refresh-btn');
        const lastUpdatedEl = document.getElementById('last-updated');
        const systemHealthIndicator = document.getElementById('system-health-indicator');
        const pumpStatusCount = document.getElementById('pump-status-count');
        const systemEfficiency = document.getElementById('system-efficiency');
        const activeAlertsCount = document.getElementById('active-alerts-count');
        const pumpCardsContainer = document.getElementById('pump-cards-container');
        const motorCardsContainer = document.getElementById('motor-cards-container');
        const waterQualityContainer = document.getElementById('water-quality-container');
        const reservoirLevelsContainer = document.getElementById('reservoir-levels');
        const recentActivityContainer = document.getElementById('recent-activity-container');

        // Performance chart
        let performanceChart;

        // Reservoir modal functionality
        const reservoirModal = document.getElementById('reservoir-modal');
        const closeModal = document.getElementById('close-modal');
        let reservoirHistoryChart;

        // Initialize the dashboard
        initDashboard();

        // Set up auto-refresh (every 10 seconds)
        const refreshInterval = setInterval(fetchDashboardData, 10000);

        // Manual refresh button
        refreshBtn.addEventListener('click', function() {
            fetchDashboardData();
            showNotification('Dashboard refreshed', 'info');
        });

        // Initialize dashboard
        function initDashboard() {
            fetchDashboardData();
            initPerformanceChart();
        }

        // Fetch dashboard data from API
        function fetchDashboardData() {
            fetch('/api/dashboard-data/')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    updateDashboard(data);
                })
                .catch(error => {
                    console.error('Error fetching dashboard data:', error);
                    showNotification('Failed to fetch dashboard data', 'error');
                });

            fetch('/api/system-status/')
                .then(response => response.json())
                .then(data => {
                    updateSystemStatus(data);
                })
                .catch(error => {
                    console.error('Error fetching system status:', error);
                });
        }

        // Update dashboard with fetched data
        function updateDashboard(data) {
            // Update last updated timestamp
            const now = new Date();
            lastUpdatedEl.textContent = `Last updated: ${now.toLocaleTimeString()}`;

            // Update KPI cards
            pumpStatusCount.textContent = `${data.running_pumps}/${data.total_pumps}`;
            systemEfficiency.textContent = `${data.system_efficiency}%`;
            activeAlertsCount.textContent = data.active_alerts_count;

            // Update pump cards
            updatePumpCards(data.pumps);
            
            // Update motor cards
            updateMotorCards(data.motors);

            // Update water quality
            updateWaterQuality(data.water_quality);

            // Update reservoir levels (if available)
            if (data.reservoir_levels) {
                updateReservoirLevels(data.reservoir_levels);
            }

            // Update performance chart data
            updatePerformanceChart(data);
            
            // Update process flow
            updateProcessFlow(data);
            
            // Update recent activity
            updateRecentActivity(data);
        }

        // Update system status indicators
        function updateSystemStatus(data) {
            // Update system health indicator
            const healthStatus = data.system_health;
            systemHealthIndicator.className = 'px-3 py-1 rounded-full text-xs font-medium';

            if (healthStatus === 'good') {
                systemHealthIndicator.classList.add('bg-green-500', 'bg-opacity-20', 'text-green-300');
                systemHealthIndicator.textContent = 'Good';
            } else if (healthStatus === 'warning') {
                systemHealthIndicator.classList.add('bg-yellow-500', 'bg-opacity-20', 'text-yellow-300');
                systemHealthIndicator.textContent = 'Warning';
            } else if (healthStatus === 'critical') {
                systemHealthIndicator.classList.add('bg-red-500', 'bg-opacity-20', 'text-red-300');
                systemHealthIndicator.textContent = 'Critical';
            }

            // Update recent activity
            updateRecentActivity(data);
        }

        // Update pump status cards
        function updatePumpCards(pumps) {
            if (!pumps || pumps.length === 0) {
                pumpCardsContainer.innerHTML = '<div class="col-span-full text-center text-blue-200 py-8"><p>No pump data available</p></div>';
                return;
            }

            let pumpCardsHTML = '';

            pumps.forEach(pump => {
                // Map status to class and label
                let statusClass, statusLabel;
                switch((pump.status || '').toLowerCase()) {
                    case 'running':
                        statusClass = 'pump-status-badge pump-status-running';
                        statusLabel = 'Running';
                        break;
                    case 'stopped':
                        statusClass = 'pump-status-badge pump-status-stopped';
                        statusLabel = 'Stopped';
                        break;
                    case 'maintenance':
                        statusClass = 'pump-status-badge pump-status-maintenance';
                        statusLabel = 'Maintenance';
                        break;
                    case 'standby':
                        statusClass = 'pump-status-badge pump-status-standby';
                        statusLabel = 'Standby';
                        break;
                    default:
                        statusClass = 'pump-status-badge pump-status-standby';
                        statusLabel = 'Unknown';
                }

                pumpCardsHTML += `
                    <div class="bg-white bg-opacity-10 rounded-lg p-6 border border-white border-opacity-10">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h4 class="text-white text-lg font-semibold">${pump.name}</h4>
                                <p class="text-blue-200 text-sm">${pump.location || 'No location data'}</p>
                            </div>
                            <span class="${statusClass}">
                              <i class="fas fa-circle"></i> ${statusLabel}
                            </span>
                        </div>

                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <p class="text-blue-200 text-xs">Flow Rate</p>
                                <p class="text-white text-lg font-semibold">${pump.flow_rate || '0'} L/s</p>
                            </div>
                            <div>
                                <p class="text-blue-200 text-xs">Pressure</p>
                                <p class="text-white text-lg font-semibold">${pump.outlet_pressure || '0'} bar</p>
                            </div>
                            <div>
                                <p class="text-blue-200 text-xs">Motor Temp</p>
                                <p class="text-white text-lg font-semibold">${pump.motor_temp || '0'}°C</p>
                            </div>
                            <div>
                                <p class="text-blue-200 text-xs">Running Hours</p>
                                <p class="text-white text-lg font-semibold">${pump.running_hours || '0'} hrs</p>
                            </div>
                        </div>

                        <div class="text-right">
                            <a href="/pump/${pump.id}/" class="text-cyan-300 hover:text-cyan-200 text-sm">
                                View details <i class="fas fa-arrow-right text-xs ml-1"></i>
                            </a>
                        </div>
                    </div>
                `;
            });

            pumpCardsContainer.innerHTML = pumpCardsHTML;
        }

        // Update motor status cards
        function updateMotorCards(motors) {
            console.log("Updating motor cards with data:", motors); // Debug log
            
            if (!motors || motors.length === 0) {
                motorCardsContainer.innerHTML = '<div class="text-center text-blue-200 py-8"><p>No motor data available</p></div>';
                return;
            }

            let motorCardsHTML = '';

            motors.forEach(motor => {
                // Map status to class and label
                let statusClass, statusLabel;
                switch((motor.status || '').toLowerCase()) {
                    case 'on':
                        statusClass = 'motor-status-badge motor-status-running';
                        statusLabel = 'Running';
                        break;
                    case 'off':
                        statusClass = 'motor-status-badge motor-status-stopped';
                        statusLabel = 'Stopped';
                        break;
                    case 'maintenance':
                        statusClass = 'motor-status-badge motor-status-maintenance';
                        statusLabel = 'Maintenance';
                        break;
                    case 'fault':
                        statusClass = 'motor-status-badge motor-status-fault';
                        statusLabel = 'Fault';
                        break;
                    default:
                        statusClass = 'motor-status-badge motor-status-stopped';
                        statusLabel = 'Unknown';
                }

                // Create motor card with animation - always show the motor regardless of status
                motorCardsHTML += `
                    <div class="bg-white bg-opacity-10 rounded-lg p-6 border border-white border-opacity-10">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h4 class="text-white text-lg font-semibold">${motor.name}</h4>
                                <p class="text-blue-200 text-sm">${motor.motor_type === 'STIRRER' ? 'Lime Stirrer Motor' : 
                                                                  motor.motor_type === 'MIXER' ? 'Water Mixer' : 
                                                                  motor.motor_type === 'CONVEYOR' ? 'Conveyor Motor' : 'Other Motor'}</p>
                            </div>
                            <span class="${statusClass}">
                              <i class="fas fa-circle"></i> ${statusLabel}
                            </span>
                        </div>

                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <p class="text-blue-200 text-xs">RPM</p>
                                <p class="text-white text-lg font-semibold">${motor.rpm || '0'}</p>
                            </div>
                            <div>
                                <p class="text-blue-200 text-xs">Temperature</p>
                                <p class="text-white text-lg font-semibold">${motor.temperature || '0'}°C</p>
                            </div>
                            <div>
                                <p class="text-blue-200 text-xs">Power</p>
                                <p class="text-white text-lg font-semibold">${motor.power || '0'} kW</p>
                            </div>
                            <div>
                                <p class="text-blue-200 text-xs">Running Hours</p>
                                <p class="text-white text-lg font-semibold">${motor.running_hours || '0'} hrs</p>
                            </div>
                        </div>

                        <div class="mt-4">
                            <!-- Motor animation - only active when motor is running -->
                            <div class="motor-animation-container ${motor.status.toLowerCase() === 'on' ? 'active' : ''}">
                                <div class="motor-rotor">
                                    <div class="rotor-blade"></div>
                                    <div class="rotor-blade"></div>
                                    <div class="rotor-blade"></div>
                                </div>
                            </div>
                        </div>

                        <div class="text-right mt-2">
                            <a href="/motor/${motor.id}/" class="text-cyan-300 hover:text-cyan-200 text-sm">
                                View details <i class="fas fa-arrow-right text-xs ml-1"></i>
                            </a>
                        </div>
                    </div>
                `;
            });

            motorCardsContainer.innerHTML = motorCardsHTML;
        }

        // Update water quality indicators
        function updateWaterQuality(waterQuality) {
            if (!waterQuality) {
                waterQualityContainer.innerHTML = '<div class="col-span-2 text-center text-blue-200 py-8"><p>No water quality data available</p></div>';
                return;
            }

            const waterQualityHTML = `
                <div class="bg-white bg-opacity-10 rounded-lg p-4 border border-white border-opacity-10">
                    <p class="text-blue-200 text-xs">Hardness</p>
                    <p class="text-white text-lg font-semibold">${waterQuality.hardness_mg_l || '0'} mg/L</p>
                </div>
                <div class="bg-white bg-opacity-10 rounded-lg p-4 border border-white border-opacity-10">
                    <p class="text-blue-200 text-xs">pH Level</p>
                    <p class="text-white text-lg font-semibold">${waterQuality.ph || '0'}</p>
                </div>
                <div class="bg-white bg-opacity-10 rounded-lg p-4 border border-white border-opacity-10">
                    <p class="text-blue-200 text-xs">Turbidity</p>
                    <p class="text-white text-lg font-semibold">${waterQuality.turbidity_ntu || '0'} NTU</p>
                </div>
                <div class="bg-white bg-opacity-10 rounded-lg p-4 border border-white border-opacity-10">
                    <p class="text-blue-200 text-xs">Temperature</p>
                    <p class="text-white text-lg font-semibold">${waterQuality.temperature_c || '0'}°C</p>
                </div>
            `;

            waterQualityContainer.innerHTML = waterQualityHTML;
        }
        
        // Update reservoir levels
        function updateReservoirLevels(reservoirs) {
            if (!reservoirs || reservoirs.length === 0) {
                reservoirLevelsContainer.innerHTML = '<div class="text-center text-blue-200 py-8"><p>No reservoir data available</p></div>';
                return;
            }
            
            let reservoirsHTML = '';
            
            reservoirs.forEach(reservoir => {
                // Determine level color based on percentage
                let levelColor;
                const level = reservoir.level_percentage || 0;
                
                if (level < 20) {
                    levelColor = 'bg-red-500';
                } else if (level < 40) {
                    levelColor = 'bg-orange-500';
                } else if (level < 60) {
                    levelColor = 'bg-yellow-500';
                } else if (level < 80) {
                    levelColor = 'bg-blue-500';
                } else {
                    levelColor = 'bg-green-500';
                }
                
                reservoirsHTML += `
                    <div class="bg-white bg-opacity-10 rounded-lg p-4 border border-white border-opacity-10 cursor-pointer hover:bg-opacity-20 transition-all duration-300"
                         onclick="showReservoirDetails('${reservoir.reservoir_id}', ${level}, '${reservoir.capacity || 'Unknown'}', '${reservoir.last_updated || 'Unknown'}', '${reservoir.status || 'Unknown'}')">
                        <div class="flex justify-between items-center mb-2">
                            <h4 class="text-white font-semibold">${reservoir.reservoir_id || 'Unknown Reservoir'}</h4>
                            <span class="text-white font-semibold">${level}%</span>
                        </div>
                        <div class="w-full bg-white bg-opacity-10 rounded-full h-4">
                            <div class="${levelColor} h-4 rounded-full" style="width: ${level}%"></div>
                        </div>
                    </div>
                `;
            });
            
            reservoirLevelsContainer.innerHTML = reservoirsHTML;
        }
        
        // Update recent activity
        function updateRecentActivity(data) {
            // If we have system logs, use them, otherwise create placeholder activities
            let activities = data.recent_logs || [
                { timestamp: new Date().toISOString(), message: 'System status check completed', category: 'SYSTEM', level: 'INFO' },
                { timestamp: new Date(Date.now() - 300000).toISOString(), message: 'Pump 1 started automatically', category: 'PUMP_CONTROL', level: 'INFO' },
                { timestamp: new Date(Date.now() - 900000).toISOString(), message: 'Water quality check completed', category: 'WATER_QUALITY', level: 'INFO' },
                { timestamp: new Date(Date.now() - 3600000).toISOString(), message: 'Low pressure alert on Pump 2', category: 'ALERT', level: 'WARNING' }
            ];
            
            let activitiesHTML = '';
            
            activities.forEach(activity => {
                // Format timestamp
                const timestamp = new Date(activity.timestamp);
                const formattedTime = timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                
                // Determine icon and color based on category and level
                let icon, color;
                
                switch(activity.category) {
                    case 'PUMP_CONTROL':
                        icon = 'fa-cogs';
                        break;
                    case 'WATER_QUALITY':
                        icon = 'fa-flask';
                        break;
                    case 'ALERT':
                        icon = 'fa-exclamation-triangle';
                        break;
                    case 'USER_ACTION':
                        icon = 'fa-user';
                        break;
                    default:
                        icon = 'fa-info-circle';
                }
                
                switch(activity.level) {
                    case 'WARNING':
                        color = 'text-yellow-300';
                        break;
                    case 'ERROR':
                    case 'CRITICAL':
                        color = 'text-red-300';
                        break;
                    default:
                        color = 'text-blue-300';
                }
                
                activitiesHTML += `
                    <div class="flex items-start space-x-3 bg-white bg-opacity-5 rounded-lg p-3">
                        <div class="${color} mt-1">
                            <i class="fas ${icon}"></i>
                        </div>
                        <div class="flex-1">
                            <p class="text-white">${activity.message}</p>
                            <p class="text-blue-200 text-xs">${formattedTime}</p>
                        </div>
                    </div>
                `;
            });
            
            recentActivityContainer.innerHTML = activitiesHTML;
        }
        
        // Initialize performance chart
        function initPerformanceChart() {
            const ctx = document.getElementById('performance-chart').getContext('2d');
            
            // Create gradient for chart background
            const gradient = ctx.createLinearGradient(0, 0, 0, 250);
            gradient.addColorStop(0, 'rgba(6, 182, 212, 0.5)');
            gradient.addColorStop(1, 'rgba(6, 182, 212, 0.0)');
            
            performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                    datasets: [{
                        label: 'System Performance',
                        data: [65, 70, 80, 75, 85, 80],
                        borderColor: '#06b6d4',
                        backgroundColor: gradient,
                        borderWidth: 2,
                        pointBackgroundColor: '#06b6d4',
                        pointBorderColor: '#fff',
                        pointRadius: 4,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(15, 23, 42, 0.8)',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            borderColor: 'rgba(148, 163, 184, 0.2)',
                            borderWidth: 1,
                            padding: 10,
                            displayColors: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(148, 163, 184, 0.1)',
                                borderColor: 'rgba(148, 163, 184, 0.2)'
                            },
                            ticks: {
                                color: 'rgba(148, 163, 184, 0.8)'
                            }
                        },
                        y: {
                            grid: {
                                color: 'rgba(148, 163, 184, 0.1)',
                                borderColor: 'rgba(148, 163, 184, 0.2)'
                            },
                            ticks: {
                                color: 'rgba(148, 163, 184, 0.8)',
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            min: 0,
                            max: 100
                        }
                    }
                }
            });
        }
        
        // Update performance chart with new data
        function updatePerformanceChart(data) {
            // In a real implementation, you would use historical data from the API
            // For now, we'll just update with random data
            const newData = Array(6).fill(0).map(() => Math.floor(Math.random() * 30) + 60);
            
            performanceChart.data.datasets[0].data = newData;
            performanceChart.update();
        }
        
        // Show notification
        function showNotification(message, type = 'info') {
            const notificationArea = document.getElementById('notification-area');
            
            // Create notification element
            const notification = document.createElement('div');
            notification.className = 'mb-3 p-4 rounded-lg shadow-lg transform transition-all duration-300 ease-in-out translate-x-0';
            
            // Set styles based on notification type
            if (type === 'success') {
                notification.classList.add('bg-green-500', 'bg-opacity-90', 'text-white');
                notification.innerHTML = `<i class="fas fa-check-circle mr-2"></i>${message}`;
            } else if (type === 'error') {
                notification.classList.add('bg-red-500', 'bg-opacity-90', 'text-white');
                notification.innerHTML = `<i class="fas fa-exclamation-circle mr-2"></i>${message}`;
            } else if (type === 'warning') {
                notification.classList.add('bg-yellow-500', 'bg-opacity-90', 'text-white');
                notification.innerHTML = `<i class="fas fa-exclamation-triangle mr-2"></i>${message}`;
            } else {
                notification.classList.add('bg-blue-500', 'bg-opacity-90', 'text-white');
                notification.innerHTML = `<i class="fas fa-info-circle mr-2"></i>${message}`;
            }
            
            // Add to DOM
            notificationArea.appendChild(notification);
            
            // Remove after 3 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    notificationArea.removeChild(notification);
                }, 300);
            }, 3000);
        }
        
        // Clean up on page unload
        window.addEventListener('beforeunload', function() {
            clearInterval(refreshInterval);
        });

        // Close modal when clicking the close button or outside the modal
        closeModal.addEventListener('click', () => {
            reservoirModal.classList.add('hidden');
        });

        reservoirModal.addEventListener('click', (e) => {
            if (e.target === reservoirModal) {
                reservoirModal.classList.add('hidden');
            }
        });

        // Show reservoir details in modal
        window.showReservoirDetails = function(reservoirId, level, capacity, lastUpdated, status) {
            const modal = document.getElementById('reservoir-modal');
            modal.classList.remove('hidden');
            modal.classList.add('flex');
            
            // Update water level visualization
            const waterLevel = document.getElementById('water-level');
            const currentLevel = document.getElementById('current-level');
            currentLevel.textContent = `${level}%`;
            waterLevel.style.height = `${level}%`;
            
            // Fetch historical data
            fetch(`/api/reservoir-history/${reservoirId}/`)
                .then(response => response.json())
                .then(data => {
                    const ctx = document.getElementById('reservoir-chart').getContext('2d');
                    new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: data.labels,
                            datasets: [{
                                label: 'Water Level',
                                data: data.values,
                                borderColor: '#0ea5e9',
                                backgroundColor: 'rgba(14, 165, 233, 0.1)',
                                fill: true,
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    labels: {
                                        color: '#ffffff'
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 100,
                                    grid: {
                                        color: 'rgba(255, 255, 255, 0.1)'
                                    },
                                    ticks: {
                                        color: '#ffffff'
                                    }
                                },
                                x: {
                                    grid: {
                                        color: 'rgba(255, 255, 255, 0.1)'
                                    },
                                    ticks: {
                                        color: '#ffffff'
                                    }
                                }
                            }
                        }
                    });
                })
                .catch(error => console.error('Error fetching historical data:', error));
        }

        function closeReservoirModal() {
            const modal = document.getElementById('reservoir-modal');
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }

        // Update process flow visualization
        function updateProcessFlow(data) {
            if (!data.process) return;
            
            // Update current stage indicator
            document.getElementById('current-stage-name').textContent = data.process.current_stage || '--';
            
            // Update stage details
            document.getElementById('level-raw').textContent = data.process.level_raw || '0.0';
            document.getElementById('tds-raw').textContent = data.process.tds_raw || '0';
            document.getElementById('level-treated').textContent = data.process.level_treated || '0.0';
            document.getElementById('tds-treated').textContent = data.process.tds_treated || '0';
            
            // Update lime pump status in the softening stage
            const limePumpStatus = document.getElementById('pump-lime-status');
            if (limePumpStatus) {
                const limeStatus = data.process.stages?.softening?.status || 'STANDBY';
                limePumpStatus.textContent = limeStatus === 'RUNNING' ? 'ON' : 'OFF';
                limePumpStatus.className = limeStatus === 'RUNNING' ? 'text-green-300' : 'text-red-300';
            }
            
            // Update motor status in the treatment stage
            const motorStatus = document.getElementById('motor-status');
            if (motorStatus) {
                const motorRunning = data.process.motor_status === 'ON' || 
                                    (data.motors && data.motors.some(m => m.motor_type === 'STIRRER' && m.status === 'ON'));
                motorStatus.textContent = motorRunning ? 'ON' : 'OFF';
                motorStatus.className = motorRunning ? 'text-green-300' : 'text-red-300';
            }
            
            // Update main pump status
            const pumpMainStatus = document.getElementById('pump-main-status');
            if (pumpMainStatus) {
                const mainStatus = data.process.stages?.distribution?.status || 'STANDBY';
                pumpMainStatus.textContent = mainStatus === 'RUNNING' ? 'ON' : 'OFF';
                pumpMainStatus.className = mainStatus === 'RUNNING' ? 'text-green-300' : 'text-red-300';
            }
            
            // Highlight current stage
            const stages = ['extraction', 'softening', 'treatment', 'storage', 'distribution'];
            const currentStage = data.process.current_stage?.toLowerCase() || '';
            
            stages.forEach(stage => {
                const stageElement = document.getElementById(`stage-${stage}`);
                if (stageElement) {
                    if (currentStage.includes(stage)) {
                        stageElement.classList.add('active');
                    } else {
                        stageElement.classList.remove('active');
                    }
                }
            });
            
            // Update stage status classes
            if (data.process.stages) {
                Object.keys(data.process.stages).forEach(stage => {
                    const stageElement = document.getElementById(`stage-${stage}`);
                    if (stageElement) {
                        // Remove all status classes
                        stageElement.classList.remove('status-running', 'status-standby', 'status-fault');
                        
                        // Add appropriate status class
                        const status = data.process.stages[stage].status;
                        if (status === 'RUNNING') {
                            stageElement.classList.add('status-running');
                        } else if (status === 'FAULT') {
                            stageElement.classList.add('status-fault');
                        } else {
                            stageElement.classList.add('status-standby');
                        }
                    }
                });
            }
            
            // Update pump status
            const aquiferPump = document.getElementById('pump-aquifer');
            const reservoirPump = document.getElementById('pump-reservoir');
            
            if (aquiferPump && data.process.stages?.extraction) {
                aquiferPump.classList.remove('status-running', 'status-standby');
                aquiferPump.classList.add(data.process.stages.extraction.status === 'RUNNING' ? 'status-running' : 'status-standby');
            }
            
            if (reservoirPump && data.process.stages?.storage) {
                reservoirPump.classList.remove('status-running', 'status-standby');
                reservoirPump.classList.add(data.process.stages.storage.status === 'RUNNING' ? 'status-running' : 'status-standby');
            }
            
            // Add flow animation classes based on status
            const flowActive = data.process.flow_active;
            document.querySelectorAll('.flow-path').forEach(path => {
                if (flowActive) {
                    path.classList.add('flow-active');
                } else {
                    path.classList.remove('flow-active');
                }
            });
        }
    });
</script>

<!-- Chart.js for data visualization -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}
