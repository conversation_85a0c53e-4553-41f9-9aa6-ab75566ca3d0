{% extends 'monitoring/base.html' %}
{% load static %}

{% block title %}{{ motor.name }} - Motor Details{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <div class="mb-6">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{% url 'monitoring:dashboard' %}" class="text-blue-300 hover:text-blue-200">
                        <i class="fas fa-home mr-2"></i>Dashboard
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                        <span class="text-white">{{ motor.name }}</span>
                    </div>
                </li>
            </ol>
        </nav>
    </div>

    <!-- Motor Header -->
    <div class="glass-panel mb-8">
        <div class="p-6">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                    <h1 class="text-2xl font-bold text-white mb-2">{{ motor.name }}</h1>
                    <p class="text-blue-200">{{ motor.get_motor_type_display }}</p>
                </div>
                <div class="mt-4 md:mt-0">
                    <span class="motor-status-badge motor-status-{{ motor.status|lower }}">
                        <i class="fas fa-circle mr-2"></i>{{ motor.get_status_display }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Motor Details -->
        <div class="glass-panel lg:col-span-1">
            <div class="px-6 py-4 border-b border-white border-opacity-20">
                <h3 class="text-lg font-semibold text-white">Motor Details</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div>
                        <p class="text-blue-200 text-sm">Model</p>
                        <p class="text-white">{{ motor.model }}</p>
                    </div>
                    <div>
                        <p class="text-blue-200 text-sm">Serial Number</p>
                        <p class="text-white">{{ motor.serial_number }}</p>
                    </div>
                    <div>
                        <p class="text-blue-200 text-sm">Current RPM</p>
                        <p class="text-white text-xl font-semibold">{{ motor.rpm }} <span class="text-sm font-normal">/ {{ motor.max_rpm }} max</span></p>
                    </div>
                    <div>
                        <p class="text-blue-200 text-sm">Temperature</p>
                        <p class="text-white text-xl font-semibold">{{ motor.temperature }}°C</p>
                    </div>
                    <div>
                        <p class="text-blue-200 text-sm">Power Consumption</p>
                        <p class="text-white text-xl font-semibold">{{ motor.power }} kW</p>
                    </div>
                    <div>
                        <p class="text-blue-200 text-sm">Running Hours</p>
                        <p class="text-white text-xl font-semibold">{{ motor.running_hours }} hrs</p>
                    </div>
                    <div>
                        <p class="text-blue-200 text-sm">Last Service</p>
                        <p class="text-white">{{ motor.last_service|default:"Not available" }}</p>
                    </div>
                    <div>
                        <p class="text-blue-200 text-sm">Next Service</p>
                        <p class="text-white">{{ motor.next_service|default:"Not scheduled" }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Charts -->
        <div class="glass-panel lg:col-span-2">
            <div class="px-6 py-4 border-b border-white border-opacity-20">
                <h3 class="text-lg font-semibold text-white">Performance History (24h)</h3>
            </div>
            <div class="p-6">
                <div class="space-y-8">
                    <!-- RPM Chart -->
                    <div>
                        <h4 class="text-cyan-300 font-semibold mb-2">RPM</h4>
                        <div class="chart-container" style="position: relative; height: 200px;">
                            <canvas id="rpmChart"></canvas>
                        </div>
                    </div>
                    
                    <!-- Temperature Chart -->
                    <div>
                        <h4 class="text-cyan-300 font-semibold mb-2">Temperature (°C)</h4>
                        <div class="chart-container" style="position: relative; height: 200px;">
                            <canvas id="tempChart"></canvas>
                        </div>
                    </div>
                    
                    <!-- Power Chart -->
                    <div>
                        <h4 class="text-cyan-300 font-semibold mb-2">Power Consumption (kW)</h4>
                        <div class="chart-container" style="position: relative; height: 200px;">
                            <canvas id="powerChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Alerts -->
    <div class="glass-panel mt-8">
        <div class="px-6 py-4 border-b border-white border-opacity-20">
            <h3 class="text-lg font-semibold text-white">Recent Alerts</h3>
        </div>
        <div class="p-6">
            {% if recent_alerts %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-700">
                        <thead>
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-blue-200 uppercase tracking-wider">Timestamp</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-blue-200 uppercase tracking-wider">Severity</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-blue-200 uppercase tracking-wider">Message</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-blue-200 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-700">
                            {% for alert in recent_alerts %}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-white">{{ alert.timestamp }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="alert-severity-badge alert-severity-{{ alert.severity|lower }}">
                                            {{ alert.get_severity_display }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-white">{{ alert.message }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="alert-status-badge alert-status-{{ alert.status|lower }}">
                                            {{ alert.get_status_display }}
                                        </span>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <p class="text-blue-200 text-center py-4">No recent alerts for this motor</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Chart configuration
        const chartConfig = {
            type: 'line',
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)'
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)'
                        },
                        // Set a reasonable min and max to prevent stretching
                        min: 0,
                        suggestedMax: 100
                    }
                },
                elements: {
                    line: {
                        tension: 0.3
                    },
                    point: {
                        radius: 2
                    }
                }
            }
        };

        // Chart data
        const chartData = {{ chart_data|safe }} || {
            timestamps: Array(24).fill(0).map((_, i) => `${i}:00`),
            rpm_data: Array(24).fill(0),
            temp_data: Array(24).fill(0),
            power_data: Array(24).fill(0)
        };

        // RPM Chart
        const rpmChart = new Chart(
            document.getElementById('rpmChart').getContext('2d'),
            {
                ...chartConfig,
                data: {
                    labels: chartData.timestamps,
                    datasets: [{
                        label: 'RPM',
                        data: chartData.rpm_data,
                        borderColor: '#06b6d4',
                        backgroundColor: 'rgba(6, 182, 212, 0.1)',
                        fill: true
                    }]
                },
                options: {
                    ...chartConfig.options,
                    scales: {
                        ...chartConfig.options.scales,
                        y: {
                            ...chartConfig.options.scales.y,
                            suggestedMax: Math.max(...chartData.rpm_data, 1000) || 1000
                        }
                    }
                }
            }
        );

        // Temperature Chart
        const tempChart = new Chart(
            document.getElementById('tempChart').getContext('2d'),
            {
                ...chartConfig,
                data: {
                    labels: chartData.timestamps,
                    datasets: [{
                        label: 'Temperature',
                        data: chartData.temp_data,
                        borderColor: '#f59e0b',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        fill: true
                    }]
                },
                options: {
                    ...chartConfig.options,
                    scales: {
                        ...chartConfig.options.scales,
                        y: {
                            ...chartConfig.options.scales.y,
                            suggestedMax: Math.max(...chartData.temp_data, 100) || 100
                        }
                    }
                }
            }
        );

        // Power Chart
        const powerChart = new Chart(
            document.getElementById('powerChart').getContext('2d'),
            {
                ...chartConfig,
                data: {
                    labels: chartData.timestamps,
                    datasets: [{
                        label: 'Power',
                        data: chartData.power_data,
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        fill: true
                    }]
                },
                options: {
                    ...chartConfig.options,
                    scales: {
                        ...chartConfig.options.scales,
                        y: {
                            ...chartConfig.options.scales.y,
                            suggestedMax: Math.max(...chartData.power_data, 50) || 50
                        }
                    }
                }
            }
        );
    });
</script>
{% endblock %}