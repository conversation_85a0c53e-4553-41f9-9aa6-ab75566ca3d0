<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Hydro System Guard SCADA</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 25%, #06b6d4 50%, #10b981 75%, #059669 100%);
        }
        .water-animation {
            background: linear-gradient(-45deg, #06b6d4, #0891b2, #0e7490, #155e75);
            background-size: 400% 400%;
            animation: wave 4s ease-in-out infinite;
        }
        @keyframes wave {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        .pulse-glow {
            animation: pulse-glow 2s ease-in-out infinite alternate;
        }
        @keyframes pulse-glow {
            from { box-shadow: 0 0 20px rgba(59, 130, 246, 0.5); }
            to { box-shadow: 0 0 30px rgba(59, 130, 246, 0.8), 0 0 40px rgba(59, 130, 246, 0.3); }
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 0.5rem;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <div class="glass-card p-8 w-full max-w-md text-white">
        <div class="text-center mb-8">
            <div class="w-16 h-16 bg-blue-500 rounded-lg flex items-center justify-center mx-auto pulse-glow mb-4">
                <i class="fas fa-tint text-white text-3xl"></i>
            </div>
            <h1 class="text-2xl font-bold">Hydro System Guard</h1>
            <p class="text-blue-200 text-sm">Nyamandlovu Water Station SCADA</p>
        </div>
        
        <form id="login-form" method="post" action="{% url 'monitoring:login' %}" class="space-y-6">
            {% csrf_token %}
            
            <div>
                <label for="username" class="block text-sm font-medium text-blue-200 mb-1">Username</label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-user text-blue-300"></i>
                    </div>
                    <input type="text" id="username" name="username" required 
                           class="bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg block w-full pl-10 pr-3 py-2 focus:outline-none focus:ring-2 focus:ring-cyan-300 focus:border-transparent">
                </div>
            </div>
            
            <div>
                <label for="password" class="block text-sm font-medium text-blue-200 mb-1">Password</label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-lock text-blue-300"></i>
                    </div>
                    <input type="password" id="password" name="password" required 
                           class="bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg block w-full pl-10 pr-3 py-2 focus:outline-none focus:ring-2 focus:ring-cyan-300 focus:border-transparent">
                </div>
            </div>
            
            <div>
                <button type="submit" class="w-full bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white font-bold py-3 px-4 rounded-lg transition duration-300 transform hover:scale-105 pulse-glow">
                    <i class="fas fa-sign-in-alt mr-2"></i>Login to Dashboard
                </button>
            </div>
            
            <div id="login-error" class="hidden bg-red-500 bg-opacity-20 border border-red-400 text-red-100 px-4 py-3 rounded relative" role="alert">
                <strong class="font-bold">Error!</strong>
                <span class="block sm:inline" id="error-message">Invalid credentials.</span>
            </div>
        </form>
        
        <div class="mt-8 text-center">
            <a href="{% url 'monitoring:landing' %}" class="text-blue-200 hover:text-white text-sm">
                <i class="fas fa-arrow-left mr-1"></i>Back to Home
            </a>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('login-form');
            const loginError = document.getElementById('login-error');
            const errorMessage = document.getElementById('error-message');
            
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(loginForm);
                
                fetch('{% url "monitoring:login" %}', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = data.redirect;
                    } else {
                        loginError.classList.remove('hidden');
                        errorMessage.textContent = data.error || 'Invalid credentials.';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    loginError.classList.remove('hidden');
                    errorMessage.textContent = 'An error occurred. Please try again.';
                });
            });
        });
    </script>
</body>
</html>