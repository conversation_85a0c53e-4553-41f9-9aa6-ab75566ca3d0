{% extends "monitoring/base.html" %}

{% block title %}Alerts - Hydro System Guard SCADA{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-3xl font-bold text-white">System Alerts</h2>
            <p class="text-blue-200">Monitor and manage system alerts and notifications</p>
        </div>
        <div class="flex items-center space-x-4">
            <div class="text-sm text-blue-200" id="last-updated">
                Last updated: --:--:--
            </div>
            <button id="refresh-btn" class="bg-cyan-500 hover:bg-cyan-600 text-white px-4 py-2 rounded-lg transition duration-300 transform hover:scale-105 pulse-glow">
                <i class="fas fa-sync-alt mr-2"></i>Refresh
            </button>
        </div>
    </div>

    <!-- <PERSON>ert Filters -->
    <div class="glass-panel mb-8">
        <div class="px-6 py-4 border-b border-white border-opacity-20">
            <h3 class="text-lg font-semibold text-white">Filter Alerts</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="status-filter" class="block text-sm font-medium text-blue-200 mb-1">Status</label>
                    <select id="status-filter" class="bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg block w-full px-3 py-2 text-white">
                        <option value="all">All Statuses</option>
                        <option value="active" selected>Active</option>
                        <option value="acknowledged">Acknowledged</option>
                        <option value="resolved">Resolved</option>
                    </select>
                </div>
                <div>
                    <label for="severity-filter" class="block text-sm font-medium text-blue-200 mb-1">Severity</label>
                    <select id="severity-filter" class="bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg block w-full px-3 py-2 text-white">
                        <option value="all">All Severities</option>
                        <option value="critical">Critical</option>
                        <option value="warning">Warning</option>
                        <option value="info">Information</option>
                    </select>
                </div>
                <div>
                    <label for="date-filter" class="block text-sm font-medium text-blue-200 mb-1">Time Period</label>
                    <select id="date-filter" class="bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg block w-full px-3 py-2 text-white">
                        <option value="24h">Last 24 Hours</option>
                        <option value="7d">Last 7 Days</option>
                        <option value="30d">Last 30 Days</option>
                        <option value="all">All Time</option>
                    </select>
                </div>
                <div>
                    <label for="location-filter" class="block text-sm font-medium text-blue-200 mb-1">Location</label>
                    <select id="location-filter" class="bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg block w-full px-3 py-2 text-white">
                        <option value="all">All Locations</option>
                        <option value="pump-station">Pump Station</option>
                        <option value="reservoir">Reservoir</option>
                        <option value="distribution">Distribution Network</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Alerts -->
    <div class="glass-panel mb-8">
        <div class="px-6 py-4 border-b border-white border-opacity-20">
            <h3 class="text-lg font-semibold text-white">Active Alerts</h3>
        </div>
        <div class="p-6">
            <div id="active-alerts-container" class="space-y-4">
                <div class="text-center text-blue-200 py-8">
                    <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                    <p>Loading alerts...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert History -->
    <div class="glass-panel">
        <div class="px-6 py-4 border-b border-white border-opacity-20">
            <h3 class="text-lg font-semibold text-white">Alert History</h3>
        </div>
        <div class="p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-white divide-opacity-20">
                    <thead>
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-blue-200 uppercase tracking-wider">Time</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-blue-200 uppercase tracking-wider">Severity</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-blue-200 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-blue-200 uppercase tracking-wider">Message</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-blue-200 uppercase tracking-wider">Location</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-blue-200 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-white divide-opacity-10" id="alert-history-table">
                        <!-- Alert history rows will be inserted here -->
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-blue-200">
                                <i class="fas fa-spinner fa-spin mr-2"></i>Loading alert history...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="mt-4 flex justify-between items-center">
                <div class="text-sm text-blue-200">
                    Showing <span id="showing-count">0</span> of <span id="total-count">0</span> alerts
                </div>
                <div class="flex space-x-2">
                    <button id="prev-page" class="bg-blue-500 bg-opacity-20 hover:bg-opacity-30 text-white px-3 py-1 rounded disabled:opacity-50">
                        <i class="fas fa-chevron-left mr-1"></i>Previous
                    </button>
                    <button id="next-page" class="bg-blue-500 bg-opacity-20 hover:bg-opacity-30 text-white px-3 py-1 rounded disabled:opacity-50">
                        Next<i class="fas fa-chevron-right ml-1"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alert Acknowledgment Modal -->
<div id="acknowledge-modal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-filter backdrop-blur-sm flex items-center justify-center hidden z-50">
    <div class="glass-card p-6 max-w-md w-full">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-white">Acknowledge Alert</h3>
            <button id="close-modal" class="text-white hover:text-blue-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="mb-4">
            <p class="text-blue-200 mb-2">Alert:</p>
            <p id="modal-alert-message" class="text-white font-medium"></p>
        </div>
        <div class="mb-4">
            <label for="acknowledgment-comment" class="block text-sm font-medium text-blue-200 mb-1">Comment (Optional)</label>
            <textarea id="acknowledgment-comment" rows="3" class="bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg block w-full px-3 py-2 text-white"></textarea>
        </div>
        <div class="flex justify-end space-x-3">
            <button id="cancel-acknowledge" class="bg-gray-500 bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded">
                Cancel
            </button>
            <button id="confirm-acknowledge" class="bg-cyan-500 hover:bg-cyan-600 text-white px-4 py-2 rounded">
                <i class="fas fa-check-circle mr-1"></i>Acknowledge
            </button>
        </div>
        <input type="hidden" id="alert-id-to-acknowledge">
    </div>
</div>

{% csrf_token %}

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Variables for pagination
        let currentPage = 1;
        let totalPages = 1;
        let alertsPerPage = 10;
        
        // Initial data load
        fetchActiveAlerts();
        fetchAlertHistory();
        
        // Set up refresh interval (every 30 seconds)
        const refreshInterval = setInterval(fetchActiveAlerts, 30000);
        
        // Manual refresh button
        document.getElementById('refresh-btn').addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Refreshing...';
            
            Promise.all([fetchActiveAlerts(), fetchAlertHistory()]).then(() => {
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-sync-alt mr-2"></i>Refresh';
            });
        });
        
        // Filter change handlers
        document.getElementById('status-filter').addEventListener('change', fetchAlertHistory);
        document.getElementById('severity-filter').addEventListener('change', fetchAlertHistory);
        document.getElementById('date-filter').addEventListener('change', fetchAlertHistory);
        document.getElementById('location-filter').addEventListener('change', fetchAlertHistory);
        
        // Pagination handlers
        document.getElementById('prev-page').addEventListener('click', function() {
            if (currentPage > 1) {
                currentPage--;
                fetchAlertHistory();
            }
        });
        
        document.getElementById('next-page').addEventListener('click', function() {
            if (currentPage < totalPages) {
                currentPage++;
                fetchAlertHistory();
            }
        });
        
        // Modal handlers
        document.getElementById('close-modal').addEventListener('click', closeAcknowledgeModal);
        document.getElementById('cancel-acknowledge').addEventListener('click', closeAcknowledgeModal);
        document.getElementById('confirm-acknowledge').addEventListener('click', confirmAcknowledge);
        
        // Fetch active alerts from API
        async function fetchActiveAlerts() {
            try {
                const response = await fetch('/api/alerts/?status=active', {
                    headers: {
                        'X-CSRFToken': csrftoken,
                    }
                });
                
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                
                const data = await response.json();
                updateActiveAlerts(data.results || []);
                
                // Update last updated time
                const now = new Date();
                document.getElementById('last-updated').textContent = 
                    `Last updated: ${now.toLocaleTimeString()}`;
                
                return data;
            } catch (error) {
                console.error('Error fetching active alerts:', error);
                document.getElementById('active-alerts-container').innerHTML = `
                    <div class="text-center text-red-300 py-4">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        <span>Failed to load alerts. Please try again.</span>
                    </div>
                `;
            }
        }
        
        // Fetch alert history from API
        async function fetchAlertHistory() {
            try {
                // Get filter values
                const statusFilter = document.getElementById('status-filter').value;
                const severityFilter = document.getElementById('severity-filter').value;
                const dateFilter = document.getElementById('date-filter').value;
                const locationFilter = document.getElementById('location-filter').value;
                
                // Build query string
                let queryParams = `page=${currentPage}&limit=${alertsPerPage}`;
                
                if (statusFilter !== 'all') {
                    queryParams += `&status=${statusFilter}`;
                }
                
                if (severityFilter !== 'all') {
                    queryParams += `&severity=${severityFilter}`;
                }
                
                if (dateFilter !== 'all') {
                    queryParams += `&period=${dateFilter}`;
                }
                
                if (locationFilter !== 'all') {
                    queryParams += `&location=${locationFilter}`;
                }
                
                const response = await fetch(`/api/alerts/?${queryParams}`, {
                    headers: {
                        'X-CSRFToken': csrftoken,
                    }
                });
                
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                
                const data = await response.json();
                updateAlertHistory(data.results || []);
                
                // Update pagination info
                totalPages = Math.ceil(data.count / alertsPerPage);
                document.getElementById('showing-count').textContent = data.results.length;
                document.getElementById('total-count').textContent = data.count;
                
                // Update pagination buttons
                document.getElementById('prev-page').disabled = currentPage <= 1;
                document.getElementById('next-page').disabled = currentPage >= totalPages;
                
                return data;
            } catch (error) {
                console.error('Error fetching alert history:', error);
                document.getElementById('alert-history-table').innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-red-300">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            <span>Failed to load alert history. Please try again.</span>
                        </td>
                    </tr>
                `;
            }
        }
        
        // Update active alerts display
        function updateActiveAlerts(alerts) {
            const alertsContainer = document.getElementById('active-alerts-container');
            
            if (!alerts || alerts.length === 0) {
                alertsContainer.innerHTML = `
                    <div class="text-center py-8">
                        <p class="text-green-400"><i class="fas fa-check-circle mr-2"></i>No active alerts</p>
                    </div>
                `;
                return;
            }
            
            let alertsHTML = '';
            
            alerts.forEach(alert => {
                // Determine severity class
                let severityClass, severityIcon;
                
                switch(alert.severity) {
                    case 'critical':
                        severityClass = 'border-red-500 bg-red-500 bg-opacity-10 status-critical';
                        severityIcon = 'fa-exclamation-circle text-red-500';
                        break;
                    case 'warning':
                        severityClass = 'border-yellow-500 bg-yellow-500 bg-opacity-10';
                        severityIcon = 'fa-exclamation-triangle text-yellow-500';
                        break;
                    case 'info':
                        severityClass = 'border-blue-500 bg-blue-500 bg-opacity-10';
                        severityIcon = 'fa-info-circle text-blue-500';
                        break;
                    default:
                        severityClass = 'border-gray-500 bg-gray-500 bg-opacity-10';
                        severityIcon = 'fa-bell text-gray-500';
                }
                
                // Format timestamp
                const timestamp = new Date(alert.timestamp).toLocaleString();
                
                alertsHTML += `
                    <div class="border-l-4 ${severityClass} p-4 rounded">
                        <div class="flex items-start">
                            <i class="fas ${severityIcon} mt-1 mr-3 text-xl"></i>
                            <div class="flex-1">
                                <div class="flex justify-between">
                                    <p class="text-white font-medium">${alert.message}</p>
                                    <span class="text-xs text-blue-200">${timestamp}</span>
                                </div>
                                <p class="text-sm text-blue-200 mt-1">${alert.location || 'System'}</p>
                                <div class="flex justify-between items-center mt-3">
                                    <div>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white bg-opacity-10 text-white">
                                            ${alert.parameter || 'System Alert'}
                                        </span>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white bg-opacity-10 text-white ml-2">
                                            Value: ${alert.value || 'N/A'}
                                        </span>
                                    </div>
                                    <button class="text-cyan-300 hover:text-cyan-400 acknowledge-btn" 
                                            data-alert-id="${alert.id}"
                                            data-alert-message="${alert.message}">
                                        <i class="fas fa-check-circle mr-1"></i>Acknowledge
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            alertsContainer.innerHTML = alertsHTML;
            
            // Add event listeners to acknowledge buttons
            document.querySelectorAll('.acknowledge-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    openAcknowledgeModal(this.dataset.alertId, this.dataset.alertMessage);
                });
            });
        }
        
        // Update alert history table
        function updateAlertHistory(alerts) {
            const historyTable = document.getElementById('alert-history-table');
            
            if (!alerts || alerts.length === 0) {
                historyTable.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-blue-200">
                            No alerts found matching the current filters
                        </td>
                    </tr>
                `;
                return;
            }
            
            let tableHTML = '';
            
            alerts.forEach(alert => {
                // Determine severity class and icon
                let severityClass, severityIcon, statusClass, statusText;
                
                switch(alert.severity) {
                    case 'critical':
                        severityClass = 'bg-red-500 bg-opacity-20 text-red-400';
                        severityIcon = 'fa-exclamation-circle';
                        break;
                    case 'warning':
                        severityClass = 'bg-yellow-500 bg-opacity-20 text-yellow-400';
                        severityIcon = 'fa-exclamation-triangle';
                        break;
                    case 'info':
                        severityClass = 'bg-blue-500 bg-opacity-20 text-blue-400';
                        severityIcon = 'fa-info-circle';
                        break;
                    default:
                        severityClass = 'bg-gray-500 bg-opacity-20 text-gray-400';
                        severityIcon = 'fa-bell';
                }
                
                switch(alert.status) {
                    case 'active':
                        statusClass = 'bg-red-500 bg-opacity-20 text-red-400';
                        statusText = 'Active';
                        break;
                    case 'acknowledged':
                        statusClass = 'bg-yellow-500 bg-opacity-20 text-yellow-400';
                        statusText = 'Acknowledged';
                        break;
                    case 'resolved':
                        statusClass = 'bg-green-500 bg-opacity-20 text-green-400';
                        statusText = 'Resolved';
                        break;
                    default:
                        statusClass = 'bg-gray-500 bg-opacity-20 text-gray-400';
                        statusText = 'Unknown';
                }
                
                // Format timestamp
                const timestamp = new Date(alert.timestamp).toLocaleString();
                
                // Determine available actions
                let actionsHTML = '';
                
                if (alert.status === 'active') {
                    actionsHTML = `
                        <button class="text-cyan-300 hover:text-cyan-400 acknowledge-btn" 
                                data-alert-id="${alert.id}"
                                data-alert-message="${alert.message}">
                            <i class="fas fa-check-circle mr-1"></i>Acknowledge
                        </button>
                    `;
                } else if (alert.status === 'acknowledged') {
                    actionsHTML = `
                        <button class="text-green-400 hover:text-green-500 resolve-btn" 
                                data-alert-id="${alert.id}">
                            <i class="fas fa-check-double mr-1"></i>Resolve
                        </button>
                    `;
                } else {
                    actionsHTML = `
                        <span class="text-blue-200 text-sm">
                            <i class="fas fa-check-double mr-1"></i>Completed
                        </span>
                    `;
                }
                
                tableHTML += `
                    <tr class="hover:bg-white hover:bg-opacity-5">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                            ${timestamp}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${severityClass}">
                                <i class="fas ${severityIcon} mr-1"></i>${alert.severity}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">
                                ${statusText}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-white">
                            ${alert.message}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                            ${alert.location || 'System'}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            ${actionsHTML}
                        </td>
                    </tr>
                `;
            });
            
            historyTable.innerHTML = tableHTML;
            
            // Add event listeners to action buttons
            document.querySelectorAll('.acknowledge-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    openAcknowledgeModal(this.dataset.alertId, this.dataset.alertMessage);
                });
            });
            
            document.querySelectorAll('.resolve-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    resolveAlert(this.dataset.alertId);
                });
            });
        }
        
        // Open acknowledge modal
        function openAcknowledgeModal(alertId, alertMessage) {
            document.getElementById('alert-id-to-acknowledge').value = alertId;
            document.getElementById('modal-alert-message').textContent = alertMessage;
            document.getElementById('acknowledgment-comment').value = '';
            document.getElementById('acknowledge-modal').classList.remove('hidden');
        }
        
        // Close acknowledge modal
        function closeAcknowledgeModal() {
            document.getElementById('acknowledge-modal').classList.add('hidden');
        }
        
        // Confirm alert acknowledgment
        async function confirmAcknowledge() {
            const alertId = document.getElementById('alert-id-to-acknowledge').value;
            const comment = document.getElementById('acknowledgment-comment').value;
            
            try {
                document.getElementById('confirm-acknowledge').disabled = true;
                document.getElementById('confirm-acknowledge').innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Processing...';
                
                const response = await fetch(`/api/alerts/${alertId}/acknowledge/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrftoken,
                    },
                    body: JSON.stringify({
                        comment: comment
                    })
                });
                
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                
                // Close modal and refresh data
                closeAcknowledgeModal();
                await Promise.all([fetchActiveAlerts(), fetchAlertHistory()]);
                
                showNotification('Alert acknowledged successfully', 'success');
                
            } catch (error) {
                console.error('Error acknowledging alert:', error);
                showNotification('Failed to acknowledge alert', 'error');
            } finally {
                document.getElementById('confirm-acknowledge').disabled = false;
                document.getElementById('confirm-acknowledge').innerHTML = '<i class="fas fa-check-circle mr-1"></i>Acknowledge';
            }
        }
        
        // Resolve an alert
        async function resolveAlert(alertId) {
            try {
                const response = await fetch(`/api/alerts/${alertId}/resolve/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrftoken,
                    }
                });
                
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                
                // Refresh data
                await Promise.all([fetchActiveAlerts(), fetchAlertHistory()]);
                
                showNotification('Alert resolved successfully', 'success');
                
            } catch (error) {
                console.error('Error resolving alert:', error);
                showNotification('Failed to resolve alert', 'error');
            }
        }
    });
</script>
{% endblock %}