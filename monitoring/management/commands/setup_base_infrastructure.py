from django.core.management.base import BaseCommand
from monitoring.models import Infrastructure, Pump, Reservoir, WaterQuality
from django.utils import timezone
import datetime

class Command(BaseCommand):
    help = 'Setup base infrastructure for the water monitoring system'

    def handle(self, *args, **options):
        # Create main pump house
        pump_house, created = Infrastructure.objects.get_or_create(
            name="Main Pump House",
            defaults={
                'type': 'PUMP_HOUSE',
                'location': 'Main Facility',
                'status': 'ACTIVE',
                'description': 'Primary pump house for water distribution',
                'installation_date': timezone.now().date(),
                'last_maintenance': timezone.now().date(),
                'next_maintenance': (timezone.now() + datetime.timedelta(days=365)).date(),
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS(f'Created infrastructure: {pump_house.name}'))
        else:
            self.stdout.write(f'Infrastructure {pump_house.name} already exists')
        
        # Create 4 pumps with zero data
        for i in range(1, 5):
            pump, created = Pump.objects.get_or_create(
                infrastructure=pump_house,
                name=f'Pump {i}',
                defaults={
                    'model': f'Model P{i}',
                    'serial_number': f'SN{i}00{i}',
                    'status': 'STOPPED',
                    'flow_rate': 0.0,
                    'max_flow_rate': 100.0,
                    'outlet_pressure': 0.0,
                    'motor_temp': 0.0,
                    'running_hours': 0.0,
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'Created pump: {pump.name}'))
            else:
                self.stdout.write(f'Pump {pump.name} already exists')
        
        # Create stirring motor (represented as a pump)
        motor, created = Pump.objects.get_or_create(
            infrastructure=pump_house,
            name='Stirring Motor',
            defaults={
                'model': 'Stirrer M1',
                'serial_number': 'SM001',
                'status': 'STOPPED',
                'flow_rate': 0.0,
                'max_flow_rate': 0.0,  # Not applicable for stirrer
                'outlet_pressure': 0.0,  # Not applicable for stirrer
                'motor_temp': 0.0,
                'running_hours': 0.0,
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS(f'Created stirring motor: {motor.name}'))
        else:
            self.stdout.write(f'Stirring motor {motor.name} already exists')
        
        # Create reservoir with level sensors
        reservoir, created = Reservoir.objects.get_or_create(
            infrastructure=pump_house,
            name='Main Reservoir',
            defaults={
                'capacity': 1000.0,
                'current_level': 0.0,
                'status': 'NORMAL',
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS(f'Created reservoir: {reservoir.name}'))
        else:
            self.stdout.write(f'Reservoir {reservoir.name} already exists')
        
        # Create water quality record for TDS sensors
        water_quality, created = WaterQuality.objects.get_or_create(
            infrastructure=pump_house,
            defaults={
                'hardness_mg_l': 0.0,
                'ph': 0.0,
                'turbidity_ntu': 0.0,
                'temperature_c': 0.0,
                'chlorine_mg_l': 0.0,
                'conductivity_ms_cm': 0.0,
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('Created water quality monitoring'))
        else:
            self.stdout.write('Water quality monitoring already exists')
            
        self.stdout.write(self.style.SUCCESS('Base infrastructure setup complete'))