from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.db import connection

class Command(BaseCommand):
    help = 'Reset database for real-time microcontroller data streaming'

    def add_arguments(self, parser):
        parser.add_argument(
            '--no-input', 
            action='store_true', 
            help='Skip confirmation prompt'
        )

    def handle(self, *args, **options):
        if options['no_input'] or input('This will delete all data. Are you sure? (y/n): ').lower() == 'y':
            self.stdout.write('Flushing database...')
            call_command('flush', interactive=False)
            self.stdout.write(self.style.SUCCESS('Database reset successfully'))
            self.stdout.write('Database is now ready for real-time data streaming')