# Generated by Django 4.2.21 on 2025-06-09 21:13

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('monitoring', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='pump',
            name='pump_type',
            field=models.CharField(choices=[('AQUIFER', 'Aquifer Pump'), ('LIME', 'Lime Dosing Pump'), ('RESERVOIR', 'Reservoir Pump'), ('MAIN', 'Main Distribution Pump'), ('OTHER', 'Other')], default='OTHER', max_length=20),
        ),
        migrations.AlterField(
            model_name='pump',
            name='status',
            field=models.CharField(choices=[('ON', 'Running'), ('OFF', 'Stopped'), ('MAINTENANCE', 'Maintenance'), ('STANDBY', 'Standby')], default='OFF', max_length=20),
        ),
        migrations.CreateModel(
            name='ProcessStage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('current_stage', models.CharField(choices=[('EXTRACTION', 'Water Extraction'), ('SOFTENING', 'Water Softening'), ('TREATMENT', 'Water Treatment'), ('STORAGE', 'Water Storage'), ('DISTRIBUTION', 'Water Distribution')], default='EXTRACTION', max_length=20)),
                ('tds_raw', models.FloatField(default=0)),
                ('tds_treated', models.FloatField(default=0)),
                ('level_raw', models.FloatField(default=0)),
                ('level_treated', models.FloatField(default=0)),
                ('motor_status', models.CharField(default='OFF', max_length=10)),
                ('timestamp', models.DateTimeField(auto_now=True)),
                ('infrastructure', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='process_stages', to='monitoring.infrastructure')),
            ],
        ),
    ]
