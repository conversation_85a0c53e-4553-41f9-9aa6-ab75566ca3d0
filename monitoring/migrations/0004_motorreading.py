# Generated by Django 4.2.21 on 2025-06-09 22:00

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('monitoring', '0003_motor'),
    ]

    operations = [
        migrations.CreateModel(
            name='MotorReading',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('rpm', models.FloatField()),
                ('temperature', models.FloatField()),
                ('power', models.FloatField()),
                ('status', models.CharField(choices=[('ON', 'Running'), ('OFF', 'Stopped'), ('MAINTENANCE', 'Maintenance'), ('FAULT', 'Fault')], max_length=20)),
                ('motor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='readings', to='monitoring.motor')),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
    ]
