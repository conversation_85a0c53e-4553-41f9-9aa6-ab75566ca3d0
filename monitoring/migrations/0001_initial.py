# Generated by Django 4.2.21 on 2025-05-28 13:10

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Infrastructure',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('type', models.CharField(choices=[('PUMP_HOUSE', 'Pump House'), ('RESERVOIR', 'Reservoir'), ('TREATMENT_PLANT', 'Treatment Plant'), ('DISTRIBUTION_NETWORK', 'Distribution Network')], max_length=50)),
                ('location', models.CharField(max_length=200)),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('MAINTENANCE', 'Maintenance'), ('INACTIVE', 'Inactive')], default='ACTIVE', max_length=20)),
                ('description', models.TextField(blank=True)),
                ('installation_date', models.DateField()),
                ('last_maintenance', models.DateField(blank=True, null=True)),
                ('next_maintenance', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='WaterQuality',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('hardness_mg_l', models.FloatField()),
                ('ph', models.FloatField()),
                ('turbidity_ntu', models.FloatField()),
                ('temperature_c', models.FloatField()),
                ('chlorine_mg_l', models.FloatField()),
                ('conductivity_ms_cm', models.FloatField()),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('infrastructure', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='water_quality', to='monitoring.infrastructure')),
            ],
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('operator', 'Operator'), ('administrator', 'Administrator')], default='operator', max_length=20)),
                ('full_name', models.CharField(blank=True, max_length=100)),
                ('phone_number', models.CharField(blank=True, max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='SystemLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category', models.CharField(choices=[('SYSTEM', 'System'), ('PUMP_CONTROL', 'Pump Control'), ('VALVE_CONTROL', 'Valve Control'), ('WATER_QUALITY', 'Water Quality'), ('ALERT', 'Alert'), ('USER_ACTION', 'User Action')], max_length=20)),
                ('message', models.TextField()),
                ('level', models.CharField(choices=[('INFO', 'Information'), ('WARNING', 'Warning'), ('ERROR', 'Error'), ('CRITICAL', 'Critical')], default='INFO', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('infrastructure', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='monitoring.infrastructure')),
            ],
        ),
        migrations.CreateModel(
            name='Reservoir',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('capacity', models.FloatField()),
                ('current_level', models.FloatField(default=0)),
                ('status', models.CharField(choices=[('NORMAL', 'Normal'), ('LOW', 'Low'), ('HIGH', 'High'), ('MAINTENANCE', 'Maintenance')], default='NORMAL', max_length=20)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('infrastructure', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reservoirs', to='monitoring.infrastructure')),
            ],
        ),
        migrations.CreateModel(
            name='Pump',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('model', models.CharField(max_length=100)),
                ('serial_number', models.CharField(max_length=50, unique=True)),
                ('status', models.CharField(choices=[('RUNNING', 'Running'), ('STOPPED', 'Stopped'), ('MAINTENANCE', 'Maintenance'), ('STANDBY', 'Standby')], default='STOPPED', max_length=20)),
                ('flow_rate', models.FloatField(default=0)),
                ('max_flow_rate', models.FloatField()),
                ('outlet_pressure', models.FloatField(default=0)),
                ('motor_temp', models.FloatField(default=0)),
                ('running_hours', models.FloatField(default=0)),
                ('last_service', models.DateField(blank=True, null=True)),
                ('next_service', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('infrastructure', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pumps', to='monitoring.infrastructure')),
            ],
        ),
        migrations.CreateModel(
            name='Alert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('INFO', 'Information'), ('WARNING', 'Warning'), ('ERROR', 'Error'), ('CRITICAL', 'Critical')], default='INFO', max_length=20)),
                ('message', models.TextField()),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('ACKNOWLEDGED', 'Acknowledged'), ('RESOLVED', 'Resolved')], default='ACTIVE', max_length=20)),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('acknowledged_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('infrastructure', models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to='monitoring.infrastructure')),
            ],
        ),
    ]
