# Generated by Django 4.2.21 on 2025-06-10 14:30

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('monitoring', '0002_pump_pump_type_alter_pump_status_processstage'),
    ]

    operations = [
        migrations.CreateModel(
            name='Motor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('motor_type', models.CharField(choices=[('STIRRER', 'Lime Stirrer Motor'), ('MIXER', 'Water Mixer'), ('CONVEYOR', 'Conveyor Motor'), ('OTHER', 'Other')], default='OTHER', max_length=20)),
                ('model', models.CharField(max_length=100)),
                ('serial_number', models.CharField(max_length=50, unique=True)),
                ('status', models.Char<PERSON>ield(choices=[('ON', 'Running'), ('OFF', 'Stopped'), ('MAINTENANCE', 'Maintenance'), ('FAULT', 'Fault')], default='OFF', max_length=20)),
                ('rpm', models.FloatField(default=0)),
                ('max_rpm', models.FloatField()),
                ('temperature', models.FloatField(default=0)),
                ('power', models.FloatField(default=0)),
                ('running_hours', models.FloatField(default=0)),
                ('last_service', models.DateField(blank=True, null=True)),
                ('next_service', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('infrastructure', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='motors', to='monitoring.infrastructure')),
            ],
        ),
    ]
