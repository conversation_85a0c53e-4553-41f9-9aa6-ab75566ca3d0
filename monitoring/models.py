from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator


class UserProfile(models.Model):
    """Extended user profile for SCADA system users"""
    ROLE_CHOICES = [
        ('operator', 'Operator'),
        ('administrator', 'Administrator'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='operator')
    full_name = models.CharField(max_length=100, blank=True)
    phone_number = models.CharField(max_length=20, blank=True)
    is_active = models.<PERSON><PERSON>anField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.user.username} ({self.role})"


class Infrastructure(models.Model):
    INFRASTRUCTURE_TYPES = [
        ('PUMP_HOUSE', 'Pump House'),
        ('RESERVOIR', 'Reservoir'),
        ('TREATMENT_PLANT', 'Treatment Plant'),
        ('DISTRIBUTION_NETWORK', 'Distribution Network'),
    ]
    
    STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('MAINTENANCE', 'Maintenance'),
        ('INACTIVE', 'Inactive'),
    ]
    
    name = models.CharField(max_length=100)
    type = models.CharField(max_length=50, choices=INFRASTRUCTURE_TYPES)
    location = models.CharField(max_length=200)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='ACTIVE')
    description = models.TextField(blank=True)
    installation_date = models.DateField()
    last_maintenance = models.DateField(null=True, blank=True)
    next_maintenance = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} ({self.get_type_display()})"


class Pump(models.Model):
    STATUS_CHOICES = [
        ('ON', 'Running'),
        ('OFF', 'Stopped'),
        ('MAINTENANCE', 'Maintenance'),
        ('STANDBY', 'Standby'),
    ]
    
    PUMP_TYPES = [
        ('AQUIFER', 'Aquifer Pump'),
        ('LIME', 'Lime Dosing Pump'),
        ('RESERVOIR', 'Reservoir Pump'),
        ('MAIN', 'Main Distribution Pump'),
        ('OTHER', 'Other'),
    ]
    
    infrastructure = models.ForeignKey(Infrastructure, on_delete=models.CASCADE, related_name='pumps')
    name = models.CharField(max_length=100)
    pump_type = models.CharField(max_length=20, choices=PUMP_TYPES, default='OTHER')
    model = models.CharField(max_length=100)
    serial_number = models.CharField(max_length=50, unique=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='OFF')
    flow_rate = models.FloatField(default=0)  # L/s
    max_flow_rate = models.FloatField()  # L/s
    outlet_pressure = models.FloatField(default=0)  # bar
    motor_temp = models.FloatField(default=0)  # °C
    running_hours = models.FloatField(default=0)
    last_service = models.DateField(null=True, blank=True)
    next_service = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} ({self.infrastructure.name})"


class Reservoir(models.Model):
    STATUS_CHOICES = [
        ('NORMAL', 'Normal'),
        ('LOW', 'Low'),
        ('HIGH', 'High'),
        ('MAINTENANCE', 'Maintenance'),
    ]
    
    infrastructure = models.ForeignKey(Infrastructure, on_delete=models.CASCADE, related_name='reservoirs')
    name = models.CharField(max_length=100)
    capacity = models.FloatField()  # L
    current_level = models.FloatField(default=0)  # L
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='NORMAL')
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} ({self.infrastructure.name})"
    
    @property
    def level_percentage(self):
        return (self.current_level / self.capacity) * 100


class WaterQuality(models.Model):
    infrastructure = models.ForeignKey(Infrastructure, on_delete=models.CASCADE, related_name='water_quality')
    hardness_mg_l = models.FloatField()  # mg/L
    ph = models.FloatField()
    turbidity_ntu = models.FloatField()  # NTU
    temperature_c = models.FloatField()  # °C
    chlorine_mg_l = models.FloatField()  # mg/L
    conductivity_ms_cm = models.FloatField()  # mS/cm
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.infrastructure.name} - {self.last_updated}"


class Alert(models.Model):
    TYPE_CHOICES = [
        ('INFO', 'Information'),
        ('WARNING', 'Warning'),
        ('ERROR', 'Error'),
        ('CRITICAL', 'Critical'),
    ]
    
    STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('ACKNOWLEDGED', 'Acknowledged'),
        ('RESOLVED', 'Resolved'),
    ]
    
    infrastructure = models.ForeignKey(Infrastructure, on_delete=models.CASCADE, default=1)
    type = models.CharField(max_length=20, choices=TYPE_CHOICES, default='INFO')
    message = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='ACTIVE')
    acknowledged_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    acknowledged_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.get_type_display()} - {self.infrastructure.name}"


class SystemLog(models.Model):
    CATEGORY_CHOICES = [
        ('SYSTEM', 'System'),
        ('PUMP_CONTROL', 'Pump Control'),
        ('VALVE_CONTROL', 'Valve Control'),
        ('WATER_QUALITY', 'Water Quality'),
        ('ALERT', 'Alert'),
        ('USER_ACTION', 'User Action'),
    ]
    
    LEVEL_CHOICES = [
        ('INFO', 'Information'),
        ('WARNING', 'Warning'),
        ('ERROR', 'Error'),
        ('CRITICAL', 'Critical'),
    ]
    
    infrastructure = models.ForeignKey(Infrastructure, on_delete=models.CASCADE, related_name='logs')
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    message = models.TextField()
    level = models.CharField(max_length=20, choices=LEVEL_CHOICES, default='INFO')
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.get_category_display()} - {self.infrastructure.name}"


class ProcessStage(models.Model):
    STAGE_CHOICES = [
        ('EXTRACTION', 'Water Extraction'),
        ('SOFTENING', 'Water Softening'),
        ('TREATMENT', 'Water Treatment'),
        ('STORAGE', 'Water Storage'),
        ('DISTRIBUTION', 'Water Distribution'),
    ]
    
    infrastructure = models.ForeignKey(Infrastructure, on_delete=models.CASCADE, related_name='process_stages')
    current_stage = models.CharField(max_length=20, choices=STAGE_CHOICES, default='EXTRACTION')
    tds_raw = models.FloatField(default=0)  # TDS1 - Raw water TDS (ppm)
    tds_treated = models.FloatField(default=0)  # TDS2 - Treated water TDS (ppm)
    level_raw = models.FloatField(default=0)  # Level1 - Raw water tank level (m)
    level_treated = models.FloatField(default=0)  # Level2 - Treated water tank level (m)
    motor_status = models.CharField(max_length=10, default='OFF')  # Main motor status
    timestamp = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Process Stage: {self.get_current_stage_display()} at {self.timestamp}"


class Motor(models.Model):
    STATUS_CHOICES = [
        ('ON', 'Running'),
        ('OFF', 'Stopped'),
        ('MAINTENANCE', 'Maintenance'),
        ('FAULT', 'Fault'),
    ]
    
    MOTOR_TYPES = [
        ('STIRRER', 'Lime Stirrer Motor'),
        ('MIXER', 'Water Mixer'),
        ('CONVEYOR', 'Conveyor Motor'),
        ('OTHER', 'Other'),
    ]
    
    infrastructure = models.ForeignKey(Infrastructure, on_delete=models.CASCADE, related_name='motors')
    name = models.CharField(max_length=100)
    motor_type = models.CharField(max_length=20, choices=MOTOR_TYPES, default='OTHER')
    model = models.CharField(max_length=100)
    serial_number = models.CharField(max_length=50, unique=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='OFF')
    rpm = models.FloatField(default=0)  # Revolutions per minute
    max_rpm = models.FloatField()  # Maximum RPM
    temperature = models.FloatField(default=0)  # °C
    power = models.FloatField(default=0)  # kW
    running_hours = models.FloatField(default=0)
    last_service = models.DateField(null=True, blank=True)
    next_service = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} ({self.get_motor_type_display()})"


class MotorReading(models.Model):
    motor = models.ForeignKey(Motor, on_delete=models.CASCADE, related_name='readings')
    timestamp = models.DateTimeField(auto_now_add=True)
    rpm = models.FloatField()
    temperature = models.FloatField()
    power = models.FloatField()
    status = models.CharField(max_length=20, choices=Motor.STATUS_CHOICES)
    
    class Meta:
        ordering = ['-timestamp']
        
    def __str__(self):
        return f"{self.motor.name} Reading at {self.timestamp}"
