from django.contrib import admin
from .models import (
    UserProfile, Infrastructure, Pump, Reservoir, 
    WaterQuality, Alert, SystemLog, ProcessStage, Motor
)

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'role', 'full_name', 'is_active')
    list_filter = ('role', 'is_active')
    search_fields = ('user__username', 'full_name')

@admin.register(Infrastructure)
class InfrastructureAdmin(admin.ModelAdmin):
    list_display = ('name', 'type', 'location', 'status', 'installation_date')
    list_filter = ('type', 'status')
    search_fields = ('name', 'location')
    date_hierarchy = 'installation_date'

@admin.register(Pump)
class PumpAdmin(admin.ModelAdmin):
    list_display = ('name', 'infrastructure', 'model', 'status', 'flow_rate', 'motor_temp')
    list_filter = ('status', 'infrastructure')
    search_fields = ('name', 'model', 'serial_number')
    readonly_fields = ('running_hours',)

@admin.register(Reservoir)
class ReservoirAdmin(admin.ModelAdmin):
    list_display = ('name', 'infrastructure', 'current_level', 'capacity', 'status')
    list_filter = ('status', 'infrastructure')
    search_fields = ('name',)
    readonly_fields = ('level_percentage',)

@admin.register(WaterQuality)
class WaterQualityAdmin(admin.ModelAdmin):
    list_display = ('infrastructure', 'hardness_mg_l', 'ph', 'temperature_c', 'last_updated')
    list_filter = ('infrastructure',)
    search_fields = ('infrastructure__name',)
    readonly_fields = ('last_updated',)

@admin.register(Alert)
class AlertAdmin(admin.ModelAdmin):
    list_display = ('infrastructure', 'type', 'status', 'created_at', 'acknowledged_by')
    list_filter = ('type', 'status', 'infrastructure')
    search_fields = ('message', 'infrastructure__name')
    readonly_fields = ('created_at', 'updated_at', 'acknowledged_at')

@admin.register(SystemLog)
class SystemLogAdmin(admin.ModelAdmin):
    list_display = ('infrastructure', 'category', 'level', 'created_at')
    list_filter = ('category', 'level', 'infrastructure')
    search_fields = ('message', 'infrastructure__name')
    readonly_fields = ('created_at',)

@admin.register(Motor)
class MotorAdmin(admin.ModelAdmin):
    list_display = ('name', 'motor_type', 'status', 'rpm', 'temperature', 'running_hours')
    list_filter = ('status', 'motor_type')
    search_fields = ('name', 'model', 'serial_number')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('infrastructure', 'name', 'motor_type', 'model', 'serial_number')
        }),
        ('Status & Performance', {
            'fields': ('status', 'rpm', 'max_rpm', 'temperature', 'power', 'running_hours')
        }),
        ('Maintenance', {
            'fields': ('last_service', 'next_service')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
