from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

app_name = 'monitoring'

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'infrastructure', views.InfrastructureViewSet, basename='infrastructure')
router.register(r'pumps', views.PumpViewSet, basename='pump')
router.register(r'reservoirs', views.ReservoirViewSet, basename='reservoir')
router.register(r'water-quality', views.WaterQualityViewSet, basename='water-quality')
router.register(r'alerts', views.AlertViewSet, basename='alert')
router.register(r'logs', views.SystemLogViewSet, basename='log')

# Web interface URLs
urlpatterns = [
    # Landing and authentication
    path('', views.LandingPageView.as_view(), name='landing'),
    path('login/', views.LoginView.as_view(), name='login'),
    path('logout/', views.LogoutView.as_view(), name='logout'),

    # Dashboard and monitoring
    path('dashboard/', views.DashboardView.as_view(), name='dashboard'),
    path('infrastructure/<int:infrastructure_id>/', views.InfrastructureDetailView.as_view(), name='infrastructure-detail'),
    path('alerts/', views.AlertsView.as_view(), name='alerts'),
    path('water-quality/', views.WaterQualityView.as_view(), name='water-quality'),
    path('reports/', views.ReportsView.as_view(), name='reports'),
    path('pump/<int:pump_id>/', views.PumpDetailView.as_view(), name='pump-detail'),
    
    # API endpoints
    path('api/', include(router.urls)),
    path('api/dashboard-data/', views.DashboardDataView.as_view(), name='dashboard-data'),
    path('api/system-status/', views.SystemStatusView.as_view(), name='system-status'),
    path('api/water-quality/current/', views.WaterQualityCurrentView.as_view(), name='water-quality-current'),
    path('api/water-quality/historical/', views.WaterQualityHistoricalView.as_view(), name='water-quality-historical'),
    path('api/reports/generate/', views.GenerateReportView.as_view(), name='generate-report'),
    path('api/process-sensor-data/', views.ProcessSensorDataView.as_view(), name='process-sensor-data'),
    path('motor/<int:pk>/', views.MotorDetailView.as_view(), name='motor_detail'),
]
