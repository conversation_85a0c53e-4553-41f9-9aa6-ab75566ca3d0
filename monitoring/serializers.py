from rest_framework import serializers
from django.contrib.auth.models import User
from .models import (
    UserProfile, Infrastructure, Pump, Reservoir,
    WaterQuality, Alert, SystemLog, Motor
)


class UserProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserProfile
        fields = ['id', 'user', 'role', 'full_name', 'is_active']


class InfrastructureSerializer(serializers.ModelSerializer):
    class Meta:
        model = Infrastructure
        fields = [
            'id', 'name', 'type', 'location', 'status',
            'installation_date', 'last_maintenance', 'next_maintenance'
        ]


class PumpSerializer(serializers.ModelSerializer):
    class Meta:
        model = Pump
        fields = [
            'id', 'infrastructure', 'name', 'model', 'serial_number',
            'status', 'flow_rate', 'max_flow_rate',
            'outlet_pressure', 'motor_temp', 'running_hours',
            'last_service', 'next_service'
        ]


class ReservoirSerializer(serializers.ModelSerializer):
    level_percentage = serializers.ReadOnlyField()
    
    class Meta:
        model = Reservoir
        fields = [
            'id', 'infrastructure', 'name', 'capacity',
            'current_level', 'level_percentage', 'status',
            'last_updated', 'created_at', 'updated_at'
        ]


class WaterQualitySerializer(serializers.ModelSerializer):
    class Meta:
        model = WaterQuality
        fields = [
            'id', 'infrastructure', 'hardness_mg_l', 'ph',
            'turbidity_ntu', 'temperature_c', 'chlorine_mg_l',
            'conductivity_ms_cm', 'last_updated'
        ]


class AlertSerializer(serializers.ModelSerializer):
    class Meta:
        model = Alert
        fields = [
            'id', 'infrastructure', 'type', 'message',
            'status', 'created_at', 'updated_at',
            'acknowledged_by', 'acknowledged_at'
        ]


class SystemLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = SystemLog
        fields = [
            'id', 'infrastructure', 'category', 'level',
            'message', 'created_at'
        ]


class MotorSerializer(serializers.ModelSerializer):
    class Meta:
        model = Motor
        fields = [
            'id', 'infrastructure', 'name', 'motor_type', 'model', 'serial_number',
            'status', 'rpm', 'max_rpm', 'temperature', 'power', 'running_hours',
            'last_service', 'next_service'
        ]


class InfrastructureDetailSerializer(serializers.ModelSerializer):
    pumps = PumpSerializer(many=True, read_only=True)
    reservoir = ReservoirSerializer(read_only=True)
    water_quality = WaterQualitySerializer(read_only=True)
    alerts = AlertSerializer(many=True, read_only=True)
    logs = SystemLogSerializer(many=True, read_only=True)
    
    class Meta:
        model = Infrastructure
        fields = [
            'id', 'name', 'type', 'location', 'status',
            'installation_date', 'last_maintenance', 'next_maintenance',
            'pumps', 'reservoir', 'water_quality', 'alerts', 'logs'
        ]
