from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth import authenticate, login, logout
from django.http import JsonResponse
from django.utils import timezone
from django.db.models import Count, Avg, Max
from django.views import View
from django.views.generic import TemplateView, RedirectView, DetailView
from django.utils.decorators import method_decorator
from django.contrib.auth.mixins import LoginRequiredMixin
from rest_framework import viewsets, status
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.permissions import IsAuthenticated, AllowAny, IsAdminUser
from rest_framework.response import Response
from rest_framework.views import APIView
from datetime import timedelta, datetime
import logging
import random
import os
from django.template.loader import render_to_string
from weasyprint import HTML
import matplotlib.pyplot as plt
import numpy as np

from .models import (
    UserProfile, Infrastructure, Pump, Reservoir,
    WaterQuality, Alert, SystemLog, ProcessStage, Motor, MotorReading
)
from .serializers import (
    UserProfileSerializer, InfrastructureSerializer, PumpSerializer,
    ReservoirSerializer, WaterQualitySerializer, AlertSerializer,
    SystemLogSerializer, InfrastructureDetailSerializer, MotorSerializer,
)

logger = logging.getLogger(__name__)


# Web Views for Dashboard Interface
class LandingPageView(View):
    """Landing page for non-authenticated users"""
    def get(self, request):
        if request.user.is_authenticated:
            return redirect('monitoring:dashboard')
        return render(request, 'monitoring/landing.html')


class DashboardView(LoginRequiredMixin, TemplateView):
    """Main dashboard view"""
    template_name = 'monitoring/dashboard.html'


class InfrastructureDetailView(LoginRequiredMixin, TemplateView):
    """Detailed infrastructure view"""
    template_name = 'monitoring/infrastructure_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['infrastructure'] = get_object_or_404(Infrastructure, id=self.kwargs['infrastructure_id'])
        return context


class AlertsView(LoginRequiredMixin, TemplateView):
    """Alerts management view"""
    template_name = 'monitoring/alerts.html'


class WaterQualityView(LoginRequiredMixin, TemplateView):
    """Water quality monitoring view"""
    template_name = 'monitoring/water_quality.html'


class ReportsView(LoginRequiredMixin, TemplateView):
    """Reports and analytics view"""
    template_name = 'monitoring/reports.html'


# Authentication Views
class LoginView(View):
    """User login"""
    def get(self, request):
        return render(request, 'monitoring/login.html')

    def post(self, request):
        username = request.POST.get('username')
        password = request.POST.get('password')
        user = authenticate(request, username=username, password=password)

        if user is not None:
            login(request, user)
            logger.info(f"User {username} logged in successfully")
            return JsonResponse({'success': True, 'redirect': '/dashboard/'})
        else:
            logger.warning(f"Failed login attempt for username: {username}")
            return JsonResponse({'success': False, 'error': 'Invalid credentials'})


class LogoutView(View):
    """User logout"""
    def get(self, request):
        logout(request)
        return redirect('monitoring:login')


# API ViewSets for REST endpoints
class InfrastructureViewSet(viewsets.ModelViewSet):
    """API for infrastructure management"""
    queryset = Infrastructure.objects.all()
    serializer_class = InfrastructureSerializer
    permission_classes = [IsAuthenticated]

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy', 'control']:
            return [IsAdminUser()]
        return [IsAuthenticated()]

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return InfrastructureDetailSerializer
        return InfrastructureSerializer

    @action(detail=True, methods=['get'])
    def monitoring(self, request, pk=None):
        infrastructure = self.get_object()

        if infrastructure.type == 'PUMP_HOUSE':
            pumps = Pump.objects.filter(infrastructure=infrastructure)
            data = {
                'flow_rate': sum(pump.flow_rate for pump in pumps),
                'pressure': max(pump.outlet_pressure for pump in pumps),
                'motor_temp': max(pump.motor_temp for pump in pumps)
            }
        elif infrastructure.type == 'RESERVOIR':
            reservoir = Reservoir.objects.get(infrastructure=infrastructure)
            data = {
                'level': (reservoir.current_level / reservoir.capacity) * 100,
                'volume': reservoir.current_level,
                'inflow': 0  # This would come from flow meters in a real system
            }
        else:
            data = {'error': 'Unsupported infrastructure type'}

        return Response(data)

    @action(detail=True, methods=['post'])
    def control(self, request, pk=None):
        infrastructure = self.get_object()
        action = request.data.get('action')

        if infrastructure.type == 'PUMP_HOUSE':
            pumps = Pump.objects.filter(infrastructure=infrastructure)

            if action == 'start':
                for pump in pumps:
                    pump.status = 'RUNNING'
                    pump.save()
                SystemLog.objects.create(
                    infrastructure=infrastructure,
                    category='PUMP_CONTROL',
                    message='All pumps started',
                    level='INFO'
                )
            elif action == 'stop':
                for pump in pumps:
                    pump.status = 'STOPPED'
                    pump.save()
                SystemLog.objects.create(
                    infrastructure=infrastructure,
                    category='PUMP_CONTROL',
                    message='All pumps stopped',
                    level='INFO'
                )
            elif action == 'adjust_flow':
                value = float(request.data.get('value', 50))
                for pump in pumps:
                    pump.flow_rate = (value / 100) * pump.max_flow_rate
                pump.save()
                SystemLog.objects.create(
                    infrastructure=infrastructure,
                    category='PUMP_CONTROL',
                    message=f'Flow rate adjusted to {value}%',
                    level='INFO'
                )

        elif infrastructure.type == 'RESERVOIR':
            if action in ['open', 'close']:
                SystemLog.objects.create(
                    infrastructure=infrastructure,
                    category='VALVE_CONTROL',
                    message=f'Valve {action}ed',
                    level='INFO'
                )

        return Response({'status': 'success'})


class PumpViewSet(viewsets.ModelViewSet):
    """API for pump management"""
    queryset = Pump.objects.all()
    serializer_class = PumpSerializer
    permission_classes = [IsAuthenticated]

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAdminUser()]
        return [IsAuthenticated()]


class ReservoirViewSet(viewsets.ModelViewSet):
    """API for reservoir management"""
    queryset = Reservoir.objects.all()
    serializer_class = ReservoirSerializer
    permission_classes = [IsAuthenticated]

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAdminUser()]
        return [IsAuthenticated()]


class WaterQualityViewSet(viewsets.ModelViewSet):
    """API for water quality readings"""
    queryset = WaterQuality.objects.all()
    serializer_class = WaterQualitySerializer
    permission_classes = [IsAuthenticated]

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAdminUser()]
        return [IsAuthenticated()]


class AlertViewSet(viewsets.ModelViewSet):
    """API for alerts management"""
    queryset = Alert.objects.all()
    serializer_class = AlertSerializer
    permission_classes = [IsAuthenticated]

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAdminUser()]
        return [IsAuthenticated()]


class SystemLogViewSet(viewsets.ReadOnlyModelViewSet):
    """API for system logs"""
    queryset = SystemLog.objects.all()
    serializer_class = SystemLogSerializer
    permission_classes = [IsAuthenticated]


class DashboardDataView(APIView):
    """API endpoint for dashboard data"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        # Get all pumps
        pumps = Pump.objects.all()
        running_pumps = pumps.filter(status='ON').count()
        
        # Get all motors
        motors = Motor.objects.all()
        running_motors = motors.filter(status='ON').count()
        
        # Calculate system efficiency
        total_equipment = pumps.count() + motors.count()
        running_equipment = running_pumps + running_motors
        system_efficiency = int((running_equipment / total_equipment * 100) if total_equipment > 0 else 0)
        
        # Get active alerts
        active_alerts_count = Alert.objects.filter(status='ACTIVE').count()
        
        # Get water quality data
        water_quality = WaterQuality.objects.order_by('-last_updated').first()
        water_quality_data = WaterQualitySerializer(water_quality).data if water_quality else None
        
        # Get reservoir levels
        reservoirs = Reservoir.objects.all()
        
        # Get process stage data
        process_stage = ProcessStage.objects.order_by('-timestamp').first()
        
        # Prepare response data
        response_data = {
            'running_pumps': running_pumps,
            'total_pumps': pumps.count(),
            'running_motors': running_motors,
            'total_motors': motors.count(),
            'system_efficiency': system_efficiency,
            'active_alerts_count': active_alerts_count,
            'pumps': PumpSerializer(pumps, many=True).data,
            'motors': MotorSerializer(motors, many=True).data,
            'water_quality': water_quality_data,
            'reservoir_levels': ReservoirSerializer(reservoirs, many=True).data,
        }
        
        # Add process data if available
        if process_stage:
            response_data['process'] = {
                'current_stage': process_stage.get_current_stage_display(),
                'tds_raw': process_stage.tds_raw,
                'tds_treated': process_stage.tds_treated,
                'level_raw': process_stage.level_raw,
                'level_treated': process_stage.level_treated,
                'motor_status': process_stage.motor_status,
                'flow_active': process_stage.motor_status == 'ON' or pumps.filter(status='ON').exists(),
                # Add stage statuses
                'stages': {
                    'extraction': {'status': 'RUNNING' if pumps.filter(pump_type='AQUIFER', status='ON').exists() else 'STANDBY'},
                    'softening': {'status': 'RUNNING' if pumps.filter(pump_type='LIME', status='ON').exists() else 'STANDBY'},
                    'treatment': {'status': process_stage.motor_status},
                    'storage': {'status': 'RUNNING' if pumps.filter(pump_type='RESERVOIR', status='ON').exists() else 'STANDBY'},
                    'distribution': {'status': 'RUNNING' if pumps.filter(pump_type='MAIN', status='ON').exists() else 'STANDBY'}
                }
            }
        
        return Response(response_data)


class SystemStatusView(APIView):
    """API endpoint for system status"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        # Get system health indicators
        critical_alerts = Alert.objects.filter(status='ACTIVE', type='CRITICAL').count()

        system_health = 'good'
        if critical_alerts > 0:
            system_health = 'critical'

        # Get recent system logs
        recent_logs = SystemLog.objects.order_by('-created_at')[:5]
        recent_logs_data = SystemLogSerializer(recent_logs, many=True).data

        return Response({
            'system_health': system_health,
            'critical_alerts': critical_alerts,
            'recent_logs': recent_logs_data,
            'last_updated': timezone.now()
        })


class PumpDetailView(LoginRequiredMixin, TemplateView):
    """Detailed pump view"""
    template_name = 'monitoring/pump_detail.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['pump'] = get_object_or_404(Pump, id=self.kwargs['pump_id'])
        return context


class WaterQualityCurrentView(APIView):
    """API endpoint for current water quality"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        latest = WaterQuality.objects.order_by('-last_updated').first()
        data = WaterQualitySerializer(latest).data if latest else None
        return Response(data)


class WaterQualityHistoricalView(APIView):
    """API endpoint for historical water quality"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        # For demo, generate 24 hourly points
        now = timezone.now()
        timestamps = [(now - timezone.timedelta(hours=i)).strftime('%H:%M') for i in range(23, -1, -1)]
        hardness = [random.uniform(300, 400) for _ in range(24)]
        ph = [random.uniform(7.2, 8.0) for _ in range(24)]
        turbidity = [random.uniform(0.8, 2.5) for _ in range(24)]
        return Response({
            'timestamps': timestamps,
            'hardness': hardness,
            'ph': ph,
            'turbidity': turbidity
        })


class GenerateReportView(APIView):
    """API endpoint for generating reports"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        params = request.data
        report_type = params.get('type', 'report')
        report_format = params.get('format', 'pdf')
        date_str = datetime.now().strftime('%Y-%m-%d')
        filename = f"{report_type}_report_{date_str}.{report_format}"
        file_path = os.path.join('static', 'reports', filename)

        # Compute summary data
        pumps = Pump.objects.all()
        avg_flow = pumps.aggregate(Avg('flow_rate'))['flow_rate__avg'] or 0
        avg_pressure = pumps.aggregate(Avg('outlet_pressure'))['outlet_pressure__avg'] or 0
        total_running_hours = pumps.aggregate(total=models.Sum('running_hours'))['total'] or 0
        recent_alerts = Alert.objects.filter(created_at__gte=timezone.now()-timedelta(days=7)).order_by('-created_at')[:10]

        # Generate chart image
        chart_filename = f"chart_{date_str}.png"
        chart_path = os.path.join('static', 'reports', chart_filename)
        times = np.arange(24)
        flow_data = np.random.normal(loc=avg_flow, scale=2, size=24)
        plt.figure(figsize=(6,2))
        plt.plot(times, flow_data, marker='o')
        plt.title('Flow Rate (Last 24h)')
        plt.xlabel('Hour')
        plt.ylabel('Flow Rate (L/s)')
        plt.tight_layout()
        plt.savefig(chart_path)
        plt.close()

        if report_format == 'pdf':
            html_string = render_to_string('monitoring/report_template.html', {
                'date': date_str,
                'summary': {
                    'avg_flow_rate': f"{avg_flow:.1f}",
                    'avg_pressure': f"{avg_pressure:.1f}",
                    'total_running_hours': f"{total_running_hours:.1f}"
                },
                'alerts': recent_alerts,
                'chart_path': f"/static/reports/{chart_filename}"
            })
            HTML(string=html_string, base_url=request.build_absolute_uri('/')).write_pdf(file_path)
        elif report_format == 'csv':
            import csv
            with open(file_path, 'w', newline='') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(['Pump', 'Flow Rate (L/s)', 'Pressure (bar)', 'Motor Temp (C)', 'Running Hours'])
                for pump in pumps:
                    writer.writerow([pump.name, pump.flow_rate, pump.outlet_pressure, pump.motor_temp, pump.running_hours])
        download_url = f"/static/reports/{filename}"
        return Response({
            **params,
            'downloadUrl': download_url,
            'filename': filename
        })


class ProcessSensorDataView(APIView):
    """API endpoint for receiving sensor data from microcontrollers"""
    permission_classes = []  # No auth for IoT devices or use API key auth in production
    
    def post(self, request):
        try:
            data = request.data
            
            # Get the main pump house infrastructure
            pump_house = Infrastructure.objects.filter(type='PUMP_HOUSE').first()
            if not pump_house:
                return Response({'error': 'Pump house not found'}, status=status.HTTP_404_NOT_FOUND)
            
            # Update pump statuses
            pumps = {
                'AQUIFER': data.get('pump_aquifer', 'OFF'),
                'LIME': data.get('pump_lime', 'OFF'),
                'RESERVOIR': data.get('pump_reservoir', 'OFF'),
                'MAIN': data.get('pump_main', 'OFF')
            }
            
            for pump_type, status_value in pumps.items():
                pump = Pump.objects.filter(infrastructure=pump_house, pump_type=pump_type).first()
                if pump:
                    pump.status = status_value
                    pump.save()
                    
                    # Log pump status change
                    if pump.status != status_value:
                        SystemLog.objects.create(
                            infrastructure=pump_house,
                            category='PUMP_CONTROL',
                            message=f'{pump.name} status changed to {status_value}',
                            level='INFO'
                        )
            
            # Update process stage
            current_stage = data.get('current_stage', '').upper()
            if current_stage:
                process_stage, created = ProcessStage.objects.get_or_create(
                    infrastructure=pump_house,
                    defaults={
                        'current_stage': current_stage,
                        'tds_raw': data.get('tds1', 0),
                        'tds_treated': data.get('tds2', 0),
                        'level_raw': data.get('level1', 0),
                        'level_treated': data.get('level2', 0),
                        'motor_status': data.get('motor_status', 'OFF')
                    }
                )
                
                if not created:
                    process_stage.current_stage = current_stage
                    process_stage.tds_raw = data.get('tds1', process_stage.tds_raw)
                    process_stage.tds_treated = data.get('tds2', process_stage.tds_treated)
                    process_stage.level_raw = data.get('level1', process_stage.level_raw)
                    process_stage.level_treated = data.get('level2', process_stage.level_treated)
                    process_stage.motor_status = data.get('motor_status', process_stage.motor_status)
                    process_stage.save()
            
            # Update water quality
            water_quality, created = WaterQuality.objects.get_or_create(
                infrastructure=pump_house,
                defaults={
                    'hardness_mg_l': data.get('tds1', 0),  # Using TDS1 as hardness for now
                    'ph': 7.0,  # Default value as not provided in the data
                    'turbidity_ntu': 1.0,  # Default value as not provided in the data
                    'temperature_c': 25.0,  # Default value as not provided in the data
                    'chlorine_mg_l': 0.5,  # Default value as not provided in the data
                    'conductivity_ms_cm': data.get('tds1', 0) * 2  # Approximation based on TDS
                }
            )
            
            if not created:
                water_quality.hardness_mg_l = data.get('tds1', water_quality.hardness_mg_l)
                water_quality.conductivity_ms_cm = data.get('tds1', 0) * 2  # Approximation
                water_quality.save()
            
            # Check for alerts (example: high TDS, low water level)
            if data.get('tds1', 0) > 400:
                Alert.objects.create(
                    infrastructure=pump_house,
                    type='WARNING',
                    message=f'High TDS detected in raw water: {data.get("tds1")} ppm',
                    status='ACTIVE'
                )
            
            if data.get('level1', 0) < 2.0:
                Alert.objects.create(
                    infrastructure=pump_house,
                    type='WARNING',
                    message=f'Low water level in raw water tank: {data.get("level1")} m',
                    status='ACTIVE'
                )
            
            return Response({'status': 'success'}, status=status.HTTP_200_OK)
        
        except Exception as e:
            logger.error(f"Error processing sensor data: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# For backward compatibility with URLs
# landing_page = LandingPageView.as_view()
# dashboard = DashboardView.as_view()
# infrastructure_detail = InfrastructureDetailView.as_view()
# alerts_view = AlertsView.as_view()
# water_quality_view = WaterQualityView.as_view()
# reports_view = ReportsView.as_view()
# login_view = LoginView.as_view()
# logout_view = LogoutView.as_view()


class MotorDetailView(LoginRequiredMixin, DetailView):
    model = Motor
    template_name = 'monitoring/motor_detail.html'
    context_object_name = 'motor'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        motor = self.get_object()
        
        # Get historical data for charts (last 24 hours)
        end_time = timezone.now()
        start_time = end_time - timedelta(hours=24)
        
        # Get motor readings for the last 24 hours
        readings = MotorReading.objects.filter(
            motor=motor,
            timestamp__gte=start_time,
            timestamp__lte=end_time
        ).order_by('timestamp')
        
        # Prepare chart data
        rpm_data = []
        temp_data = []
        power_data = []
        timestamps = []
        
        for reading in readings:
            timestamps.append(reading.timestamp.strftime('%H:%M'))
            rpm_data.append(reading.rpm)
            temp_data.append(reading.temperature)
            power_data.append(reading.power)
        
        context['chart_data'] = {
            'timestamps': timestamps,
            'rpm_data': rpm_data,
            'temp_data': temp_data,
            'power_data': power_data
        }
        
        # Get recent alerts for this motor
        # The Alert model doesn't have equipment_id field, so we need to filter by infrastructure
        context['recent_alerts'] = Alert.objects.filter(
            infrastructure=motor.infrastructure
        ).order_by('-created_at')[:5]
        
        return context
