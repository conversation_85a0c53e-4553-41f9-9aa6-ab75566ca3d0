from django.test import TestCase
from django.contrib.auth.models import User
from rest_framework.test import APITestCase
from rest_framework import status
from ..models import Infrastructure, Pump, Reservoir, WaterQuality, Alert, SystemLog

class InfrastructureModelTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.infrastructure = Infrastructure.objects.create(
            name='Test Pump House',
            type='PUMP_HOUSE',
            location='Test Location',
            description='Test Description',
            installation_date='2024-01-01'
        )

    def test_infrastructure_creation(self):
        self.assertEqual(self.infrastructure.name, 'Test Pump House')
        self.assertEqual(self.infrastructure.type, 'PUMP_HOUSE')
        self.assertEqual(self.infrastructure.status, 'ACTIVE')

    def test_pump_creation(self):
        pump = Pump.objects.create(
            infrastructure=self.infrastructure,
            name='Test Pump',
            model='Test Model',
            serial_number='TEST123',
            max_flow_rate=100.0
        )
        self.assertEqual(pump.name, 'Test Pump')
        self.assertEqual(pump.status, 'STOPPED')
        self.assertEqual(pump.max_flow_rate, 100.0)

    def test_reservoir_creation(self):
        reservoir = Reservoir.objects.create(
            infrastructure=self.infrastructure,
            name='Test Reservoir',
            capacity=1000.0,
            current_level=500.0
        )
        self.assertEqual(reservoir.name, 'Test Reservoir')
        self.assertEqual(reservoir.level_percentage, 50.0)

class InfrastructureAPITest(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            is_staff=True
        )
        self.client.force_authenticate(user=self.user)
        self.infrastructure = Infrastructure.objects.create(
            name='Test Pump House',
            type='PUMP_HOUSE',
            location='Test Location',
            description='Test Description',
            installation_date='2024-01-01'
        )

    def test_get_infrastructure_list(self):
        response = self.client.get('/api/infrastructure/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_create_infrastructure(self):
        data = {
            'name': 'New Pump House',
            'type': 'PUMP_HOUSE',
            'location': 'New Location',
            'description': 'New Description',
            'installation_date': '2024-01-01'
        }
        response = self.client.post('/api/infrastructure/', data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Infrastructure.objects.count(), 2)

    def test_get_infrastructure_detail(self):
        response = self.client.get(f'/api/infrastructure/{self.infrastructure.id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Test Pump House')

    def test_update_infrastructure(self):
        data = {
            'name': 'Updated Pump House',
            'type': 'PUMP_HOUSE',
            'location': 'Updated Location',
            'description': 'Updated Description',
            'installation_date': '2024-01-01'
        }
        response = self.client.put(f'/api/infrastructure/{self.infrastructure.id}/', data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.infrastructure.refresh_from_db()
        self.assertEqual(self.infrastructure.name, 'Updated Pump House')

    def test_delete_infrastructure(self):
        response = self.client.delete(f'/api/infrastructure/{self.infrastructure.id}/')
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(Infrastructure.objects.count(), 0)

    def test_infrastructure_monitoring(self):
        # Create test data
        Pump.objects.create(
            infrastructure=self.infrastructure,
            name='Test Pump',
            model='Test Model',
            serial_number='TEST123',
            max_flow_rate=100.0,
            flow_rate=50.0,
            outlet_pressure=2.5,
            motor_temp=45.0
        )
        Reservoir.objects.create(
            infrastructure=self.infrastructure,
            name='Test Reservoir',
            capacity=1000.0,
            current_level=500.0
        )
        WaterQuality.objects.create(
            infrastructure=self.infrastructure,
            hardness_mg_l=150.0,
            ph=7.0,
            turbidity_ntu=1.0,
            temperature_c=25.0,
            chlorine_mg_l=0.5,
            conductivity_ms_cm=500.0
        )

        response = self.client.get(f'/api/infrastructure/{self.infrastructure.id}/monitoring/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('pumps', response.data)
        self.assertIn('reservoirs', response.data)
        self.assertIn('water_quality', response.data)

    def test_infrastructure_control(self):
        pump = Pump.objects.create(
            infrastructure=self.infrastructure,
            name='Test Pump',
            model='Test Model',
            serial_number='TEST123',
            max_flow_rate=100.0
        )

        # Test pump start
        data = {
            'action': 'start_pump',
            'pump_id': pump.id
        }
        response = self.client.post(f'/api/infrastructure/{self.infrastructure.id}/control/', data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        pump.refresh_from_db()
        self.assertEqual(pump.status, 'RUNNING')

        # Test pump stop
        data = {
            'action': 'stop_pump',
            'pump_id': pump.id
        }
        response = self.client.post(f'/api/infrastructure/{self.infrastructure.id}/control/', data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        pump.refresh_from_db()
        self.assertEqual(pump.status, 'STOPPED') 