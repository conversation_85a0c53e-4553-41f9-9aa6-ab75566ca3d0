<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ZimHealth-ID{% endblock %}</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        'medical': {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        'health': {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        }
                    }
                }
            }
        }
    </script>

    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50 font-sans antialiased min-h-screen flex flex-col">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="{% if user.is_authenticated %}{% url 'api:dashboard' %}{% else %}{% url 'zhid_auth:login' %}{% endif %}"
                       class="flex items-center space-x-2 text-medical-600 hover:text-medical-700 transition-colors">
                        <div class="bg-medical-100 p-2 rounded-lg">
                            <i class="fas fa-heartbeat text-medical-600 text-xl"></i>
                        </div>
                        <span class="text-xl font-bold">ZimHealth-ID</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-4">
                    {% if user.is_authenticated %}
                        <!-- Navigation Links -->
                        <a href="{% url 'api:dashboard' %}" class="text-gray-700 hover:text-medical-600 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors font-medium">
                            <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                        </a>
                        <a href="{% url 'api:patients' %}" class="text-gray-700 hover:text-medical-600 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors font-medium">
                            <i class="fas fa-users mr-2"></i>Patients
                        </a>
                        <a href="{% url 'api:appointments' %}" class="text-gray-700 hover:text-medical-600 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors font-medium">
                            <i class="fas fa-calendar mr-2"></i>Appointments
                        </a>
                        <a href="{% url 'api:medical_records' %}" class="text-gray-700 hover:text-medical-600 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors font-medium">
                            <i class="fas fa-file-medical mr-2"></i>Records
                        </a>
                        <a href="{% url 'api:prescriptions' %}" class="text-gray-700 hover:text-medical-600 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors font-medium">
                            <i class="fas fa-pills mr-2"></i>Prescriptions
                        </a>

                        <!-- User Menu -->
                        <div class="relative group">
                            <button class="flex items-center space-x-2 text-gray-700 hover:text-medical-600 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors">
                                <div class="w-8 h-8 bg-medical-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-medical-600 text-sm"></i>
                                </div>
                                <span class="font-medium">{{ user.first_name|default:user.username }}</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>

                            <!-- Dropdown Menu -->
                            <div class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                                <div class="py-2">
                                    <a href="{% url 'zhid_auth:profile' %}"
                                       class="flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors">
                                        <i class="fas fa-user-circle text-medical-600"></i>
                                        <span>Profile</span>
                                    </a>
                                    <a href="{% url 'zhid_auth:password_change' %}"
                                       class="flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors">
                                        <i class="fas fa-key text-medical-600"></i>
                                        <span>Change Password</span>
                                    </a>
                                    <hr class="my-2 border-gray-200">
                                    <a href="{% url 'zhid_auth:logout' %}"
                                       class="flex items-center space-x-2 px-4 py-2 text-red-600 hover:bg-red-50 transition-colors">
                                        <i class="fas fa-sign-out-alt"></i>
                                        <span>Logout</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <a href="{% url 'zhid_auth:login' %}"
                           class="text-gray-700 hover:text-medical-600 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors font-medium">
                            <i class="fas fa-sign-in-alt mr-2"></i>Login
                        </a>
                        <a href="{% url 'zhid_auth:register' %}"
                           class="bg-medical-600 text-white px-4 py-2 rounded-lg hover:bg-medical-700 transition-colors font-medium">
                            <i class="fas fa-user-plus mr-2"></i>Register
                        </a>
                    {% endif %}
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-gray-700 hover:text-medical-600 p-2">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t border-gray-200">
            <div class="px-4 py-2 space-y-2">
                {% if user.is_authenticated %}
                    <div class="flex items-center space-x-3 py-3 border-b border-gray-200">
                        <div class="w-10 h-10 bg-medical-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-medical-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">{{ user.first_name|default:user.username }}</p>
                            <p class="text-sm text-gray-500">{{ user.email }}</p>
                        </div>
                    </div>
                    <a href="{% url 'api:dashboard' %}"
                       class="flex items-center space-x-3 py-2 text-gray-700 hover:text-medical-600">
                        <i class="fas fa-tachometer-alt text-medical-600 w-5"></i>
                        <span>Dashboard</span>
                    </a>
                    <a href="{% url 'api:patients' %}"
                       class="flex items-center space-x-3 py-2 text-gray-700 hover:text-medical-600">
                        <i class="fas fa-users text-medical-600 w-5"></i>
                        <span>Patients</span>
                    </a>
                    <a href="{% url 'api:appointments' %}"
                       class="flex items-center space-x-3 py-2 text-gray-700 hover:text-medical-600">
                        <i class="fas fa-calendar text-medical-600 w-5"></i>
                        <span>Appointments</span>
                    </a>
                    <a href="{% url 'api:medical_records' %}"
                       class="flex items-center space-x-3 py-2 text-gray-700 hover:text-medical-600">
                        <i class="fas fa-file-medical text-medical-600 w-5"></i>
                        <span>Medical Records</span>
                    </a>
                    <a href="{% url 'api:prescriptions' %}"
                       class="flex items-center space-x-3 py-2 text-gray-700 hover:text-medical-600">
                        <i class="fas fa-pills text-medical-600 w-5"></i>
                        <span>Prescriptions</span>
                    </a>
                    <a href="{% url 'zhid_auth:profile' %}"
                       class="flex items-center space-x-3 py-2 text-gray-700 hover:text-medical-600">
                        <i class="fas fa-user-circle text-medical-600 w-5"></i>
                        <span>Profile</span>
                    </a>
                    <a href="{% url 'zhid_auth:password_change' %}"
                       class="flex items-center space-x-3 py-2 text-gray-700 hover:text-medical-600">
                        <i class="fas fa-key text-medical-600 w-5"></i>
                        <span>Change Password</span>
                    </a>
                    <a href="{% url 'zhid_auth:logout' %}"
                       class="flex items-center space-x-3 py-2 text-red-600 hover:text-red-700">
                        <i class="fas fa-sign-out-alt w-5"></i>
                        <span>Logout</span>
                    </a>
                {% else %}
                    <a href="{% url 'zhid_auth:login' %}"
                       class="flex items-center space-x-3 py-2 text-gray-700 hover:text-medical-600">
                        <i class="fas fa-sign-in-alt text-medical-600 w-5"></i>
                        <span>Login</span>
                    </a>
                    <a href="{% url 'zhid_auth:register' %}"
                       class="flex items-center space-x-3 py-2 text-medical-600 hover:text-medical-700">
                        <i class="fas fa-user-plus w-5"></i>
                        <span>Register</span>
                    </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4">
            {% for message in messages %}
                <div class="mb-4 p-4 rounded-lg border-l-4 {% if message.tags == 'success' %}bg-health-50 border-health-400 text-health-800{% elif message.tags == 'error' %}bg-red-50 border-red-400 text-red-800{% elif message.tags == 'warning' %}bg-yellow-50 border-yellow-400 text-yellow-800{% else %}bg-medical-50 border-medical-400 text-medical-800{% endif %} shadow-sm">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            {% if message.tags == 'success' %}
                                <i class="fas fa-check-circle text-health-500 mr-3"></i>
                            {% elif message.tags == 'error' %}
                                <i class="fas fa-exclamation-circle text-red-500 mr-3"></i>
                            {% elif message.tags == 'warning' %}
                                <i class="fas fa-exclamation-triangle text-yellow-500 mr-3"></i>
                            {% else %}
                                <i class="fas fa-info-circle text-medical-500 mr-3"></i>
                            {% endif %}
                            <span class="font-medium">{{ message }}</span>
                        </div>
                        <button onclick="this.parentElement.parentElement.remove()"
                                class="text-gray-400 hover:text-gray-600 transition-colors">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <main class="flex-1">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white mt-auto">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Brand Section -->
                <div class="space-y-4">
                    <div class="flex items-center space-x-2">
                        <div class="bg-medical-600 p-2 rounded-lg">
                            <i class="fas fa-heartbeat text-white text-lg"></i>
                        </div>
                        <span class="text-xl font-bold">ZimHealth-ID</span>
                    </div>
                    <p class="text-gray-300 leading-relaxed">
                        Secure healthcare identity management system for Zimbabwe.
                        Empowering healthcare providers with reliable patient identification.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-medical-400 transition-colors">
                            <i class="fab fa-twitter text-lg"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-medical-400 transition-colors">
                            <i class="fab fa-facebook text-lg"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-medical-400 transition-colors">
                            <i class="fab fa-linkedin text-lg"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-300 hover:text-medical-400 transition-colors">About Us</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-medical-400 transition-colors">Privacy Policy</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-medical-400 transition-colors">Terms of Service</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-medical-400 transition-colors">Contact Support</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold">Contact Information</h3>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-envelope text-medical-400"></i>
                            <span class="text-gray-300"><EMAIL></span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-phone text-medical-400"></i>
                            <span class="text-gray-300">+263 4 123 4567</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-map-marker-alt text-medical-400"></i>
                            <span class="text-gray-300">Harare, Zimbabwe</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm">
                    &copy; 2024 ZimHealth-ID. All rights reserved.
                </p>
                <p class="text-gray-400 text-sm mt-2 md:mt-0">
                    Developed with <i class="fas fa-heart text-red-500"></i> for Zimbabwe's healthcare system
                </p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Auto-hide messages after 5 seconds
        setTimeout(function() {
            const messages = document.querySelectorAll('[role="alert"]');
            messages.forEach(function(message) {
                message.style.transition = 'opacity 0.5s ease-out';
                message.style.opacity = '0';
                setTimeout(function() {
                    message.remove();
                }, 500);
            });
        }, 5000);

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
