# API Templates Directory

This directory contains Django templates for API-related pages and documentation in the ZimHealth-ID application.

## Purpose
- Store HTML templates for API documentation and interfaces
- Organize API testing and debugging tools
- Provide user-friendly API explorer interfaces
- Include API response formatting templates

## Template Organization
```
api/
├── documentation.html      # Main API documentation page
├── explorer.html          # Interactive API explorer/testing tool
├── endpoints.html         # API endpoints listing and details
├── authentication.html    # API authentication documentation
├── examples.html          # API usage examples and tutorials
├── errors.html            # Error codes and troubleshooting guide
├── changelog.html         # API version history and changes
├── rate_limits.html       # Rate limiting information
├── swagger.html           # Swagger/OpenAPI documentation
├── responses/             # API response templates
│   ├── json.html          # JSON response formatting
│   ├── xml.html           # XML response formatting
│   └── error.html         # Error response formatting
└── partials/              # Partial templates
    ├── endpoint_card.html  # Individual endpoint documentation
    ├── parameter_table.html # API parameter tables
    └── response_examples.html # Response example displays
```

## Template Context
API templates typically receive context variables such as:
- `api_version`: Current API version
- `endpoints`: Available API endpoints and their details
- `authentication`: Authentication methods and requirements
- `rate_limits`: API rate limiting information
- `examples`: Code examples and sample responses
- `user`: Current user (for authenticated documentation)
- `permissions`: User's API access permissions

## Usage Example
```python
# In views.py
from django.shortcuts import render
from django.contrib.auth.decorators import login_required

def api_documentation(request):
    context = {
        'api_version': '1.0',
        'endpoints': get_api_endpoints(),
        'user': request.user if request.user.is_authenticated else None,
    }
    return render(request, 'api/documentation.html', context)
```

## Integration with API Framework
- Compatible with Django REST Framework
- Supports automatic documentation generation
- Integrates with API versioning systems
- Provides consistent styling with main application

## Features
- Interactive API testing tools
- Syntax highlighting for code examples
- Copy-to-clipboard functionality
- Responsive design for mobile access
- Search and filtering capabilities
- Real-time API status indicators

## Development Guidelines
- Keep documentation up-to-date with API changes
- Include comprehensive examples for each endpoint
- Provide clear error handling guidance
- Use consistent formatting and styling
- Ensure accessibility for all users
- Test templates with different API responses
