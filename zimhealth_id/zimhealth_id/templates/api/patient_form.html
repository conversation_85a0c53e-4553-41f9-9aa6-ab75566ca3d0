{% extends 'base.html' %}

{% block title %}{% if patient %}Edit Patient{% else %}Add New Patient{% endif %} - ZimHealth-ID{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:patients' %}" class="text-medical-600 hover:text-medical-700 transition-colors">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </a>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">
                                {% if patient %}Edit Patient{% else %}Add New Patient{% endif %}
                            </h1>
                            <p class="text-gray-600 mt-1">
                                {% if patient %}Update patient information{% else %}Register a new patient in the system{% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <form method="post" enctype="multipart/form-data" class="space-y-8">
            {% csrf_token %}
            
            <!-- Personal Information -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-100">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-user text-medical-500 mr-2"></i>Personal Information
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- First Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-user text-medical-500 mr-2"></i>First Name *
                            </label>
                            <input type="text" name="first_name" 
                                   value="{{ form.first_name.value|default:'' }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors"
                                   placeholder="Enter first name" required>
                            {% if form.first_name.errors %}
                                <div class="mt-2 text-sm text-red-600">
                                    {% for error in form.first_name.errors %}
                                        <p class="flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                        </p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Last Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-user text-medical-500 mr-2"></i>Last Name *
                            </label>
                            <input type="text" name="last_name" 
                                   value="{{ form.last_name.value|default:'' }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors"
                                   placeholder="Enter last name" required>
                            {% if form.last_name.errors %}
                                <div class="mt-2 text-sm text-red-600">
                                    {% for error in form.last_name.errors %}
                                        <p class="flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                        </p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- National ID -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-id-card text-medical-500 mr-2"></i>National ID
                            </label>
                            <input type="text" name="national_id" 
                                   value="{{ form.national_id.value|default:'' }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors"
                                   placeholder="e.g., 63-123456A12">
                            {% if form.national_id.errors %}
                                <div class="mt-2 text-sm text-red-600">
                                    {% for error in form.national_id.errors %}
                                        <p class="flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                        </p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Date of Birth -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-birthday-cake text-medical-500 mr-2"></i>Date of Birth *
                            </label>
                            <input type="date" name="date_of_birth" 
                                   value="{{ form.date_of_birth.value|default:'' }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors"
                                   required>
                            {% if form.date_of_birth.errors %}
                                <div class="mt-2 text-sm text-red-600">
                                    {% for error in form.date_of_birth.errors %}
                                        <p class="flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                        </p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Gender -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-venus-mars text-medical-500 mr-2"></i>Gender *
                            </label>
                            <select name="gender" 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors"
                                    required>
                                <option value="">Select Gender</option>
                                <option value="M" {% if form.gender.value == 'M' %}selected{% endif %}>Male</option>
                                <option value="F" {% if form.gender.value == 'F' %}selected{% endif %}>Female</option>
                                <option value="O" {% if form.gender.value == 'O' %}selected{% endif %}>Other</option>
                            </select>
                            {% if form.gender.errors %}
                                <div class="mt-2 text-sm text-red-600">
                                    {% for error in form.gender.errors %}
                                        <p class="flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                        </p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Blood Type -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-tint text-medical-500 mr-2"></i>Blood Type
                            </label>
                            <select name="blood_type" 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors">
                                <option value="">Select Blood Type</option>
                                <option value="A+" {% if form.blood_type.value == 'A+' %}selected{% endif %}>A+</option>
                                <option value="A-" {% if form.blood_type.value == 'A-' %}selected{% endif %}>A-</option>
                                <option value="B+" {% if form.blood_type.value == 'B+' %}selected{% endif %}>B+</option>
                                <option value="B-" {% if form.blood_type.value == 'B-' %}selected{% endif %}>B-</option>
                                <option value="AB+" {% if form.blood_type.value == 'AB+' %}selected{% endif %}>AB+</option>
                                <option value="AB-" {% if form.blood_type.value == 'AB-' %}selected{% endif %}>AB-</option>
                                <option value="O+" {% if form.blood_type.value == 'O+' %}selected{% endif %}>O+</option>
                                <option value="O-" {% if form.blood_type.value == 'O-' %}selected{% endif %}>O-</option>
                            </select>
                            {% if form.blood_type.errors %}
                                <div class="mt-2 text-sm text-red-600">
                                    {% for error in form.blood_type.errors %}
                                        <p class="flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                        </p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-100">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-address-book text-health-500 mr-2"></i>Contact Information
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Phone Number -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-phone text-health-500 mr-2"></i>Phone Number *
                            </label>
                            <input type="tel" name="phone_number" 
                                   value="{{ form.phone_number.value|default:'' }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors"
                                   placeholder="+263 77 123 4567" required>
                            {% if form.phone_number.errors %}
                                <div class="mt-2 text-sm text-red-600">
                                    {% for error in form.phone_number.errors %}
                                        <p class="flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                        </p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Emergency Contact -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-phone-alt text-health-500 mr-2"></i>Emergency Contact *
                            </label>
                            <input type="tel" name="emergency_contact" 
                                   value="{{ form.emergency_contact.value|default:'' }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors"
                                   placeholder="+263 77 123 4567" required>
                            {% if form.emergency_contact.errors %}
                                <div class="mt-2 text-sm text-red-600">
                                    {% for error in form.emergency_contact.errors %}
                                        <p class="flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                        </p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Address -->
                    <div class="mt-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-map-marker-alt text-health-500 mr-2"></i>Address *
                        </label>
                        <textarea name="address" rows="3"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors resize-none"
                                  placeholder="Enter full address" required>{{ form.address.value|default:'' }}</textarea>
                        {% if form.address.errors %}
                            <div class="mt-2 text-sm text-red-600">
                                {% for error in form.address.errors %}
                                    <p class="flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                    </p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Medical Information -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-100">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-heartbeat text-red-500 mr-2"></i>Medical Information
                    </h3>
                </div>
                <div class="p-6">
                    <!-- Allergies -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>Known Allergies
                        </label>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                            {% for allergy in common_allergies %}
                                <label class="flex items-center">
                                    <input type="checkbox" name="allergies" value="{{ allergy }}" 
                                           {% if allergy in form.allergies.value %}checked{% endif %}
                                           class="rounded border-gray-300 text-red-600 focus:ring-red-500">
                                    <span class="ml-2 text-sm text-gray-700">{{ allergy }}</span>
                                </label>
                            {% endfor %}
                        </div>
                        <div class="mt-3">
                            <input type="text" name="other_allergies" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors"
                                   placeholder="Other allergies (comma separated)">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex flex-col sm:flex-row gap-4 pt-6">
                <button type="submit" 
                        class="flex-1 bg-medical-600 text-white py-3 px-6 rounded-lg hover:bg-medical-700 focus:ring-2 focus:ring-medical-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                    <i class="fas fa-save"></i>
                    <span>{% if patient %}Update Patient{% else %}Save Patient{% endif %}</span>
                </button>
                
                <a href="{% url 'api:patients' %}" 
                   class="flex-1 bg-gray-600 text-white py-3 px-6 rounded-lg hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                    <i class="fas fa-times"></i>
                    <span>Cancel</span>
                </a>
            </div>
        </form>
    </div>
</div>

<script>
// Common allergies for the form
const commonAllergies = ['Penicillin', 'Peanuts', 'Shellfish', 'Latex', 'Dust', 'Pollen', 'Eggs', 'Milk'];
</script>
{% endblock %}
