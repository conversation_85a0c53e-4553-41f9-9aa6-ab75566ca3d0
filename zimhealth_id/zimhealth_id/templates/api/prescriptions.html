{% extends 'base.html' %}

{% block title %}Prescriptions - ZimHealth-ID{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Prescription Management</h1>
                        <p class="text-gray-600 mt-1">Manage patient prescriptions and medications</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors font-medium flex items-center space-x-2">
                            <i class="fas fa-prescription-bottle-alt"></i>
                            <span>New Prescription</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-pills text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Active Prescriptions</p>
                        <p class="text-2xl font-bold text-gray-900">{{ active_prescriptions|default:0 }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Expiring Soon</p>
                        <p class="text-2xl font-bold text-gray-900">{{ expiring_prescriptions|default:0 }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Completed</p>
                        <p class="text-2xl font-bold text-gray-900">{{ completed_prescriptions|default:0 }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-ban text-red-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Discontinued</p>
                        <p class="text-2xl font-bold text-gray-900">{{ discontinued_prescriptions|default:0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 mb-6">
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <!-- Search -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Search Prescriptions</label>
                        <div class="relative">
                            <input type="text" id="prescription-search" 
                                   class="w-full px-4 py-3 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
                                   placeholder="Search by medication, patient, or doctor">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="completed">Completed</option>
                            <option value="discontinued">Discontinued</option>
                            <option value="on_hold">On Hold</option>
                        </select>
                    </div>

                    <!-- Frequency Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Frequency</label>
                        <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors">
                            <option value="">All Frequencies</option>
                            <option value="once_daily">Once Daily</option>
                            <option value="twice_daily">Twice Daily</option>
                            <option value="three_times_daily">Three Times Daily</option>
                            <option value="as_needed">As Needed</option>
                        </select>
                    </div>

                    <!-- Date Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                        <input type="date" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors">
                    </div>
                </div>
            </div>
        </div>

        <!-- Prescriptions List -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Prescriptions</h3>
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <span>{{ prescriptions.count }} prescriptions</span>
                    </div>
                </div>
            </div>

            <div class="divide-y divide-gray-200">
                {% for prescription in prescriptions %}
                    <div class="p-6 hover:bg-gray-50 transition-colors">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <!-- Patient and Medication Info -->
                                <div class="flex items-center space-x-4 mb-4">
                                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-pills text-purple-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-semibold text-gray-900">{{ prescription.medication }}</h4>
                                        <p class="text-sm text-gray-500">
                                            Patient: {{ prescription.medical_record.patient.full_name }} 
                                            ({{ prescription.medical_record.patient.zimhealth_id }})
                                        </p>
                                    </div>
                                    <div class="ml-auto">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            {% if prescription.status == 'active' %}bg-purple-100 text-purple-800
                                            {% elif prescription.status == 'completed' %}bg-green-100 text-green-800
                                            {% elif prescription.status == 'discontinued' %}bg-red-100 text-red-800
                                            {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                            {{ prescription.get_status_display }}
                                        </span>
                                    </div>
                                </div>

                                <!-- Prescription Details -->
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-4">
                                    <div>
                                        <h5 class="font-medium text-gray-900 mb-2">Dosage & Frequency</h5>
                                        <div class="space-y-1 text-sm text-gray-600">
                                            <div class="flex items-center">
                                                <i class="fas fa-pills w-4 text-purple-500 mr-2"></i>
                                                <span>{{ prescription.dosage }}</span>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-clock w-4 text-purple-500 mr-2"></i>
                                                <span>{{ prescription.get_frequency_display }}</span>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-calendar w-4 text-purple-500 mr-2"></i>
                                                <span>{{ prescription.duration }}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <h5 class="font-medium text-gray-900 mb-2">Prescription Details</h5>
                                        <div class="space-y-1 text-sm text-gray-600">
                                            <div>Quantity: {{ prescription.quantity_prescribed|default:"Not specified" }}</div>
                                            <div>Refills: {{ prescription.refills_allowed }}</div>
                                            <div>Start: {{ prescription.start_date|date:"M d, Y" }}</div>
                                            {% if prescription.end_date %}
                                                <div>End: {{ prescription.end_date|date:"M d, Y" }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div>
                                        <h5 class="font-medium text-gray-900 mb-2">Prescribed By</h5>
                                        <div class="space-y-1 text-sm text-gray-600">
                                            <div class="flex items-center">
                                                <i class="fas fa-user-md w-4 text-purple-500 mr-2"></i>
                                                <span>Dr. {{ prescription.medical_record.doctor_name }}</span>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-hospital w-4 text-purple-500 mr-2"></i>
                                                <span>{{ prescription.medical_record.facility_name }}</span>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-calendar w-4 text-purple-500 mr-2"></i>
                                                <span>{{ prescription.created_at|date:"M d, Y" }}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <h5 class="font-medium text-gray-900 mb-2">Status Info</h5>
                                        <div class="space-y-1 text-sm text-gray-600">
                                            <div>Active: 
                                                {% if prescription.is_active %}
                                                    <span class="text-green-600 font-medium">Yes</span>
                                                {% else %}
                                                    <span class="text-red-600 font-medium">No</span>
                                                {% endif %}
                                            </div>
                                            {% if prescription.end_date %}
                                                <div>Days remaining: 
                                                    {% with days_left=prescription.end_date|timeuntil %}
                                                        {% if days_left %}
                                                            <span class="font-medium">{{ days_left }}</span>
                                                        {% else %}
                                                            <span class="text-red-600 font-medium">Expired</span>
                                                        {% endif %}
                                                    {% endwith %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Instructions -->
                                {% if prescription.instructions %}
                                    <div class="mb-4">
                                        <h5 class="font-medium text-gray-900 mb-2">Instructions</h5>
                                        <p class="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">{{ prescription.instructions }}</p>
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Actions -->
                            <div class="flex flex-col space-y-2 ml-6">
                                <button class="text-purple-600 hover:text-purple-900 transition-colors p-2" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                {% if prescription.status == 'active' %}
                                    <button class="text-green-600 hover:text-green-900 transition-colors p-2" title="Mark Complete">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="text-yellow-600 hover:text-yellow-900 transition-colors p-2" title="Put on Hold">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-900 transition-colors p-2" title="Discontinue">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                {% endif %}
                                <button class="text-blue-600 hover:text-blue-900 transition-colors p-2" title="Print">
                                    <i class="fas fa-print"></i>
                                </button>
                                <button class="text-gray-600 hover:text-gray-900 transition-colors p-2" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                {% empty %}
                    <div class="p-12 text-center">
                        <div class="flex flex-col items-center">
                            <i class="fas fa-prescription-bottle-alt text-gray-300 text-4xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No prescriptions found</h3>
                            <p class="text-gray-500 mb-4">Start by creating your first prescription.</p>
                            <button class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors font-medium">
                                <i class="fas fa-plus mr-2"></i>New Prescription
                            </button>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if prescriptions.has_other_pages %}
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-500">
                            Showing {{ prescriptions.start_index }} to {{ prescriptions.end_index }} of {{ prescriptions.paginator.count }} results
                        </div>
                        <div class="flex items-center space-x-2">
                            {% if prescriptions.has_previous %}
                                <a href="?page={{ prescriptions.previous_page_number }}" 
                                   class="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                                    Previous
                                </a>
                            {% endif %}
                            
                            <span class="px-3 py-2 text-sm font-medium text-gray-700">
                                Page {{ prescriptions.number }} of {{ prescriptions.paginator.num_pages }}
                            </span>
                            
                            {% if prescriptions.has_next %}
                                <a href="?page={{ prescriptions.next_page_number }}" 
                                   class="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                                    Next
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Search functionality
document.getElementById('prescription-search').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const prescriptions = document.querySelectorAll('.divide-y > div');
    
    prescriptions.forEach(prescription => {
        const text = prescription.textContent.toLowerCase();
        prescription.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});
</script>
{% endblock %}
