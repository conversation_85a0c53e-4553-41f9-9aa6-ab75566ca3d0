{% extends 'base.html' %}

{% block title %}Appointments - ZimHealth-ID{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Appointments</h1>
                        <p class="text-gray-600 mt-1">Manage patient appointments and scheduling</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="bg-health-600 text-white px-4 py-2 rounded-lg hover:bg-health-700 transition-colors font-medium flex items-center space-x-2">
                            <i class="fas fa-calendar-plus"></i>
                            <span>Schedule Appointment</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-health-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-calendar-day text-health-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Today</p>
                        <p class="text-2xl font-bold text-gray-900">{{ todays_appointments|default:0 }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Pending</p>
                        <p class="text-2xl font-bold text-gray-900">{{ pending_appointments|default:0 }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Completed</p>
                        <p class="text-2xl font-bold text-gray-900">{{ completed_appointments|default:0 }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-times-circle text-red-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Cancelled</p>
                        <p class="text-2xl font-bold text-gray-900">{{ cancelled_appointments|default:0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 mb-6">
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <!-- Search -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Search Appointments</label>
                        <div class="relative">
                            <input type="text" id="appointment-search" 
                                   class="w-full px-4 py-3 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors"
                                   placeholder="Search by patient name or doctor">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors">
                            <option value="">All Status</option>
                            <option value="scheduled">Scheduled</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                            <option value="no_show">No Show</option>
                        </select>
                    </div>

                    <!-- Type Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Type</label>
                        <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors">
                            <option value="">All Types</option>
                            <option value="consultation">Consultation</option>
                            <option value="follow_up">Follow-up</option>
                            <option value="check_up">Check-up</option>
                            <option value="vaccination">Vaccination</option>
                            <option value="screening">Screening</option>
                        </select>
                    </div>

                    <!-- Date Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Date</label>
                        <input type="date" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors">
                    </div>
                </div>
            </div>
        </div>

        <!-- Appointments Calendar/List View Toggle -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Appointments</h3>
                    <div class="flex items-center space-x-2">
                        <button class="px-3 py-2 bg-health-100 text-health-700 rounded-lg font-medium text-sm">
                            <i class="fas fa-list mr-2"></i>List View
                        </button>
                        <button class="px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg font-medium text-sm">
                            <i class="fas fa-calendar mr-2"></i>Calendar View
                        </button>
                    </div>
                </div>
            </div>

            <!-- Appointments List -->
            <div class="divide-y divide-gray-200">
                {% for appointment in appointments %}
                    <div class="p-6 hover:bg-gray-50 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <!-- Status Indicator -->
                                <div class="w-4 h-4 rounded-full 
                                    {% if appointment.status == 'scheduled' %}bg-health-500
                                    {% elif appointment.status == 'completed' %}bg-green-500
                                    {% elif appointment.status == 'cancelled' %}bg-red-500
                                    {% else %}bg-yellow-500{% endif %}">
                                </div>

                                <!-- Patient Info -->
                                <div class="w-12 h-12 bg-medical-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-medical-600"></i>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-gray-900">{{ appointment.patient.full_name }}</h4>
                                    <p class="text-sm text-gray-500">{{ appointment.patient.zimhealth_id }}</p>
                                </div>
                            </div>

                            <!-- Appointment Details -->
                            <div class="flex items-center space-x-8">
                                <div class="text-center">
                                    <p class="text-sm font-medium text-gray-900">{{ appointment.date|date:"M d, Y" }}</p>
                                    <p class="text-sm text-gray-500">{{ appointment.time|time:"H:i" }}</p>
                                </div>

                                <div class="text-center">
                                    <p class="text-sm font-medium text-gray-900">Dr. {{ appointment.doctor_name }}</p>
                                    <p class="text-sm text-gray-500">{{ appointment.facility_name }}</p>
                                </div>

                                <div class="text-center">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if appointment.appointment_type == 'consultation' %}bg-blue-100 text-blue-800
                                        {% elif appointment.appointment_type == 'follow_up' %}bg-green-100 text-green-800
                                        {% elif appointment.appointment_type == 'check_up' %}bg-purple-100 text-purple-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ appointment.get_appointment_type_display }}
                                    </span>
                                    <p class="text-sm text-gray-500 mt-1">{{ appointment.estimated_duration }} min</p>
                                </div>

                                <div class="text-center">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if appointment.status == 'scheduled' %}bg-health-100 text-health-800
                                        {% elif appointment.status == 'completed' %}bg-green-100 text-green-800
                                        {% elif appointment.status == 'cancelled' %}bg-red-100 text-red-800
                                        {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                        {{ appointment.get_status_display }}
                                    </span>
                                    {% if appointment.priority != 'normal' %}
                                        <p class="text-sm text-red-600 mt-1 font-medium">{{ appointment.get_priority_display }} Priority</p>
                                    {% endif %}
                                </div>

                                <!-- Actions -->
                                <div class="flex items-center space-x-2">
                                    {% if appointment.status == 'scheduled' %}
                                        <button class="text-health-600 hover:text-health-900 transition-colors p-2" title="Mark Complete">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="text-blue-600 hover:text-blue-900 transition-colors p-2" title="Reschedule">
                                            <i class="fas fa-calendar-alt"></i>
                                        </button>
                                        <button class="text-red-600 hover:text-red-900 transition-colors p-2" title="Cancel">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    {% endif %}
                                    <button class="text-medical-600 hover:text-medical-900 transition-colors p-2" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-gray-600 hover:text-gray-900 transition-colors p-2" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Appointment Notes -->
                        {% if appointment.reason %}
                            <div class="mt-4 ml-20">
                                <p class="text-sm text-gray-600"><strong>Reason:</strong> {{ appointment.reason }}</p>
                            </div>
                        {% endif %}
                        {% if appointment.notes %}
                            <div class="mt-2 ml-20">
                                <p class="text-sm text-gray-600"><strong>Notes:</strong> {{ appointment.notes }}</p>
                            </div>
                        {% endif %}
                    </div>
                {% empty %}
                    <div class="p-12 text-center">
                        <div class="flex flex-col items-center">
                            <i class="fas fa-calendar text-gray-300 text-4xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No appointments found</h3>
                            <p class="text-gray-500 mb-4">Start by scheduling your first appointment.</p>
                            <button class="bg-health-600 text-white px-4 py-2 rounded-lg hover:bg-health-700 transition-colors font-medium">
                                <i class="fas fa-calendar-plus mr-2"></i>Schedule Appointment
                            </button>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if appointments.has_other_pages %}
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-500">
                            Showing {{ appointments.start_index }} to {{ appointments.end_index }} of {{ appointments.paginator.count }} results
                        </div>
                        <div class="flex items-center space-x-2">
                            {% if appointments.has_previous %}
                                <a href="?page={{ appointments.previous_page_number }}" 
                                   class="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                                    Previous
                                </a>
                            {% endif %}
                            
                            <span class="px-3 py-2 text-sm font-medium text-gray-700">
                                Page {{ appointments.number }} of {{ appointments.paginator.num_pages }}
                            </span>
                            
                            {% if appointments.has_next %}
                                <a href="?page={{ appointments.next_page_number }}" 
                                   class="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                                    Next
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Search functionality
document.getElementById('appointment-search').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const appointments = document.querySelectorAll('.divide-y > div');
    
    appointments.forEach(appointment => {
        const text = appointment.textContent.toLowerCase();
        appointment.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});
</script>
{% endblock %}
