{% extends 'base.html' %}

{% block title %}{% if record %}Edit Medical Record{% else %}New Medical Record{% endif %} - ZimHealth-ID{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:medical_records' %}" class="text-medical-600 hover:text-medical-700 transition-colors">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </a>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">
                                {% if record %}Edit Medical Record{% else %}New Medical Record{% endif %}
                            </h1>
                            <p class="text-gray-600 mt-1">
                                {% if patient %}For {{ patient.full_name }}{% else %}Create a new medical consultation record{% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <form method="post" class="space-y-8">
            {% csrf_token %}
            
            <!-- Patient Selection -->
            {% if not patient %}
            <div class="bg-white rounded-xl shadow-lg border border-gray-100">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-user text-medical-500 mr-2"></i>Patient Information
                    </h3>
                </div>
                <div class="p-6">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-search text-medical-500 mr-2"></i>Search Patient
                        </label>
                        <div class="relative">
                            <input type="text" id="patient-search" 
                                   class="w-full px-4 py-3 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors"
                                   placeholder="Search by name or ZimHealth ID">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                    <select name="patient" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors">
                        <option value="">Select Patient</option>
                        {% for p in patients %}
                            <option value="{{ p.zimhealth_id }}">{{ p.full_name }} ({{ p.zimhealth_id }})</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            {% endif %}

            <!-- Visit Information -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-100">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-hospital text-health-500 mr-2"></i>Visit Information
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Date -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-calendar text-health-500 mr-2"></i>Visit Date & Time *
                            </label>
                            <input type="datetime-local" name="date" 
                                   value="{{ form.date.value|default:'' }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors"
                                   required>
                        </div>

                        <!-- Facility -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-hospital text-health-500 mr-2"></i>Healthcare Facility *
                            </label>
                            <input type="text" name="facility_name" 
                                   value="{{ form.facility_name.value|default:'' }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors"
                                   placeholder="e.g., Harare Central Hospital" required>
                        </div>

                        <!-- Doctor -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-user-md text-health-500 mr-2"></i>Attending Doctor *
                            </label>
                            <input type="text" name="doctor_name" 
                                   value="{{ form.doctor_name.value|default:'' }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors"
                                   placeholder="Dr. John Smith" required>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Vital Signs -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-100">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-heartbeat text-red-500 mr-2"></i>Vital Signs
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                        <!-- Temperature -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Temperature (°C)</label>
                            <input type="number" step="0.1" name="temperature" 
                                   value="{{ form.temperature.value|default:'' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors"
                                   placeholder="36.5">
                        </div>

                        <!-- Blood Pressure Systolic -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">BP Systolic</label>
                            <input type="number" name="blood_pressure_systolic" 
                                   value="{{ form.blood_pressure_systolic.value|default:'' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors"
                                   placeholder="120">
                        </div>

                        <!-- Blood Pressure Diastolic -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">BP Diastolic</label>
                            <input type="number" name="blood_pressure_diastolic" 
                                   value="{{ form.blood_pressure_diastolic.value|default:'' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors"
                                   placeholder="80">
                        </div>

                        <!-- Heart Rate -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Heart Rate (bpm)</label>
                            <input type="number" name="heart_rate" 
                                   value="{{ form.heart_rate.value|default:'' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors"
                                   placeholder="72">
                        </div>

                        <!-- Weight -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Weight (kg)</label>
                            <input type="number" step="0.1" name="weight" 
                                   value="{{ form.weight.value|default:'' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors"
                                   placeholder="70.5">
                        </div>

                        <!-- Height -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Height (cm)</label>
                            <input type="number" step="0.1" name="height" 
                                   value="{{ form.height.value|default:'' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors"
                                   placeholder="175">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Medical Details -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-100">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-file-medical text-blue-500 mr-2"></i>Medical Details
                    </h3>
                </div>
                <div class="p-6 space-y-6">
                    <!-- Diagnosis -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-stethoscope text-blue-500 mr-2"></i>Diagnosis *
                        </label>
                        <textarea name="diagnosis" rows="4" required
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
                                  placeholder="Primary diagnosis and any secondary diagnoses">{{ form.diagnosis.value|default:'' }}</textarea>
                    </div>

                    <!-- Treatment -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-pills text-blue-500 mr-2"></i>Treatment *
                        </label>
                        <textarea name="treatment" rows="4" required
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
                                  placeholder="Treatment provided or recommended">{{ form.treatment.value|default:'' }}</textarea>
                    </div>

                    <!-- Notes -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-sticky-note text-blue-500 mr-2"></i>Additional Notes
                        </label>
                        <textarea name="notes" rows="3"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
                                  placeholder="Any additional observations or notes">{{ form.notes.value|default:'' }}</textarea>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex flex-col sm:flex-row gap-4 pt-6">
                <button type="submit" 
                        class="flex-1 bg-medical-600 text-white py-3 px-6 rounded-lg hover:bg-medical-700 focus:ring-2 focus:ring-medical-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                    <i class="fas fa-save"></i>
                    <span>{% if record %}Update Record{% else %}Save Record{% endif %}</span>
                </button>
                
                {% if not record %}
                <button type="submit" name="save_and_add_prescription"
                        class="flex-1 bg-purple-600 text-white py-3 px-6 rounded-lg hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                    <i class="fas fa-prescription-bottle-alt"></i>
                    <span>Save & Add Prescription</span>
                </button>
                {% endif %}
                
                <a href="{% url 'api:medical_records' %}" 
                   class="flex-1 bg-gray-600 text-white py-3 px-6 rounded-lg hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                    <i class="fas fa-times"></i>
                    <span>Cancel</span>
                </a>
            </div>
        </form>
    </div>
</div>

<script>
// Patient search functionality
document.getElementById('patient-search')?.addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const select = document.querySelector('select[name="patient"]');
    const options = select.querySelectorAll('option');
    
    options.forEach(option => {
        if (option.value === '') return; // Skip the default option
        const text = option.textContent.toLowerCase();
        option.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// Auto-calculate BMI
function calculateBMI() {
    const weight = parseFloat(document.querySelector('input[name="weight"]').value);
    const height = parseFloat(document.querySelector('input[name="height"]').value);
    
    if (weight && height) {
        const heightM = height / 100;
        const bmi = (weight / (heightM * heightM)).toFixed(1);
        
        // You could display BMI somewhere if needed
        console.log('BMI:', bmi);
    }
}

document.querySelector('input[name="weight"]')?.addEventListener('input', calculateBMI);
document.querySelector('input[name="height"]')?.addEventListener('input', calculateBMI);
</script>
{% endblock %}
