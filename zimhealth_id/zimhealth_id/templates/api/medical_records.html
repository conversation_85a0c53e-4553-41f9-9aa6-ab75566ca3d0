{% extends 'base.html' %}

{% block title %}Medical Records - ZimHealth-ID{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Medical Records</h1>
                        <p class="text-gray-600 mt-1">Manage patient medical records and consultations</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="bg-medical-600 text-white px-4 py-2 rounded-lg hover:bg-medical-700 transition-colors font-medium flex items-center space-x-2">
                            <i class="fas fa-plus"></i>
                            <span>New Medical Record</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Filters -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 mb-6">
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <!-- Search -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Search Records</label>
                        <div class="relative">
                            <input type="text" id="record-search" 
                                   class="w-full px-4 py-3 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors"
                                   placeholder="Search by patient name, diagnosis, or doctor">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Date Range -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">From Date</label>
                        <input type="date" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">To Date</label>
                        <input type="date" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors">
                    </div>

                    <!-- Facility Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Facility</label>
                        <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors">
                            <option value="">All Facilities</option>
                            <option value="harare-central">Harare Central Hospital</option>
                            <option value="parirenyatwa">Parirenyatwa Hospital</option>
                            <option value="chitungwiza">Chitungwiza Hospital</option>
                            <option value="mpilo">Mpilo Hospital</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Medical Records List -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Medical Records</h3>
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <span>{{ medical_records.count }} records</span>
                    </div>
                </div>
            </div>

            <div class="divide-y divide-gray-200">
                {% for record in medical_records %}
                    <div class="p-6 hover:bg-gray-50 transition-colors">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <!-- Patient Info -->
                                <div class="flex items-center space-x-4 mb-4">
                                    <div class="w-12 h-12 bg-medical-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-medical-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-semibold text-gray-900">{{ record.patient.full_name }}</h4>
                                        <p class="text-sm text-gray-500">ZimHealth ID: {{ record.patient.zimhealth_id }}</p>
                                    </div>
                                </div>

                                <!-- Record Details -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                                    <div>
                                        <h5 class="font-medium text-gray-900 mb-2">Visit Information</h5>
                                        <div class="space-y-2 text-sm text-gray-600">
                                            <div class="flex items-center">
                                                <i class="fas fa-hospital w-4 text-medical-500 mr-2"></i>
                                                <span>{{ record.facility_name }}</span>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-user-md w-4 text-medical-500 mr-2"></i>
                                                <span>Dr. {{ record.doctor_name }}</span>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-calendar w-4 text-medical-500 mr-2"></i>
                                                <span>{{ record.date|date:"M d, Y H:i" }}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <h5 class="font-medium text-gray-900 mb-2">Vital Signs</h5>
                                        <div class="grid grid-cols-2 gap-2 text-sm text-gray-600">
                                            {% if record.temperature %}
                                                <div>Temp: {{ record.temperature }}°C</div>
                                            {% endif %}
                                            {% if record.blood_pressure %}
                                                <div>BP: {{ record.blood_pressure }} mmHg</div>
                                            {% endif %}
                                            {% if record.heart_rate %}
                                                <div>HR: {{ record.heart_rate }} bpm</div>
                                            {% endif %}
                                            {% if record.weight %}
                                                <div>Weight: {{ record.weight }} kg</div>
                                            {% endif %}
                                            {% if record.height %}
                                                <div>Height: {{ record.height }} cm</div>
                                            {% endif %}
                                            {% if record.bmi %}
                                                <div>BMI: {{ record.bmi }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Diagnosis and Treatment -->
                                <div class="mb-4">
                                    <h5 class="font-medium text-gray-900 mb-2">Diagnosis</h5>
                                    <p class="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">{{ record.diagnosis }}</p>
                                </div>

                                <div class="mb-4">
                                    <h5 class="font-medium text-gray-900 mb-2">Treatment</h5>
                                    <p class="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">{{ record.treatment }}</p>
                                </div>

                                {% if record.notes %}
                                    <div class="mb-4">
                                        <h5 class="font-medium text-gray-900 mb-2">Notes</h5>
                                        <p class="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">{{ record.notes }}</p>
                                    </div>
                                {% endif %}

                                <!-- Prescriptions -->
                                {% if record.prescriptions.exists %}
                                    <div class="mb-4">
                                        <h5 class="font-medium text-gray-900 mb-2">Prescriptions ({{ record.prescriptions.count }})</h5>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                            {% for prescription in record.prescriptions.all %}
                                                <div class="bg-purple-50 border border-purple-200 rounded-lg p-3">
                                                    <div class="flex items-center justify-between mb-2">
                                                        <h6 class="font-medium text-purple-900">{{ prescription.medication }}</h6>
                                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                                            {% if prescription.status == 'active' %}bg-health-100 text-health-800
                                                            {% elif prescription.status == 'completed' %}bg-gray-100 text-gray-800
                                                            {% else %}bg-red-100 text-red-800{% endif %}">
                                                            {{ prescription.status|title }}
                                                        </span>
                                                    </div>
                                                    <div class="text-sm text-purple-700">
                                                        <div>{{ prescription.dosage }} • {{ prescription.get_frequency_display }}</div>
                                                        <div>Duration: {{ prescription.duration }}</div>
                                                        {% if prescription.instructions %}
                                                            <div class="mt-1 text-xs">{{ prescription.instructions }}</div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Actions -->
                            <div class="flex flex-col space-y-2 ml-6">
                                <button class="text-medical-600 hover:text-medical-900 transition-colors p-2" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="text-health-600 hover:text-health-900 transition-colors p-2" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-blue-600 hover:text-blue-900 transition-colors p-2" title="Print">
                                    <i class="fas fa-print"></i>
                                </button>
                                <button class="text-purple-600 hover:text-purple-900 transition-colors p-2" title="Add Prescription">
                                    <i class="fas fa-pills"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                {% empty %}
                    <div class="p-12 text-center">
                        <div class="flex flex-col items-center">
                            <i class="fas fa-file-medical text-gray-300 text-4xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No medical records found</h3>
                            <p class="text-gray-500 mb-4">Start by creating your first medical record.</p>
                            <button class="bg-medical-600 text-white px-4 py-2 rounded-lg hover:bg-medical-700 transition-colors font-medium">
                                <i class="fas fa-plus mr-2"></i>New Medical Record
                            </button>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if medical_records.has_other_pages %}
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-500">
                            Showing {{ medical_records.start_index }} to {{ medical_records.end_index }} of {{ medical_records.paginator.count }} results
                        </div>
                        <div class="flex items-center space-x-2">
                            {% if medical_records.has_previous %}
                                <a href="?page={{ medical_records.previous_page_number }}" 
                                   class="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                                    Previous
                                </a>
                            {% endif %}
                            
                            <span class="px-3 py-2 text-sm font-medium text-gray-700">
                                Page {{ medical_records.number }} of {{ medical_records.paginator.num_pages }}
                            </span>
                            
                            {% if medical_records.has_next %}
                                <a href="?page={{ medical_records.next_page_number }}" 
                                   class="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                                    Next
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Search functionality
document.getElementById('record-search').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const records = document.querySelectorAll('.divide-y > div');
    
    records.forEach(record => {
        const text = record.textContent.toLowerCase();
        record.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});
</script>
{% endblock %}
