{% extends 'base.html' %}

{% block title %}{{ patient.full_name }} - Patient Details{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:patients' %}" class="text-medical-600 hover:text-medical-700 transition-colors">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </a>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">{{ patient.full_name }}</h1>
                            <p class="text-gray-600 mt-1">ZimHealth ID: {{ patient.zimhealth_id }}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="bg-health-600 text-white px-4 py-2 rounded-lg hover:bg-health-700 transition-colors font-medium">
                            <i class="fas fa-edit mr-2"></i>Edit Patient
                        </button>
                        <button class="bg-medical-600 text-white px-4 py-2 rounded-lg hover:bg-medical-700 transition-colors font-medium">
                            <i class="fas fa-qrcode mr-2"></i>Show QR Code
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Patient Information -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl shadow-lg border border-gray-100">
                    <!-- Patient Avatar -->
                    <div class="bg-gradient-to-r from-medical-500 to-health-500 h-24 rounded-t-xl"></div>
                    <div class="px-6 pb-6">
                        <div class="flex flex-col items-center -mt-12">
                            <div class="w-24 h-24 rounded-full border-4 border-white shadow-lg bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-user text-gray-500 text-2xl"></i>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mt-4">{{ patient.full_name }}</h3>
                            <p class="text-gray-600">{{ patient.age }} years old</p>
                        </div>

                        <!-- Basic Info -->
                        <div class="mt-6 space-y-4">
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-id-card w-5 text-medical-500"></i>
                                <span class="ml-3 text-sm">{{ patient.national_id|default:"No National ID" }}</span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-phone w-5 text-medical-500"></i>
                                <span class="ml-3 text-sm">{{ patient.phone_number }}</span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-map-marker-alt w-5 text-medical-500"></i>
                                <span class="ml-3 text-sm">{{ patient.address }}</span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-phone-alt w-5 text-medical-500"></i>
                                <span class="ml-3 text-sm">Emergency: {{ patient.emergency_contact }}</span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-calendar w-5 text-medical-500"></i>
                                <span class="ml-3 text-sm">Born {{ patient.date_of_birth|date:"M d, Y" }}</span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-venus-mars w-5 text-medical-500"></i>
                                <span class="ml-3 text-sm">{{ patient.get_gender_display }}</span>
                            </div>
                        </div>

                        <!-- Medical Info -->
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <h4 class="font-semibold text-gray-900 mb-4">Medical Information</h4>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">Blood Type</span>
                                    {% if patient.blood_type %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            {{ patient.blood_type }}
                                        </span>
                                    {% else %}
                                        <span class="text-sm text-gray-400">Unknown</span>
                                    {% endif %}
                                </div>
                                <div>
                                    <span class="text-sm text-gray-600">Allergies</span>
                                    {% if patient.allergies %}
                                        <div class="mt-2 flex flex-wrap gap-2">
                                            {% for allergy in patient.allergies %}
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    {{ allergy }}
                                                </span>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        <p class="text-sm text-gray-400 mt-1">No known allergies</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Registration Info -->
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <h4 class="font-semibold text-gray-900 mb-4">Registration Details</h4>
                            <div class="space-y-2 text-sm text-gray-600">
                                <div>Registered: {{ patient.registration_date|date:"M d, Y H:i" }}</div>
                                <div>Last Visit: 
                                    {% if patient.last_visit %}
                                        {{ patient.last_visit|date:"M d, Y H:i" }}
                                    {% else %}
                                        <span class="text-gray-400">Never</span>
                                    {% endif %}
                                </div>
                                <div>Status: 
                                    {% if patient.is_active %}
                                        <span class="text-health-600 font-medium">Active</span>
                                    {% else %}
                                        <span class="text-red-600 font-medium">Inactive</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Medical Records and Appointments -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Quick Stats -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-file-medical text-blue-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Medical Records</p>
                                <p class="text-2xl font-bold text-gray-900">{{ patient.medical_records.count }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-health-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-calendar-check text-health-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Appointments</p>
                                <p class="text-2xl font-bold text-gray-900">{{ patient.appointments.count }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-pills text-purple-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Active Prescriptions</p>
                                <p class="text-2xl font-bold text-gray-900">{{ active_prescriptions_count|default:0 }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Medical Records -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">Recent Medical Records</h3>
                            <button class="text-medical-600 hover:text-medical-700 font-medium text-sm">
                                View All
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        {% if recent_records %}
                            <div class="space-y-4">
                                {% for record in recent_records %}
                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1">
                                                <div class="flex items-center space-x-2 mb-2">
                                                    <h4 class="font-medium text-gray-900">{{ record.facility_name }}</h4>
                                                    <span class="text-sm text-gray-500">•</span>
                                                    <span class="text-sm text-gray-500">Dr. {{ record.doctor_name }}</span>
                                                </div>
                                                <p class="text-sm text-gray-600 mb-2">{{ record.diagnosis|truncatechars:100 }}</p>
                                                <div class="flex items-center space-x-4 text-xs text-gray-500">
                                                    <span>{{ record.date|date:"M d, Y" }}</span>
                                                    {% if record.prescriptions.count %}
                                                        <span>{{ record.prescriptions.count }} prescription{{ record.prescriptions.count|pluralize }}</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <button class="text-medical-600 hover:text-medical-700 ml-4">
                                                <i class="fas fa-chevron-right"></i>
                                            </button>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-8">
                                <i class="fas fa-file-medical text-gray-300 text-4xl mb-4"></i>
                                <p class="text-gray-500">No medical records found</p>
                                <button class="mt-4 bg-medical-600 text-white px-4 py-2 rounded-lg hover:bg-medical-700 transition-colors font-medium">
                                    <i class="fas fa-plus mr-2"></i>Add Medical Record
                                </button>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Upcoming Appointments -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">Upcoming Appointments</h3>
                            <button class="text-health-600 hover:text-health-700 font-medium text-sm">
                                Schedule New
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        {% if upcoming_appointments %}
                            <div class="space-y-3">
                                {% for appointment in upcoming_appointments %}
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-health-100 rounded-lg flex items-center justify-center">
                                                <i class="fas fa-calendar text-health-600"></i>
                                            </div>
                                            <div>
                                                <p class="font-medium text-gray-900">{{ appointment.appointment_type|title }}</p>
                                                <p class="text-sm text-gray-500">Dr. {{ appointment.doctor_name }} • {{ appointment.facility_name }}</p>
                                                <p class="text-sm text-gray-500">{{ appointment.date|date:"M d, Y" }} at {{ appointment.time|time:"H:i" }}</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-health-100 text-health-800">
                                                {{ appointment.status|title }}
                                            </span>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-8">
                                <i class="fas fa-calendar text-gray-300 text-4xl mb-4"></i>
                                <p class="text-gray-500">No upcoming appointments</p>
                                <button class="mt-4 bg-health-600 text-white px-4 py-2 rounded-lg hover:bg-health-700 transition-colors font-medium">
                                    <i class="fas fa-plus mr-2"></i>Schedule Appointment
                                </button>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
