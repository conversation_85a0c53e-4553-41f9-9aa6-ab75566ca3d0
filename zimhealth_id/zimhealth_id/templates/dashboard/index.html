{% extends 'base.html' %}

{% block title %}Dashboard - ZimHealth-ID{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Dashboard Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
                        <p class="text-gray-600 mt-1">Welcome back, {{ user.first_name|default:user.username }}!</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-right">
                            <p class="text-sm text-gray-500">Last login</p>
                            <p class="text-sm font-medium text-gray-900">{{ user.last_login|date:"M d, Y H:i" }}</p>
                        </div>
                        <div class="w-12 h-12 bg-medical-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-medical-600 text-lg"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Patients -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-medical-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-users text-medical-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Patients</p>
                        <p class="text-2xl font-bold text-gray-900">{{ total_patients|default:0 }}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-sm text-health-600">
                        <i class="fas fa-arrow-up mr-1"></i>
                        <span>+12% from last month</span>
                    </div>
                </div>
            </div>

            <!-- Today's Appointments -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-health-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-calendar-check text-health-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Today's Appointments</p>
                        <p class="text-2xl font-bold text-gray-900">{{ todays_appointments|default:0 }}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-sm text-medical-600">
                        <i class="fas fa-clock mr-1"></i>
                        <span>{{ pending_appointments|default:0 }} pending</span>
                    </div>
                </div>
            </div>

            <!-- Medical Records -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-file-medical text-blue-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Medical Records</p>
                        <p class="text-2xl font-bold text-gray-900">{{ total_records|default:0 }}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-sm text-blue-600">
                        <i class="fas fa-plus mr-1"></i>
                        <span>{{ new_records|default:0 }} this week</span>
                    </div>
                </div>
            </div>

            <!-- Active Prescriptions -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-pills text-purple-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Active Prescriptions</p>
                        <p class="text-2xl font-bold text-gray-900">{{ active_prescriptions|default:0 }}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-sm text-purple-600">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        <span>{{ expiring_prescriptions|default:0 }} expiring soon</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Dashboard Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Recent Activity -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-lg border border-gray-100">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-clock text-medical-500 mr-2"></i>Recent Activity
                        </h3>
                    </div>
                    <div class="p-6">
                        {% if recent_activities %}
                            <div class="space-y-4">
                                {% for activity in recent_activities %}
                                    <div class="flex items-start space-x-4">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 bg-medical-100 rounded-full flex items-center justify-center">
                                                <i class="fas fa-{{ activity.icon }} text-medical-600 text-sm"></i>
                                            </div>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm font-medium text-gray-900">{{ activity.title }}</p>
                                            <p class="text-sm text-gray-500">{{ activity.description }}</p>
                                            <p class="text-xs text-gray-400 mt-1">{{ activity.timestamp|timesince }} ago</p>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-8">
                                <i class="fas fa-history text-gray-300 text-4xl mb-4"></i>
                                <p class="text-gray-500">No recent activity</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Quick Actions & Upcoming -->
            <div class="space-y-6">
                <!-- Quick Actions -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-bolt text-health-500 mr-2"></i>Quick Actions
                        </h3>
                    </div>
                    <div class="p-6 space-y-3">
                        <a href="#" class="w-full flex items-center justify-between p-3 bg-medical-50 hover:bg-medical-100 rounded-lg transition-colors">
                            <div class="flex items-center">
                                <i class="fas fa-user-plus text-medical-600 mr-3"></i>
                                <span class="font-medium text-gray-900">Add Patient</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </a>
                        <a href="#" class="w-full flex items-center justify-between p-3 bg-health-50 hover:bg-health-100 rounded-lg transition-colors">
                            <div class="flex items-center">
                                <i class="fas fa-calendar-plus text-health-600 mr-3"></i>
                                <span class="font-medium text-gray-900">Schedule Appointment</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </a>
                        <a href="#" class="w-full flex items-center justify-between p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                            <div class="flex items-center">
                                <i class="fas fa-file-medical-alt text-blue-600 mr-3"></i>
                                <span class="font-medium text-gray-900">New Medical Record</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </a>
                        <a href="#" class="w-full flex items-center justify-between p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
                            <div class="flex items-center">
                                <i class="fas fa-prescription-bottle-alt text-purple-600 mr-3"></i>
                                <span class="font-medium text-gray-900">Create Prescription</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </a>
                    </div>
                </div>

                <!-- Upcoming Appointments -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-calendar-alt text-health-500 mr-2"></i>Upcoming Appointments
                        </h3>
                    </div>
                    <div class="p-6">
                        {% if upcoming_appointments %}
                            <div class="space-y-3">
                                {% for appointment in upcoming_appointments %}
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div>
                                            <p class="font-medium text-gray-900">{{ appointment.patient.full_name }}</p>
                                            <p class="text-sm text-gray-500">{{ appointment.date|date:"M d" }} at {{ appointment.time|time:"H:i" }}</p>
                                        </div>
                                        <div class="text-right">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-health-100 text-health-800">
                                                {{ appointment.appointment_type|title }}
                                            </span>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-calendar text-gray-300 text-3xl mb-2"></i>
                                <p class="text-gray-500 text-sm">No upcoming appointments</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
