# Dashboard Templates Directory

This directory contains Django templates for dashboard-related pages in the ZimHealth-ID application.

## Purpose
- Store HTML templates for the main dashboard interface
- Organize user dashboard views and components
- Provide administrative and user-facing dashboard layouts
- Include data visualization and metrics templates

## Template Organization
```
dashboard/
├── index.html              # Main dashboard landing page
├── overview.html           # Dashboard overview with key metrics
├── profile.html            # User profile management
├── settings.html           # User settings and preferences
├── notifications.html      # Notifications and alerts page
├── reports.html            # Reports and analytics page
├── health_records.html     # Health records management
├── appointments.html       # Appointments scheduling and viewing
├── components/             # Reusable dashboard components
│   ├── sidebar.html        # Navigation sidebar
│   ├── header.html         # Dashboard header
│   ├── stats_cards.html    # Statistics cards widget
│   ├── chart_widgets.html  # Chart and graph widgets
│   └── data_tables.html    # Data table components
└── partials/              # Partial templates
    ├── user_info.html      # User information widget
    ├── quick_actions.html  # Quick action buttons
    └── recent_activity.html # Recent activity feed
```

## Template Context
Dashboard templates typically receive context variables such as:
- `user`: Current authenticated user object
- `health_data`: User's health information and records
- `appointments`: Upcoming and past appointments
- `notifications`: User notifications and alerts
- `statistics`: Dashboard metrics and analytics data
- `recent_activities`: User's recent activities
- `permissions`: User's access permissions

## Usage Example
```python
# In views.py
from django.shortcuts import render
from django.contrib.auth.decorators import login_required

@login_required
def dashboard_view(request):
    context = {
        'user': request.user,
        'statistics': get_user_statistics(request.user),
        'recent_activities': get_recent_activities(request.user),
    }
    return render(request, 'dashboard/index.html', context)
```

## Template Features
- Responsive design for mobile and desktop views
- Interactive widgets and components
- Real-time data updates (with JavaScript)
- Accessible design following WCAG guidelines
- Print-friendly layouts for reports

## Development Guidelines
- Use semantic HTML for better accessibility
- Implement progressive enhancement
- Optimize for performance and fast loading
- Follow consistent naming conventions
- Include proper error handling and loading states
