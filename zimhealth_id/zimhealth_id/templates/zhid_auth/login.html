{% extends 'base.html' %}

{% block title %}Login - ZimHealth-ID{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-medical-50 to-health-50">
    <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="mx-auto h-16 w-16 bg-medical-100 rounded-full flex items-center justify-center mb-4">
                <i class="fas fa-sign-in-alt text-medical-600 text-2xl"></i>
            </div>
            <h2 class="text-3xl font-bold text-gray-900 mb-2">Welcome Back</h2>
            <p class="text-gray-600">Sign in to your ZimHealth-ID account</p>
        </div>

        <!-- Login Form -->
        <div class="bg-white rounded-xl shadow-lg p-8 border border-gray-100">
            <form method="post" class="space-y-6">
                {% csrf_token %}

                <!-- Username/Email Field -->
                <div>
                    <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user text-medical-500 mr-2"></i>Username or Email
                    </label>
                    <div class="relative">
                        <input type="text" name="{{ form.username.name }}" id="{{ form.username.id_for_label }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors placeholder-gray-400"
                               placeholder="Enter your username or email" required>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-user text-gray-400"></i>
                        </div>
                    </div>
                    {% if form.username.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            {% for error in form.username.errors %}
                                <p class="flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                </p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Password Field -->
                <div>
                    <label for="{{ form.password.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-lock text-medical-500 mr-2"></i>Password
                    </label>
                    <div class="relative">
                        <input type="password" name="{{ form.password.name }}" id="{{ form.password.id_for_label }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors placeholder-gray-400"
                               placeholder="Enter your password" required>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <button type="button" onclick="togglePassword()" class="text-gray-400 hover:text-gray-600">
                                <i id="password-toggle-icon" class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    {% if form.password.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            {% for error in form.password.errors %}
                                <p class="flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                </p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Non-field Errors -->
                {% if form.non_field_errors %}
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        {% for error in form.non_field_errors %}
                            <p class="text-red-800 text-sm flex items-center">
                                <i class="fas fa-exclamation-triangle mr-2"></i>{{ error }}
                            </p>
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Submit Button -->
                <div>
                    <button type="submit"
                            class="w-full bg-medical-600 text-white py-3 px-4 rounded-lg hover:bg-medical-700 focus:ring-2 focus:ring-medical-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>Sign In</span>
                    </button>
                </div>

                <!-- Additional Links -->
                <div class="space-y-4">
                    <div class="text-center">
                        <a href="{% url 'zhid_auth:password_reset' %}"
                           class="text-medical-600 hover:text-medical-700 text-sm font-medium flex items-center justify-center space-x-2">
                            <i class="fas fa-key"></i>
                            <span>Forgot your password?</span>
                        </a>
                    </div>

                    <div class="text-center">
                        <a href="{% url 'zhid_auth:resend_verification' %}"
                           class="text-medical-600 hover:text-medical-700 text-sm font-medium flex items-center justify-center space-x-2">
                            <i class="fas fa-envelope"></i>
                            <span>Resend verification email</span>
                        </a>
                    </div>

                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white text-gray-500">Don't have an account?</span>
                        </div>
                    </div>

                    <div class="text-center">
                        <a href="{% url 'zhid_auth:register' %}"
                           class="w-full bg-health-600 text-white py-3 px-4 rounded-lg hover:bg-health-700 focus:ring-2 focus:ring-health-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                            <i class="fas fa-user-plus"></i>
                            <span>Create New Account</span>
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Security Notice -->
        <div class="text-center text-sm text-gray-500">
            <p class="flex items-center justify-center space-x-2">
                <i class="fas fa-shield-alt text-health-500"></i>
                <span>Your data is protected with enterprise-grade security</span>
            </p>
        </div>
    </div>
</div>

<script>
function togglePassword() {
    const passwordField = document.getElementById('{{ form.password.id_for_label }}');
    const toggleIcon = document.getElementById('password-toggle-icon');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}
</script>
{% endblock %}
