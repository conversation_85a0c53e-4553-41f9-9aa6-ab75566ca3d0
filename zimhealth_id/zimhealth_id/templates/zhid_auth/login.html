{% extends 'base.html' %}

{% block title %}Login - ZimHealth-ID{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <h2><i class="fas fa-sign-in-alt"></i> Login</h2>
            <p>Welcome back! Please sign in to your account.</p>
        </div>

        <form method="post">
            {% csrf_token %}
            
            <div class="mb-3">
                <label for="{{ form.username.id_for_label }}" class="form-label">
                    <i class="fas fa-user"></i> Username or Email
                </label>
                {{ form.username }}
                {% if form.username.errors %}
                    <div class="text-danger small mt-1">
                        {% for error in form.username.errors %}
                            <div>{{ error }}</div>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.password.id_for_label }}" class="form-label">
                    <i class="fas fa-lock"></i> Password
                </label>
                {{ form.password }}
                {% if form.password.errors %}
                    <div class="text-danger small mt-1">
                        {% for error in form.password.errors %}
                            <div>{{ error }}</div>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {% for error in form.non_field_errors %}
                        <div>{{ error }}</div>
                    {% endfor %}
                </div>
            {% endif %}

            <div class="d-grid gap-2 mb-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i> Sign In
                </button>
            </div>

            <div class="text-center">
                <p class="mb-2">
                    <a href="{% url 'zhid_auth:password_reset' %}" class="text-decoration-none">
                        <i class="fas fa-key"></i> Forgot your password?
                    </a>
                </p>
                <p class="mb-2">
                    <a href="{% url 'zhid_auth:resend_verification' %}" class="text-decoration-none">
                        <i class="fas fa-envelope"></i> Resend verification email
                    </a>
                </p>
                <hr>
                <p class="mb-0">
                    Don't have an account? 
                    <a href="{% url 'zhid_auth:register' %}" class="text-decoration-none">
                        <i class="fas fa-user-plus"></i> Sign up here
                    </a>
                </p>
            </div>
        </form>
    </div>
</div>
{% endblock %}
