{% extends 'base.html' %}

{% block title %}Set New Password - ZimHealth-ID{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        {% if validlink %}
            <div class="auth-header">
                <h2><i class="fas fa-lock"></i> Set New Password</h2>
                <p>Please enter your new password below.</p>
            </div>

            <form method="post">
                {% csrf_token %}
                
                <div class="mb-3">
                    <label for="{{ form.new_password1.id_for_label }}" class="form-label">
                        <i class="fas fa-key"></i> New Password
                    </label>
                    {{ form.new_password1 }}
                    {% if form.new_password1.errors %}
                        <div class="text-danger small mt-1">
                            {% for error in form.new_password1.errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="{{ form.new_password2.id_for_label }}" class="form-label">
                        <i class="fas fa-key"></i> Confirm New Password
                    </label>
                    {{ form.new_password2 }}
                    {% if form.new_password2.errors %}
                        <div class="text-danger small mt-1">
                            {% for error in form.new_password2.errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            <div>{{ error }}</div>
                        {% endfor %}
                    </div>
                {% endif %}

                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Password Requirements:</strong>
                    <ul class="list-unstyled mt-2 mb-0">
                        <li>• At least 8 characters long</li>
                        <li>• Cannot be too similar to your personal information</li>
                        <li>• Cannot be a commonly used password</li>
                        <li>• Cannot be entirely numeric</li>
                    </ul>
                </div>

                <div class="d-grid gap-2 mb-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Set New Password
                    </button>
                </div>
            </form>
        {% else %}
            <div class="auth-header text-center">
                <h2><i class="fas fa-exclamation-triangle text-danger"></i> Invalid Link</h2>
                <p>This password reset link is invalid or has expired.</p>
            </div>

            <div class="alert alert-danger">
                <i class="fas fa-times-circle"></i>
                The password reset link you used is no longer valid. This could be because:
                <ul class="mt-2 mb-0">
                    <li>The link has expired</li>
                    <li>The link has already been used</li>
                    <li>The link was malformed</li>
                </ul>
            </div>

            <div class="d-grid gap-2 mb-3">
                <a href="{% url 'zhid_auth:password_reset' %}" class="btn btn-primary">
                    <i class="fas fa-redo"></i> Request New Reset Link
                </a>
            </div>
        {% endif %}

        <div class="text-center">
            <p class="mb-0">
                Remember your password? 
                <a href="{% url 'zhid_auth:login' %}" class="text-decoration-none">
                    <i class="fas fa-sign-in-alt"></i> Sign in here
                </a>
            </p>
        </div>
    </div>
</div>
{% endblock %}
