{% extends 'base.html' %}

{% block title %}Password Reset - ZimHealth-ID{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <h2><i class="fas fa-key"></i> Reset Password</h2>
            <p>Enter your email address and we'll send you a link to reset your password.</p>
        </div>

        <form method="post">
            {% csrf_token %}
            
            <div class="mb-3">
                <label for="{{ form.email.id_for_label }}" class="form-label">
                    <i class="fas fa-envelope"></i> Email Address
                </label>
                {{ form.email }}
                {% if form.email.errors %}
                    <div class="text-danger small mt-1">
                        {% for error in form.email.errors %}
                            <div>{{ error }}</div>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {% for error in form.non_field_errors %}
                        <div>{{ error }}</div>
                    {% endfor %}
                </div>
            {% endif %}

            <div class="d-grid gap-2 mb-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i> Send Reset Link
                </button>
            </div>

            <div class="text-center">
                <p class="mb-2">
                    Remember your password? 
                    <a href="{% url 'zhid_auth:login' %}" class="text-decoration-none">
                        <i class="fas fa-sign-in-alt"></i> Sign in here
                    </a>
                </p>
                <p class="mb-0">
                    Don't have an account? 
                    <a href="{% url 'zhid_auth:register' %}" class="text-decoration-none">
                        <i class="fas fa-user-plus"></i> Create one here
                    </a>
                </p>
            </div>
        </form>
    </div>
</div>
{% endblock %}
