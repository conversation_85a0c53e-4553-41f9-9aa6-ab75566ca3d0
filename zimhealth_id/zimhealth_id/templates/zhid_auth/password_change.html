{% extends 'base.html' %}

{% block title %}Change Password - ZimHealth-ID{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center space-x-4">
                <a href="{% url 'zhid_auth:profile' %}"
                   class="text-medical-600 hover:text-medical-700 transition-colors">
                    <i class="fas fa-arrow-left text-xl"></i>
                </a>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Change Password</h1>
                    <p class="text-gray-600 mt-1">Update your account password for better security</p>
                </div>
            </div>
        </div>

        <!-- Password Change Form -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-key text-medical-500 mr-2"></i>Update Password
                </h2>
                <p class="text-gray-600 text-sm mt-1">Choose a strong password to keep your account secure</p>
            </div>

            <div class="p-6">
                <form method="post" class="space-y-6">
                    {% csrf_token %}

                    <!-- Current Password -->
                    <div>
                        <label for="{{ form.old_password.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-lock text-medical-500 mr-2"></i>Current Password
                        </label>
                        <div class="relative">
                            <input type="password" name="{{ form.old_password.name }}" id="{{ form.old_password.id_for_label }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors placeholder-gray-400"
                                   placeholder="Enter your current password" required>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <button type="button" onclick="togglePassword('{{ form.old_password.id_for_label }}')" class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        {% if form.old_password.errors %}
                            <div class="mt-2 text-sm text-red-600">
                                {% for error in form.old_password.errors %}
                                    <p class="flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                    </p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- New Password -->
                    <div>
                        <label for="{{ form.new_password1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-key text-medical-500 mr-2"></i>New Password
                        </label>
                        <div class="relative">
                            <input type="password" name="{{ form.new_password1.name }}" id="{{ form.new_password1.id_for_label }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors placeholder-gray-400"
                                   placeholder="Enter your new password" required>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <button type="button" onclick="togglePassword('{{ form.new_password1.id_for_label }}')" class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        {% if form.new_password1.errors %}
                            <div class="mt-2 text-sm text-red-600">
                                {% for error in form.new_password1.errors %}
                                    <p class="flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                    </p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Confirm New Password -->
                    <div>
                        <label for="{{ form.new_password2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-key text-medical-500 mr-2"></i>Confirm New Password
                        </label>
                        <div class="relative">
                            <input type="password" name="{{ form.new_password2.name }}" id="{{ form.new_password2.id_for_label }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors placeholder-gray-400"
                                   placeholder="Confirm your new password" required>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <button type="button" onclick="togglePassword('{{ form.new_password2.id_for_label }}')" class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        {% if form.new_password2.errors %}
                            <div class="mt-2 text-sm text-red-600">
                                {% for error in form.new_password2.errors %}
                                    <p class="flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                    </p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Non-field Errors -->
                    {% if form.non_field_errors %}
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            {% for error in form.non_field_errors %}
                                <p class="text-red-800 text-sm flex items-center">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>{{ error }}
                                </p>
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- Password Requirements -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-start">
                            <i class="fas fa-info-circle text-blue-500 mt-0.5 mr-3"></i>
                            <div>
                                <p class="text-blue-800 font-medium text-sm mb-2">Password Requirements:</p>
                                <ul class="text-blue-700 text-sm space-y-1">
                                    <li class="flex items-center"><i class="fas fa-check text-blue-500 mr-2 text-xs"></i>At least 8 characters long</li>
                                    <li class="flex items-center"><i class="fas fa-check text-blue-500 mr-2 text-xs"></i>Not similar to personal information</li>
                                    <li class="flex items-center"><i class="fas fa-check text-blue-500 mr-2 text-xs"></i>Not a commonly used password</li>
                                    <li class="flex items-center"><i class="fas fa-check text-blue-500 mr-2 text-xs"></i>Not entirely numeric</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 pt-4 border-t border-gray-200">
                        <button type="submit"
                                class="flex-1 bg-medical-600 text-white py-3 px-4 rounded-lg hover:bg-medical-700 focus:ring-2 focus:ring-medical-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                            <i class="fas fa-save"></i>
                            <span>Update Password</span>
                        </button>

                        <a href="{% url 'zhid_auth:profile' %}"
                           class="flex-1 bg-gray-600 text-white py-3 px-4 rounded-lg hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                            <i class="fas fa-arrow-left"></i>
                            <span>Back to Profile</span>
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Security Tips -->
        <div class="mt-6 bg-health-50 border border-health-200 rounded-lg p-4">
            <div class="flex items-start">
                <i class="fas fa-shield-alt text-health-500 mt-0.5 mr-3"></i>
                <div>
                    <p class="text-health-800 font-medium text-sm mb-2">Security Tips:</p>
                    <ul class="text-health-700 text-sm space-y-1">
                        <li>• Use a unique password that you don't use elsewhere</li>
                        <li>• Consider using a password manager</li>
                        <li>• Include a mix of letters, numbers, and symbols</li>
                        <li>• Avoid using personal information in your password</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const toggleIcon = passwordField.parentElement.querySelector('i');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}
</script>
{% endblock %}
