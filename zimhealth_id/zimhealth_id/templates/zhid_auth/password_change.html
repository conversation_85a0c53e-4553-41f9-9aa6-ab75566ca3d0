{% extends 'base.html' %}

{% block title %}Change Password - ZimHealth-ID{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-key"></i> Change Password</h4>
                    <p class="mb-0 text-muted">Update your account password</p>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.old_password.id_for_label }}" class="form-label">
                                <i class="fas fa-lock"></i> Current Password
                            </label>
                            {{ form.old_password }}
                            {% if form.old_password.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.old_password.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.new_password1.id_for_label }}" class="form-label">
                                <i class="fas fa-key"></i> New Password
                            </label>
                            {{ form.new_password1 }}
                            {% if form.new_password1.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.new_password1.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.new_password2.id_for_label }}" class="form-label">
                                <i class="fas fa-key"></i> Confirm New Password
                            </label>
                            {{ form.new_password2 }}
                            {% if form.new_password2.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.new_password2.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Password Requirements:</strong>
                            <ul class="list-unstyled mt-2 mb-0">
                                <li>• At least 8 characters long</li>
                                <li>• Cannot be too similar to your personal information</li>
                                <li>• Cannot be a commonly used password</li>
                                <li>• Cannot be entirely numeric</li>
                            </ul>
                        </div>

                        <div class="d-grid gap-2 mb-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Change Password
                            </button>
                        </div>

                        <div class="text-center">
                            <a href="{% url 'zhid_auth:profile' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Profile
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
