{% extends 'base.html' %}

{% block title %}Register - ZimHealth-ID{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <h2><i class="fas fa-user-plus"></i> Create Account</h2>
            <p>Join <PERSON><PERSON><PERSON>-ID to manage your healthcare identity.</p>
        </div>

        <form method="post">
            {% csrf_token %}
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.first_name.id_for_label }}" class="form-label">
                        <i class="fas fa-user"></i> First Name
                    </label>
                    {{ form.first_name }}
                    {% if form.first_name.errors %}
                        <div class="text-danger small mt-1">
                            {% for error in form.first_name.errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="{{ form.last_name.id_for_label }}" class="form-label">
                        <i class="fas fa-user"></i> Last Name
                    </label>
                    {{ form.last_name }}
                    {% if form.last_name.errors %}
                        <div class="text-danger small mt-1">
                            {% for error in form.last_name.errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>

            <div class="mb-3">
                <label for="{{ form.username.id_for_label }}" class="form-label">
                    <i class="fas fa-at"></i> Username
                </label>
                {{ form.username }}
                {% if form.username.errors %}
                    <div class="text-danger small mt-1">
                        {% for error in form.username.errors %}
                            <div>{{ error }}</div>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.email.id_for_label }}" class="form-label">
                    <i class="fas fa-envelope"></i> Email Address
                </label>
                {{ form.email }}
                {% if form.email.errors %}
                    <div class="text-danger small mt-1">
                        {% for error in form.email.errors %}
                            <div>{{ error }}</div>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.phone_number.id_for_label }}" class="form-label">
                    <i class="fas fa-phone"></i> Phone Number (Optional)
                </label>
                {{ form.phone_number }}
                {% if form.phone_number.errors %}
                    <div class="text-danger small mt-1">
                        {% for error in form.phone_number.errors %}
                            <div>{{ error }}</div>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.password1.id_for_label }}" class="form-label">
                    <i class="fas fa-lock"></i> Password
                </label>
                {{ form.password1 }}
                {% if form.password1.errors %}
                    <div class="text-danger small mt-1">
                        {% for error in form.password1.errors %}
                            <div>{{ error }}</div>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.password2.id_for_label }}" class="form-label">
                    <i class="fas fa-lock"></i> Confirm Password
                </label>
                {{ form.password2 }}
                {% if form.password2.errors %}
                    <div class="text-danger small mt-1">
                        {% for error in form.password2.errors %}
                            <div>{{ error }}</div>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {% for error in form.non_field_errors %}
                        <div>{{ error }}</div>
                    {% endfor %}
                </div>
            {% endif %}

            <div class="d-grid gap-2 mb-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i> Create Account
                </button>
            </div>

            <div class="text-center">
                <p class="mb-0">
                    Already have an account? 
                    <a href="{% url 'zhid_auth:login' %}" class="text-decoration-none">
                        <i class="fas fa-sign-in-alt"></i> Sign in here
                    </a>
                </p>
            </div>
        </form>
    </div>
</div>
{% endblock %}
