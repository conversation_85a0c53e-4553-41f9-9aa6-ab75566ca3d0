{% extends 'base.html' %}

{% block title %}Profile - ZimHealth-ID{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">My Profile</h1>
            <p class="text-gray-600 mt-2">Manage your ZimHealth-ID account information</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Profile Summary Card -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="bg-gradient-to-r from-medical-500 to-health-500 h-24"></div>
                    <div class="px-6 pb-6">
                        <div class="flex flex-col items-center -mt-12">
                            {% if profile.avatar %}
                                <img src="{{ profile.avatar.url }}" alt="Profile Picture"
                                     class="w-24 h-24 rounded-full border-4 border-white shadow-lg object-cover">
                            {% else %}
                                <div class="w-24 h-24 rounded-full border-4 border-white shadow-lg bg-gray-200 flex items-center justify-center">
                                    <i class="fas fa-user text-gray-500 text-2xl"></i>
                                </div>
                            {% endif %}

                            <h3 class="text-xl font-bold text-gray-900 mt-4">{{ user.first_name }} {{ user.last_name }}</h3>
                            <p class="text-gray-600">@{{ user.username }}</p>

                            <!-- Email Verification Status -->
                            <div class="mt-4">
                                {% if profile.is_email_verified %}
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-health-100 text-health-800">
                                        <i class="fas fa-check-circle mr-2"></i>Email Verified
                                    </span>
                                {% else %}
                                    <div class="text-center">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 mb-2">
                                            <i class="fas fa-exclamation-circle mr-2"></i>Email Not Verified
                                        </span>
                                        <div>
                                            <a href="{% url 'zhid_auth:resend_verification' %}"
                                               class="text-medical-600 hover:text-medical-700 text-sm font-medium">
                                                <i class="fas fa-envelope mr-1"></i>Verify Email
                                            </a>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Profile Details -->
                        <div class="mt-6 space-y-4">
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-envelope w-5 text-medical-500"></i>
                                <span class="ml-3 text-sm">{{ user.email }}</span>
                            </div>
                            {% if profile.phone_number %}
                                <div class="flex items-center text-gray-600">
                                    <i class="fas fa-phone w-5 text-medical-500"></i>
                                    <span class="ml-3 text-sm">{{ profile.phone_number }}</span>
                                </div>
                            {% endif %}
                            {% if profile.date_of_birth %}
                                <div class="flex items-center text-gray-600">
                                    <i class="fas fa-birthday-cake w-5 text-medical-500"></i>
                                    <span class="ml-3 text-sm">{{ profile.date_of_birth }}</span>
                                </div>
                            {% endif %}
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-calendar w-5 text-medical-500"></i>
                                <span class="ml-3 text-sm">Joined {{ user.date_joined|date:"M d, Y" }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 mt-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h4 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-cogs text-medical-500 mr-2"></i>Quick Actions
                        </h4>
                    </div>
                    <div class="p-6 space-y-3">
                        <a href="{% url 'zhid_auth:password_change' %}"
                           class="w-full flex items-center justify-center px-4 py-3 border border-medical-300 rounded-lg text-medical-700 hover:bg-medical-50 transition-colors font-medium">
                            <i class="fas fa-key mr-2"></i>Change Password
                        </a>
                        <a href="{% url 'zhid_auth:logout' %}"
                           class="w-full flex items-center justify-center px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors font-medium">
                            <i class="fas fa-sign-out-alt mr-2"></i>Logout
                        </a>
                    </div>
                </div>
            </div>

            <!-- Profile Edit Form -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-lg border border-gray-100">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h4 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-edit text-medical-500 mr-2"></i>Edit Profile
                        </h4>
                        <p class="text-gray-600 text-sm mt-1">Update your personal information</p>
                    </div>
                    <div class="p-6">
                        <form method="post" enctype="multipart/form-data" class="space-y-6">
                            {% csrf_token %}

                            <!-- Name Fields -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-user text-medical-500 mr-2"></i>First Name
                                    </label>
                                    <input type="text" name="{{ form.first_name.name }}" id="{{ form.first_name.id_for_label }}"
                                           value="{{ form.first_name.value|default:'' }}"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors">
                                    {% if form.first_name.errors %}
                                        <div class="mt-2 text-sm text-red-600">
                                            {% for error in form.first_name.errors %}
                                                <p class="flex items-center">
                                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                                </p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div>
                                    <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-user text-medical-500 mr-2"></i>Last Name
                                    </label>
                                    <input type="text" name="{{ form.last_name.name }}" id="{{ form.last_name.id_for_label }}"
                                           value="{{ form.last_name.value|default:'' }}"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors">
                                    {% if form.last_name.errors %}
                                        <div class="mt-2 text-sm text-red-600">
                                            {% for error in form.last_name.errors %}
                                                <p class="flex items-center">
                                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                                </p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Email Field -->
                            <div>
                                <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-envelope text-medical-500 mr-2"></i>Email Address
                                </label>
                                <input type="email" name="{{ form.email.name }}" id="{{ form.email.id_for_label }}"
                                       value="{{ form.email.value|default:'' }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors">
                                {% if form.email.errors %}
                                    <div class="mt-2 text-sm text-red-600">
                                        {% for error in form.email.errors %}
                                            <p class="flex items-center">
                                                <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                            </p>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Phone and Date of Birth -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="{{ form.phone_number.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-phone text-medical-500 mr-2"></i>Phone Number
                                    </label>
                                    <input type="tel" name="{{ form.phone_number.name }}" id="{{ form.phone_number.id_for_label }}"
                                           value="{{ form.phone_number.value|default:'' }}"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors">
                                    {% if form.phone_number.errors %}
                                        <div class="mt-2 text-sm text-red-600">
                                            {% for error in form.phone_number.errors %}
                                                <p class="flex items-center">
                                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                                </p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div>
                                    <label for="{{ form.date_of_birth.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-birthday-cake text-medical-500 mr-2"></i>Date of Birth
                                    </label>
                                    <input type="date" name="{{ form.date_of_birth.name }}" id="{{ form.date_of_birth.id_for_label }}"
                                           value="{{ form.date_of_birth.value|default:'' }}"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors">
                                    {% if form.date_of_birth.errors %}
                                        <div class="mt-2 text-sm text-red-600">
                                            {% for error in form.date_of_birth.errors %}
                                                <p class="flex items-center">
                                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                                </p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Address Field -->
                            <div>
                                <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-map-marker-alt text-medical-500 mr-2"></i>Address
                                </label>
                                <textarea name="{{ form.address.name }}" id="{{ form.address.id_for_label }}" rows="3"
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors resize-none"
                                          placeholder="Enter your address">{{ form.address.value|default:'' }}</textarea>
                                {% if form.address.errors %}
                                    <div class="mt-2 text-sm text-red-600">
                                        {% for error in form.address.errors %}
                                            <p class="flex items-center">
                                                <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                            </p>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Bio Field -->
                            <div>
                                <label for="{{ form.bio.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-info-circle text-medical-500 mr-2"></i>Bio
                                </label>
                                <textarea name="{{ form.bio.name }}" id="{{ form.bio.id_for_label }}" rows="4"
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors resize-none"
                                          placeholder="Tell us about yourself">{{ form.bio.value|default:'' }}</textarea>
                                {% if form.bio.errors %}
                                    <div class="mt-2 text-sm text-red-600">
                                        {% for error in form.bio.errors %}
                                            <p class="flex items-center">
                                                <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                            </p>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Avatar Upload -->
                            <div>
                                <label for="{{ form.avatar.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-image text-medical-500 mr-2"></i>Profile Picture
                                </label>
                                <div class="flex items-center space-x-4">
                                    <div class="flex-shrink-0">
                                        {% if profile.avatar %}
                                            <img src="{{ profile.avatar.url }}" alt="Current avatar" class="w-16 h-16 rounded-full object-cover border-2 border-gray-300">
                                        {% else %}
                                            <div class="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center border-2 border-gray-300">
                                                <i class="fas fa-user text-gray-500"></i>
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="flex-1">
                                        <input type="file" name="{{ form.avatar.name }}" id="{{ form.avatar.id_for_label }}"
                                               accept="image/*"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-medical-50 file:text-medical-700 hover:file:bg-medical-100">
                                        <p class="text-sm text-gray-500 mt-1">PNG, JPG, GIF up to 10MB</p>
                                    </div>
                                </div>
                                {% if form.avatar.errors %}
                                    <div class="mt-2 text-sm text-red-600">
                                        {% for error in form.avatar.errors %}
                                            <p class="flex items-center">
                                                <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                            </p>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Submit Button -->
                            <div class="pt-4 border-t border-gray-200">
                                <button type="submit"
                                        class="w-full bg-medical-600 text-white py-3 px-4 rounded-lg hover:bg-medical-700 focus:ring-2 focus:ring-medical-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                                    <i class="fas fa-save"></i>
                                    <span>Update Profile</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
