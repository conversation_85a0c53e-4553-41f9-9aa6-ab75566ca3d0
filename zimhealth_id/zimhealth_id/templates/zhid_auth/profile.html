{% extends 'base.html' %}

{% block title %}Profile - ZimHealth-ID{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-4">
            <!-- Profile Summary Card -->
            <div class="card">
                <div class="card-body text-center">
                    {% if profile.avatar %}
                        <img src="{{ profile.avatar.url }}" alt="Profile Picture" class="profile-avatar mb-3">
                    {% else %}
                        <div class="profile-avatar mb-3 d-flex align-items-center justify-content-center bg-secondary text-white" style="font-size: 2rem;">
                            <i class="fas fa-user"></i>
                        </div>
                    {% endif %}
                    
                    <h4>{{ user.first_name }} {{ user.last_name }}</h4>
                    <p class="text-muted">@{{ user.username }}</p>
                    
                    {% if profile.is_email_verified %}
                        <span class="verification-badge">
                            <i class="fas fa-check-circle"></i> Email Verified
                        </span>
                    {% else %}
                        <span class="unverified-badge">
                            <i class="fas fa-exclamation-circle"></i> Email Not Verified
                        </span>
                        <div class="mt-2">
                            <a href="{% url 'zhid_auth:resend_verification' %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-envelope"></i> Verify Email
                            </a>
                        </div>
                    {% endif %}
                    
                    <hr>
                    
                    <div class="text-start">
                        <p><i class="fas fa-envelope"></i> {{ user.email }}</p>
                        {% if profile.phone_number %}
                            <p><i class="fas fa-phone"></i> {{ profile.phone_number }}</p>
                        {% endif %}
                        {% if profile.date_of_birth %}
                            <p><i class="fas fa-birthday-cake"></i> {{ profile.date_of_birth }}</p>
                        {% endif %}
                        <p><i class="fas fa-calendar"></i> Joined {{ user.date_joined|date:"M d, Y" }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-cogs"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'zhid_auth:password_change' %}" class="btn btn-outline-primary">
                            <i class="fas fa-key"></i> Change Password
                        </a>
                        <a href="{% url 'zhid_auth:logout' %}" class="btn btn-outline-danger">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <!-- Profile Edit Form -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-edit"></i> Edit Profile</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                    <i class="fas fa-user"></i> First Name
                                </label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.first_name.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                    <i class="fas fa-user"></i> Last Name
                                </label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.last_name.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                <i class="fas fa-envelope"></i> Email Address
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.email.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.phone_number.id_for_label }}" class="form-label">
                                    <i class="fas fa-phone"></i> Phone Number
                                </label>
                                {{ form.phone_number }}
                                {% if form.phone_number.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.phone_number.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">
                                    <i class="fas fa-birthday-cake"></i> Date of Birth
                                </label>
                                {{ form.date_of_birth }}
                                {% if form.date_of_birth.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.date_of_birth.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.address.id_for_label }}" class="form-label">
                                <i class="fas fa-map-marker-alt"></i> Address
                            </label>
                            {{ form.address }}
                            {% if form.address.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.address.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.bio.id_for_label }}" class="form-label">
                                <i class="fas fa-info-circle"></i> Bio
                            </label>
                            {{ form.bio }}
                            {% if form.bio.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.bio.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.avatar.id_for_label }}" class="form-label">
                                <i class="fas fa-image"></i> Profile Picture
                            </label>
                            {{ form.avatar }}
                            {% if form.avatar.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.avatar.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
