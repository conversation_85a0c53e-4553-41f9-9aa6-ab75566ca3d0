from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import Patient, MedicalRecord, Prescription, Appointment


class PrescriptionInline(admin.TabularInline):
    """Inline admin for prescriptions within medical records"""
    model = Prescription
    extra = 1
    fields = ('medication', 'dosage', 'frequency', 'duration', 'status')
    readonly_fields = ('id',)


class MedicalRecordInline(admin.TabularInline):
    """Inline admin for medical records within patient"""
    model = MedicalRecord
    extra = 0
    fields = ('date', 'facility_name', 'doctor_name', 'diagnosis')
    readonly_fields = ('id', 'date')
    show_change_link = True


class AppointmentInline(admin.TabularInline):
    """Inline admin for appointments within patient"""
    model = Appointment
    extra = 0
    fields = ('date', 'time', 'doctor_name', 'appointment_type', 'status')
    readonly_fields = ('id',)
    show_change_link = True


@admin.register(Patient)
class PatientAdmin(admin.ModelAdmin):
    """Admin configuration for Patient model"""
    list_display = (
        'zimhealth_id', 'full_name', 'national_id', 'phone_number',
        'age', 'gender', 'blood_type', 'registration_date', 'is_active'
    )
    list_filter = (
        'gender', 'blood_type', 'is_active', 'registration_date'
    )
    search_fields = (
        'zimhealth_id', 'first_name', 'last_name', 'national_id', 'phone_number'
    )
    readonly_fields = (
        'zimhealth_id', 'registration_date', 'created_at', 'updated_at',
        'qr_code_display', 'age'
    )

    fieldsets = (
        ('Basic Information', {
            'fields': ('zimhealth_id', 'first_name', 'last_name', 'national_id')
        }),
        ('Personal Details', {
            'fields': ('date_of_birth', 'age', 'gender', 'blood_type')
        }),
        ('Contact Information', {
            'fields': ('phone_number', 'address', 'emergency_contact')
        }),
        ('Medical Information', {
            'fields': ('allergies',)
        }),
        ('System Information', {
            'fields': ('user', 'is_active', 'registration_date', 'last_visit')
        }),
        ('QR Code', {
            'fields': ('qr_code_display', 'qr_code'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    inlines = [MedicalRecordInline, AppointmentInline]

    def qr_code_display(self, obj):
        if obj.qr_code:
            return format_html(
                '<img src="{}" width="150" height="150" />',
                obj.qr_code.url
            )
        return "No QR Code"
    qr_code_display.short_description = "QR Code Preview"

    def full_name(self, obj):
        return obj.full_name
    full_name.short_description = "Full Name"

    def age(self, obj):
        return f"{obj.age} years"
    age.short_description = "Age"


@admin.register(MedicalRecord)
class MedicalRecordAdmin(admin.ModelAdmin):
    """Admin configuration for MedicalRecord model"""
    list_display = (
        'patient_link', 'date', 'facility_name', 'doctor_name',
        'diagnosis_short', 'prescription_count'
    )
    list_filter = (
        'date', 'facility_name', 'doctor_name'
    )
    search_fields = (
        'patient__zimhealth_id', 'patient__first_name', 'patient__last_name',
        'facility_name', 'doctor_name', 'diagnosis'
    )
    readonly_fields = ('id', 'created_at', 'updated_at', 'bmi', 'blood_pressure')

    fieldsets = (
        ('Patient Information', {
            'fields': ('patient',)
        }),
        ('Visit Details', {
            'fields': ('date', 'facility_name', 'doctor_name')
        }),
        ('Medical Information', {
            'fields': ('diagnosis', 'treatment', 'notes')
        }),
        ('Vital Signs', {
            'fields': (
                ('temperature', 'heart_rate'),
                ('blood_pressure_systolic', 'blood_pressure_diastolic', 'blood_pressure'),
                ('weight', 'height', 'bmi')
            ),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    inlines = [PrescriptionInline]

    def patient_link(self, obj):
        url = reverse('admin:api_patient_change', args=[obj.patient.zimhealth_id])
        return format_html('<a href="{}">{}</a>', url, obj.patient.full_name)
    patient_link.short_description = "Patient"

    def diagnosis_short(self, obj):
        return obj.diagnosis[:50] + "..." if len(obj.diagnosis) > 50 else obj.diagnosis
    diagnosis_short.short_description = "Diagnosis"

    def prescription_count(self, obj):
        count = obj.prescriptions.count()
        return f"{count} prescription{'s' if count != 1 else ''}"
    prescription_count.short_description = "Prescriptions"


@admin.register(Prescription)
class PrescriptionAdmin(admin.ModelAdmin):
    """Admin configuration for Prescription model"""
    list_display = (
        'medication', 'patient_link', 'dosage', 'frequency',
        'status', 'start_date', 'is_active_status'
    )
    list_filter = (
        'status', 'frequency', 'start_date', 'medical_record__facility_name'
    )
    search_fields = (
        'medication', 'medical_record__patient__zimhealth_id',
        'medical_record__patient__first_name', 'medical_record__patient__last_name'
    )
    readonly_fields = ('id', 'created_at', 'updated_at', 'patient_info', 'is_active_status')

    fieldsets = (
        ('Medical Record', {
            'fields': ('medical_record', 'patient_info')
        }),
        ('Medication Details', {
            'fields': ('medication', 'dosage', 'frequency', 'duration', 'instructions')
        }),
        ('Prescription Information', {
            'fields': ('quantity_prescribed', 'refills_allowed', 'status')
        }),
        ('Dates', {
            'fields': ('start_date', 'end_date')
        }),
        ('System Information', {
            'fields': ('prescribed_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def patient_link(self, obj):
        patient = obj.medical_record.patient
        url = reverse('admin:api_patient_change', args=[patient.zimhealth_id])
        return format_html('<a href="{}">{}</a>', url, patient.full_name)
    patient_link.short_description = "Patient"

    def patient_info(self, obj):
        patient = obj.medical_record.patient
        return f"{patient.full_name} ({patient.zimhealth_id})"
    patient_info.short_description = "Patient Information"

    def is_active_status(self, obj):
        if obj.is_active:
            return format_html('<span style="color: green;">✓ Active</span>')
        else:
            return format_html('<span style="color: red;">✗ Inactive</span>')
    is_active_status.short_description = "Active Status"


@admin.register(Appointment)
class AppointmentAdmin(admin.ModelAdmin):
    """Admin configuration for Appointment model"""
    list_display = (
        'patient_link', 'date', 'time', 'doctor_name',
        'appointment_type', 'status', 'priority', 'is_today_status'
    )
    list_filter = (
        'status', 'appointment_type', 'priority', 'date', 'facility_name'
    )
    search_fields = (
        'patient__zimhealth_id', 'patient__first_name', 'patient__last_name',
        'doctor_name', 'facility_name', 'reason'
    )
    readonly_fields = (
        'id', 'created_at', 'updated_at', 'is_upcoming', 'is_today_status',
        'is_overdue', 'datetime'
    )

    fieldsets = (
        ('Patient Information', {
            'fields': ('patient',)
        }),
        ('Appointment Details', {
            'fields': (
                ('date', 'time', 'datetime'),
                ('doctor_name', 'facility_name'),
                ('appointment_type', 'priority'),
                'department'
            )
        }),
        ('Status & Information', {
            'fields': (
                'status', 'reason', 'notes',
                ('estimated_duration', 'reminder_sent')
            )
        }),
        ('Status Indicators', {
            'fields': ('is_upcoming', 'is_today_status', 'is_overdue'),
            'classes': ('collapse',)
        }),
        ('Cancellation Information', {
            'fields': ('cancelled_at', 'cancelled_by', 'cancellation_reason'),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_completed', 'cancel_appointments']

    def patient_link(self, obj):
        url = reverse('admin:api_patient_change', args=[obj.patient.zimhealth_id])
        return format_html('<a href="{}">{}</a>', url, obj.patient.full_name)
    patient_link.short_description = "Patient"

    def is_today_status(self, obj):
        if obj.is_today:
            return format_html('<span style="color: orange;">📅 Today</span>')
        elif obj.is_upcoming:
            return format_html('<span style="color: green;">⏰ Upcoming</span>')
        elif obj.is_overdue and obj.status == 'scheduled':
            return format_html('<span style="color: red;">⚠️ Overdue</span>')
        return "—"
    is_today_status.short_description = "Status"

    def mark_completed(self, request, queryset):
        updated = queryset.filter(status='scheduled').update(status='completed')
        self.message_user(request, f'{updated} appointments marked as completed.')
    mark_completed.short_description = "Mark selected appointments as completed"

    def cancel_appointments(self, request, queryset):
        updated = queryset.filter(status='scheduled').update(
            status='cancelled',
            cancelled_at=timezone.now()
        )
        self.message_user(request, f'{updated} appointments cancelled.')
    cancel_appointments.short_description = "Cancel selected appointments"


# Customize admin site headers
admin.site.site_header = "ZimHealth-ID Administration"
admin.site.site_title = "ZimHealth-ID Admin"
admin.site.index_title = "Welcome to ZimHealth-ID Administration"
