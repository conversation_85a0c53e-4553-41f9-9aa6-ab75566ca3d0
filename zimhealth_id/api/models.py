from django.db import models
from django.contrib.auth.models import User
from django.core.validators import RegexValidator
from django.utils import timezone
import uuid
import qrcode
from io import BytesIO
from django.core.files import File
from PIL import Image


class Patient(models.Model):
    """Patient model representing a patient in the ZimHealth-ID system"""

    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
        ('O', 'Other'),
    ]

    BLOOD_TYPE_CHOICES = [
        ('A+', 'A+'),
        ('A-', 'A-'),
        ('B+', 'B+'),
        ('B-', 'B-'),
        ('AB+', 'AB+'),
        ('AB-', 'AB-'),
        ('O+', 'O+'),
        ('O-', 'O-'),
    ]

    # Primary identifier
    zimhealth_id = models.CharField(
        max_length=20,
        unique=True,
        primary_key=True,
        help_text="Unique ZimHealth-ID identifier"
    )

    # Personal Information
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    national_id = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        unique=True,
        validators=[RegexValidator(
            regex=r'^\d{2}-\d{6,7}[A-Z]\d{2}$',
            message='Enter a valid Zimbabwean National ID (e.g., 63-123456A12)'
        )],
        help_text="Zimbabwean National ID (optional)"
    )
    date_of_birth = models.DateField()
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES)

    # Contact Information
    phone_number = models.CharField(
        max_length=15,
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
        )]
    )
    address = models.TextField()
    emergency_contact = models.CharField(
        max_length=15,
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message="Emergency contact must be entered in the format: '+999999999'. Up to 15 digits allowed."
        )]
    )

    # Medical Information
    blood_type = models.CharField(
        max_length=3,
        choices=BLOOD_TYPE_CHOICES,
        blank=True,
        null=True
    )
    allergies = models.JSONField(
        default=list,
        blank=True,
        help_text="List of patient allergies"
    )

    # System Information
    registration_date = models.DateTimeField(auto_now_add=True)
    last_visit = models.DateTimeField(blank=True, null=True)
    qr_code = models.ImageField(
        upload_to='qr_codes/',
        blank=True,
        null=True,
        help_text="QR code for quick patient identification"
    )

    # Link to User account (optional - for patients who have user accounts)
    user = models.OneToOneField(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='patient_profile'
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = "Patient"
        verbose_name_plural = "Patients"
        ordering = ['-registration_date']
        indexes = [
            models.Index(fields=['zimhealth_id']),
            models.Index(fields=['national_id']),
            models.Index(fields=['phone_number']),
            models.Index(fields=['last_name', 'first_name']),
        ]

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.zimhealth_id})"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def age(self):
        """Calculate patient's age"""
        from datetime import date
        today = date.today()
        return today.year - self.date_of_birth.year - (
            (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
        )

    def save(self, *args, **kwargs):
        # Generate ZimHealth ID if not provided
        if not self.zimhealth_id:
            self.zimhealth_id = self.generate_zimhealth_id()

        # Generate QR code if not exists
        if not self.qr_code:
            self.generate_qr_code()

        super().save(*args, **kwargs)

    def generate_zimhealth_id(self):
        """Generate a unique ZimHealth ID"""
        import random
        import string

        while True:
            # Format: ZH-YYYY-XXXXXX (ZH + year + 6 random digits)
            year = timezone.now().year
            random_part = ''.join(random.choices(string.digits, k=6))
            zimhealth_id = f"ZH-{year}-{random_part}"

            if not Patient.objects.filter(zimhealth_id=zimhealth_id).exists():
                return zimhealth_id

    def generate_qr_code(self):
        """Generate QR code for the patient"""
        qr_data = {
            'zimhealth_id': self.zimhealth_id,
            'name': self.full_name,
            'dob': str(self.date_of_birth),
            'blood_type': self.blood_type or 'Unknown',
            'emergency_contact': self.emergency_contact
        }

        qr_string = f"ZimHealth-ID: {self.zimhealth_id}\nName: {self.full_name}\nDOB: {self.date_of_birth}\nBlood Type: {self.blood_type or 'Unknown'}\nEmergency: {self.emergency_contact}"

        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(qr_string)
        qr.make(fit=True)

        qr_image = qr.make_image(fill_color="black", back_color="white")

        # Save to BytesIO
        buffer = BytesIO()
        qr_image.save(buffer, format='PNG')
        buffer.seek(0)

        # Save to model
        filename = f'qr_{self.zimhealth_id}.png'
        self.qr_code.save(filename, File(buffer), save=False)

    def update_last_visit(self):
        """Update the last visit timestamp"""
        self.last_visit = timezone.now()
        self.save(update_fields=['last_visit'])


class MedicalRecord(models.Model):
    """Medical record model representing a patient's medical visit/consultation"""

    # Primary key
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text="Unique identifier for the medical record"
    )

    # Foreign key to Patient
    patient = models.ForeignKey(
        Patient,
        on_delete=models.CASCADE,
        related_name='medical_records',
        to_field='zimhealth_id'
    )

    # Visit Information
    date = models.DateTimeField(
        default=timezone.now,
        help_text="Date and time of the medical consultation"
    )
    facility_name = models.CharField(
        max_length=200,
        help_text="Name of the healthcare facility"
    )
    doctor_name = models.CharField(
        max_length=100,
        help_text="Name of the attending doctor"
    )

    # Medical Information
    diagnosis = models.TextField(
        help_text="Primary diagnosis and any secondary diagnoses"
    )
    treatment = models.TextField(
        help_text="Treatment provided or recommended"
    )
    notes = models.TextField(
        blank=True,
        help_text="Additional notes from the consultation"
    )

    # Vital Signs (optional)
    temperature = models.DecimalField(
        max_digits=4,
        decimal_places=1,
        blank=True,
        null=True,
        help_text="Body temperature in Celsius"
    )
    blood_pressure_systolic = models.IntegerField(
        blank=True,
        null=True,
        help_text="Systolic blood pressure (mmHg)"
    )
    blood_pressure_diastolic = models.IntegerField(
        blank=True,
        null=True,
        help_text="Diastolic blood pressure (mmHg)"
    )
    heart_rate = models.IntegerField(
        blank=True,
        null=True,
        help_text="Heart rate (beats per minute)"
    )
    weight = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Patient weight in kg"
    )
    height = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Patient height in cm"
    )

    # System Information
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_medical_records',
        help_text="Healthcare provider who created this record"
    )

    class Meta:
        verbose_name = "Medical Record"
        verbose_name_plural = "Medical Records"
        ordering = ['-date']
        indexes = [
            models.Index(fields=['patient', '-date']),
            models.Index(fields=['facility_name']),
            models.Index(fields=['doctor_name']),
            models.Index(fields=['-date']),
        ]

    def __str__(self):
        return f"Medical Record for {self.patient.full_name} - {self.date.strftime('%Y-%m-%d')}"

    @property
    def blood_pressure(self):
        """Return formatted blood pressure"""
        if self.blood_pressure_systolic and self.blood_pressure_diastolic:
            return f"{self.blood_pressure_systolic}/{self.blood_pressure_diastolic}"
        return None

    @property
    def bmi(self):
        """Calculate BMI if height and weight are available"""
        if self.height and self.weight:
            height_m = float(self.height) / 100  # Convert cm to meters
            return round(float(self.weight) / (height_m ** 2), 2)
        return None

    def save(self, *args, **kwargs):
        # Update patient's last visit when creating a new medical record
        if not self.pk:  # Only for new records
            self.patient.update_last_visit()
        super().save(*args, **kwargs)


class Prescription(models.Model):
    """Prescription model representing medications prescribed in a medical record"""

    FREQUENCY_CHOICES = [
        ('once_daily', 'Once Daily'),
        ('twice_daily', 'Twice Daily'),
        ('three_times_daily', 'Three Times Daily'),
        ('four_times_daily', 'Four Times Daily'),
        ('every_4_hours', 'Every 4 Hours'),
        ('every_6_hours', 'Every 6 Hours'),
        ('every_8_hours', 'Every 8 Hours'),
        ('every_12_hours', 'Every 12 Hours'),
        ('as_needed', 'As Needed'),
        ('before_meals', 'Before Meals'),
        ('after_meals', 'After Meals'),
        ('at_bedtime', 'At Bedtime'),
        ('other', 'Other'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('discontinued', 'Discontinued'),
        ('on_hold', 'On Hold'),
    ]

    # Primary key
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text="Unique identifier for the prescription"
    )

    # Foreign key to MedicalRecord
    medical_record = models.ForeignKey(
        MedicalRecord,
        on_delete=models.CASCADE,
        related_name='prescriptions'
    )

    # Medication Information
    medication = models.CharField(
        max_length=200,
        help_text="Name of the medication"
    )
    dosage = models.CharField(
        max_length=100,
        help_text="Dosage amount (e.g., 500mg, 2 tablets)"
    )
    frequency = models.CharField(
        max_length=20,
        choices=FREQUENCY_CHOICES,
        help_text="How often to take the medication"
    )
    duration = models.CharField(
        max_length=100,
        help_text="Duration of treatment (e.g., 7 days, 2 weeks)"
    )
    instructions = models.TextField(
        blank=True,
        help_text="Special instructions for taking the medication"
    )

    # Additional Information
    quantity_prescribed = models.IntegerField(
        blank=True,
        null=True,
        help_text="Total quantity prescribed"
    )
    refills_allowed = models.IntegerField(
        default=0,
        help_text="Number of refills allowed"
    )
    status = models.CharField(
        max_length=15,
        choices=STATUS_CHOICES,
        default='active'
    )

    # Dates
    start_date = models.DateField(
        default=timezone.now,
        help_text="Date to start taking the medication"
    )
    end_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date to stop taking the medication"
    )

    # System Information
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    prescribed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='prescribed_medications',
        help_text="Healthcare provider who prescribed this medication"
    )

    class Meta:
        verbose_name = "Prescription"
        verbose_name_plural = "Prescriptions"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['medical_record', '-created_at']),
            models.Index(fields=['medication']),
            models.Index(fields=['status']),
            models.Index(fields=['start_date']),
        ]

    def __str__(self):
        return f"{self.medication} - {self.dosage} {self.frequency}"

    @property
    def patient(self):
        """Get the patient this prescription belongs to"""
        return self.medical_record.patient

    @property
    def is_active(self):
        """Check if prescription is currently active"""
        if self.status != 'active':
            return False

        today = timezone.now().date()
        if self.end_date and today > self.end_date:
            return False

        return today >= self.start_date

    def mark_completed(self):
        """Mark prescription as completed"""
        self.status = 'completed'
        self.save(update_fields=['status'])

    def discontinue(self):
        """Discontinue the prescription"""
        self.status = 'discontinued'
        self.end_date = timezone.now().date()
        self.save(update_fields=['status', 'end_date'])


class Appointment(models.Model):
    """Appointment model representing scheduled appointments for patients"""

    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('no_show', 'No Show'),
        ('rescheduled', 'Rescheduled'),
    ]

    APPOINTMENT_TYPE_CHOICES = [
        ('consultation', 'General Consultation'),
        ('follow_up', 'Follow-up'),
        ('check_up', 'Check-up'),
        ('vaccination', 'Vaccination'),
        ('screening', 'Screening'),
        ('emergency', 'Emergency'),
        ('specialist', 'Specialist Consultation'),
        ('dental', 'Dental'),
        ('mental_health', 'Mental Health'),
        ('other', 'Other'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('normal', 'Normal'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    # Primary key
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text="Unique identifier for the appointment"
    )

    # Foreign key to Patient
    patient = models.ForeignKey(
        Patient,
        on_delete=models.CASCADE,
        related_name='appointments',
        to_field='zimhealth_id'
    )

    # Appointment Details
    doctor_name = models.CharField(
        max_length=100,
        help_text="Name of the doctor for the appointment"
    )
    date = models.DateField(
        help_text="Date of the appointment"
    )
    time = models.TimeField(
        help_text="Time of the appointment"
    )
    appointment_type = models.CharField(
        max_length=20,
        choices=APPOINTMENT_TYPE_CHOICES,
        default='consultation'
    )
    status = models.CharField(
        max_length=15,
        choices=STATUS_CHOICES,
        default='scheduled'
    )
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='normal'
    )

    # Additional Information
    facility_name = models.CharField(
        max_length=200,
        help_text="Name of the healthcare facility"
    )
    department = models.CharField(
        max_length=100,
        blank=True,
        help_text="Department or specialty"
    )
    notes = models.TextField(
        blank=True,
        help_text="Additional notes about the appointment"
    )
    reason = models.TextField(
        help_text="Reason for the appointment"
    )

    # Duration and Reminders
    estimated_duration = models.IntegerField(
        default=30,
        help_text="Estimated duration in minutes"
    )
    reminder_sent = models.BooleanField(
        default=False,
        help_text="Whether reminder has been sent to patient"
    )

    # System Information
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_appointments',
        help_text="User who created this appointment"
    )

    # Cancellation/Rescheduling Information
    cancelled_at = models.DateTimeField(blank=True, null=True)
    cancelled_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='cancelled_appointments'
    )
    cancellation_reason = models.TextField(blank=True)

    class Meta:
        verbose_name = "Appointment"
        verbose_name_plural = "Appointments"
        ordering = ['date', 'time']
        indexes = [
            models.Index(fields=['patient', 'date']),
            models.Index(fields=['date', 'time']),
            models.Index(fields=['doctor_name']),
            models.Index(fields=['status']),
            models.Index(fields=['facility_name']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['patient', 'date', 'time', 'doctor_name'],
                condition=models.Q(status__in=['scheduled', 'rescheduled']),
                name='unique_active_appointment'
            )
        ]

    def __str__(self):
        return f"Appointment: {self.patient.full_name} with Dr. {self.doctor_name} on {self.date} at {self.time}"

    @property
    def datetime(self):
        """Combine date and time into a datetime object"""
        from datetime import datetime, time
        return datetime.combine(self.date, self.time)

    @property
    def is_upcoming(self):
        """Check if appointment is in the future"""
        return self.datetime > timezone.now() and self.status == 'scheduled'

    @property
    def is_today(self):
        """Check if appointment is today"""
        return self.date == timezone.now().date()

    @property
    def is_overdue(self):
        """Check if appointment is overdue"""
        return self.datetime < timezone.now() and self.status == 'scheduled'

    def cancel(self, cancelled_by=None, reason=""):
        """Cancel the appointment"""
        self.status = 'cancelled'
        self.cancelled_at = timezone.now()
        self.cancelled_by = cancelled_by
        self.cancellation_reason = reason
        self.save(update_fields=['status', 'cancelled_at', 'cancelled_by', 'cancellation_reason'])

    def complete(self):
        """Mark appointment as completed"""
        self.status = 'completed'
        self.save(update_fields=['status'])

    def reschedule(self, new_date, new_time):
        """Reschedule the appointment"""
        self.date = new_date
        self.time = new_time
        self.status = 'scheduled'
        self.save(update_fields=['date', 'time', 'status'])

    def send_reminder(self):
        """Mark that reminder has been sent"""
        self.reminder_sent = True
        self.save(update_fields=['reminder_sent'])
