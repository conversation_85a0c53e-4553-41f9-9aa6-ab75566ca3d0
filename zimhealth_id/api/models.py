from django.db import models
from django.contrib.auth.models import User
from django.core.validators import RegexValidator
from django.utils import timezone
import uuid
import qrcode
from io import BytesIO
from django.core.files import File
from PIL import Image


class Patient(models.Model):
    """Patient model representing a patient in the ZimHealth-ID system"""

    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
        ('O', 'Other'),
    ]

    BLOOD_TYPE_CHOICES = [
        ('A+', 'A+'),
        ('A-', 'A-'),
        ('B+', 'B+'),
        ('B-', 'B-'),
        ('AB+', 'AB+'),
        ('AB-', 'AB-'),
        ('O+', 'O+'),
        ('O-', 'O-'),
    ]

    # Primary identifier
    zimhealth_id = models.CharField(
        max_length=20,
        unique=True,
        primary_key=True,
        help_text="Unique ZimHealth-ID identifier"
    )

    # Personal Information
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    national_id = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        unique=True,
        validators=[RegexValidator(
            regex=r'^\d{2}-\d{6,7}[A-Z]\d{2}$',
            message='Enter a valid Zimbabwean National ID (e.g., 63-123456A12)'
        )],
        help_text="Zimbabwean National ID (optional)"
    )
    date_of_birth = models.DateField()
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES)

    # Contact Information
    phone_number = models.CharField(
        max_length=15,
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
        )]
    )
    address = models.TextField()
    emergency_contact = models.CharField(
        max_length=15,
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message="Emergency contact must be entered in the format: '+999999999'. Up to 15 digits allowed."
        )]
    )

    # Medical Information
    blood_type = models.CharField(
        max_length=3,
        choices=BLOOD_TYPE_CHOICES,
        blank=True,
        null=True
    )
    allergies = models.JSONField(
        default=list,
        blank=True,
        help_text="List of patient allergies"
    )

    # System Information
    registration_date = models.DateTimeField(auto_now_add=True)
    last_visit = models.DateTimeField(blank=True, null=True)
    qr_code = models.ImageField(
        upload_to='qr_codes/',
        blank=True,
        null=True,
        help_text="QR code for quick patient identification"
    )

    # Link to User account (optional - for patients who have user accounts)
    user = models.OneToOneField(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='patient_profile'
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = "Patient"
        verbose_name_plural = "Patients"
        ordering = ['-registration_date']
        indexes = [
            models.Index(fields=['zimhealth_id']),
            models.Index(fields=['national_id']),
            models.Index(fields=['phone_number']),
            models.Index(fields=['last_name', 'first_name']),
        ]

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.zimhealth_id})"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def age(self):
        """Calculate patient's age"""
        from datetime import date
        today = date.today()
        return today.year - self.date_of_birth.year - (
            (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
        )

    def save(self, *args, **kwargs):
        # Generate ZimHealth ID if not provided
        if not self.zimhealth_id:
            self.zimhealth_id = self.generate_zimhealth_id()

        # Generate QR code if not exists
        if not self.qr_code:
            self.generate_qr_code()

        super().save(*args, **kwargs)

    def generate_zimhealth_id(self):
        """Generate a unique ZimHealth ID"""
        import random
        import string

        while True:
            # Format: ZH-YYYY-XXXXXX (ZH + year + 6 random digits)
            year = timezone.now().year
            random_part = ''.join(random.choices(string.digits, k=6))
            zimhealth_id = f"ZH-{year}-{random_part}"

            if not Patient.objects.filter(zimhealth_id=zimhealth_id).exists():
                return zimhealth_id

    def generate_qr_code(self):
        """Generate QR code for the patient"""
        qr_data = {
            'zimhealth_id': self.zimhealth_id,
            'name': self.full_name,
            'dob': str(self.date_of_birth),
            'blood_type': self.blood_type or 'Unknown',
            'emergency_contact': self.emergency_contact
        }

        qr_string = f"ZimHealth-ID: {self.zimhealth_id}\nName: {self.full_name}\nDOB: {self.date_of_birth}\nBlood Type: {self.blood_type or 'Unknown'}\nEmergency: {self.emergency_contact}"

        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(qr_string)
        qr.make(fit=True)

        qr_image = qr.make_image(fill_color="black", back_color="white")

        # Save to BytesIO
        buffer = BytesIO()
        qr_image.save(buffer, format='PNG')
        buffer.seek(0)

        # Save to model
        filename = f'qr_{self.zimhealth_id}.png'
        self.qr_code.save(filename, File(buffer), save=False)

    def update_last_visit(self):
        """Update the last visit timestamp"""
        self.last_visit = timezone.now()
        self.save(update_fields=['last_visit'])
