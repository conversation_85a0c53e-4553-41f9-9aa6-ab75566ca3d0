from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.utils import timezone
from datetime import datetime, timedelta
from .models import Patient, MedicalRecord, Prescription, Appointment


@login_required
def dashboard_index(request):
    """Main dashboard view"""
    # Get statistics
    total_patients = Patient.objects.filter(is_active=True).count()
    todays_appointments = Appointment.objects.filter(
        date=timezone.now().date(),
        status='scheduled'
    ).count()
    pending_appointments = Appointment.objects.filter(
        status='scheduled',
        date__gte=timezone.now().date()
    ).count()
    active_prescriptions = Prescription.objects.filter(status='active').count()

    # Get recent activities (mock data for now)
    recent_activities = [
        {
            'icon': 'user-plus',
            'title': 'New Patient Registered',
            'description': '<PERSON> has been registered in the system',
            'timestamp': timezone.now() - timedelta(minutes=30)
        },
        {
            'icon': 'calendar-check',
            'title': 'Appointment Completed',
            'description': '<PERSON><PERSON> completed consultation with <PERSON>',
            'timestamp': timezone.now() - timedelta(hours=2)
        },
        {
            'icon': 'pills',
            'title': 'Prescription Added',
            'description': 'New prescription added for patient ZH-2024-001234',
            'timestamp': timezone.now() - timedelta(hours=4)
        }
    ]

    # Get upcoming appointments
    upcoming_appointments = Appointment.objects.filter(
        status='scheduled',
        date__gte=timezone.now().date()
    ).order_by('date', 'time')[:5]

    context = {
        'total_patients': total_patients,
        'todays_appointments': todays_appointments,
        'pending_appointments': pending_appointments,
        'active_prescriptions': active_prescriptions,
        'recent_activities': recent_activities,
        'upcoming_appointments': upcoming_appointments,
    }

    return render(request, 'dashboard/index.html', context)


@login_required
def analytics_dashboard(request):
    """Analytics dashboard view"""
    # Get analytics data
    total_patients = Patient.objects.filter(is_active=True).count()
    monthly_visits = MedicalRecord.objects.filter(
        date__gte=timezone.now() - timedelta(days=30)
    ).count()
    active_prescriptions = Prescription.objects.filter(status='active').count()

    # Get top diagnoses
    top_diagnoses = ['Hypertension', 'Diabetes', 'Malaria', 'Upper Respiratory Infection', 'Gastritis']

    # Get top facilities
    top_facilities = ['Harare Central Hospital', 'Parirenyatwa Hospital', 'Chitungwiza Hospital', 'Mpilo Hospital']

    context = {
        'total_patients': total_patients,
        'monthly_visits': monthly_visits,
        'active_prescriptions': active_prescriptions,
        'top_diagnoses': top_diagnoses,
        'top_facilities': top_facilities,
    }

    return render(request, 'dashboard/analytics.html', context)


@login_required
def patients_list(request):
    """List all patients with search and filtering"""
    patients = Patient.objects.filter(is_active=True).order_by('-created_at')

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        patients = patients.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(zimhealth_id__icontains=search_query) |
            Q(national_id__icontains=search_query) |
            Q(phone_number__icontains=search_query)
        )

    # Filtering
    gender_filter = request.GET.get('gender')
    if gender_filter:
        patients = patients.filter(gender=gender_filter)

    blood_type_filter = request.GET.get('blood_type')
    if blood_type_filter:
        patients = patients.filter(blood_type=blood_type_filter)

    # Pagination
    paginator = Paginator(patients, 20)  # Show 20 patients per page
    page_number = request.GET.get('page')
    patients = paginator.get_page(page_number)

    context = {
        'patients': patients,
        'search_query': search_query,
        'gender_filter': gender_filter,
        'blood_type_filter': blood_type_filter,
    }

    return render(request, 'api/patients.html', context)


@login_required
def patient_detail(request, zimhealth_id):
    """Patient detail view"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    # Get recent medical records
    recent_records = patient.medical_records.order_by('-date')[:5]

    # Get upcoming appointments
    upcoming_appointments = patient.appointments.filter(
        status='scheduled',
        date__gte=timezone.now().date()
    ).order_by('date', 'time')[:5]

    # Get active prescriptions count
    active_prescriptions_count = Prescription.objects.filter(
        medical_record__patient=patient,
        status='active'
    ).count()

    context = {
        'patient': patient,
        'recent_records': recent_records,
        'upcoming_appointments': upcoming_appointments,
        'active_prescriptions_count': active_prescriptions_count,
    }

    return render(request, 'api/patient_detail.html', context)


@login_required
def medical_records_list(request):
    """List all medical records"""
    medical_records = MedicalRecord.objects.select_related('patient').order_by('-date')

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        medical_records = medical_records.filter(
            Q(patient__first_name__icontains=search_query) |
            Q(patient__last_name__icontains=search_query) |
            Q(diagnosis__icontains=search_query) |
            Q(doctor_name__icontains=search_query) |
            Q(facility_name__icontains=search_query)
        )

    # Date filtering
    from_date = request.GET.get('from_date')
    if from_date:
        medical_records = medical_records.filter(date__gte=from_date)

    to_date = request.GET.get('to_date')
    if to_date:
        medical_records = medical_records.filter(date__lte=to_date)

    # Facility filtering
    facility_filter = request.GET.get('facility')
    if facility_filter:
        medical_records = medical_records.filter(facility_name=facility_filter)

    # Pagination
    paginator = Paginator(medical_records, 10)  # Show 10 records per page
    page_number = request.GET.get('page')
    medical_records = paginator.get_page(page_number)

    context = {
        'medical_records': medical_records,
        'search_query': search_query,
        'from_date': from_date,
        'to_date': to_date,
        'facility_filter': facility_filter,
    }

    return render(request, 'api/medical_records.html', context)


@login_required
def appointments_list(request):
    """List all appointments"""
    appointments = Appointment.objects.select_related('patient').order_by('-date', '-time')

    # Get statistics
    todays_appointments = appointments.filter(date=timezone.now().date()).count()
    pending_appointments = appointments.filter(status='scheduled').count()
    completed_appointments = appointments.filter(status='completed').count()
    cancelled_appointments = appointments.filter(status='cancelled').count()

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        appointments = appointments.filter(
            Q(patient__first_name__icontains=search_query) |
            Q(patient__last_name__icontains=search_query) |
            Q(doctor_name__icontains=search_query) |
            Q(facility_name__icontains=search_query)
        )

    # Status filtering
    status_filter = request.GET.get('status')
    if status_filter:
        appointments = appointments.filter(status=status_filter)

    # Type filtering
    type_filter = request.GET.get('type')
    if type_filter:
        appointments = appointments.filter(appointment_type=type_filter)

    # Date filtering
    date_filter = request.GET.get('date')
    if date_filter:
        appointments = appointments.filter(date=date_filter)

    # Pagination
    paginator = Paginator(appointments, 15)  # Show 15 appointments per page
    page_number = request.GET.get('page')
    appointments = paginator.get_page(page_number)

    context = {
        'appointments': appointments,
        'todays_appointments': todays_appointments,
        'pending_appointments': pending_appointments,
        'completed_appointments': completed_appointments,
        'cancelled_appointments': cancelled_appointments,
        'search_query': search_query,
        'status_filter': status_filter,
        'type_filter': type_filter,
        'date_filter': date_filter,
    }

    return render(request, 'api/appointments.html', context)


@login_required
def prescriptions_list(request):
    """List all prescriptions"""
    prescriptions = Prescription.objects.select_related(
        'medical_record__patient'
    ).order_by('-created_at')

    # Get statistics
    active_prescriptions = prescriptions.filter(status='active').count()
    expiring_prescriptions = prescriptions.filter(
        status='active',
        end_date__lte=timezone.now().date() + timedelta(days=7)
    ).count()
    completed_prescriptions = prescriptions.filter(status='completed').count()
    discontinued_prescriptions = prescriptions.filter(status='discontinued').count()

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        prescriptions = prescriptions.filter(
            Q(medication__icontains=search_query) |
            Q(medical_record__patient__first_name__icontains=search_query) |
            Q(medical_record__patient__last_name__icontains=search_query) |
            Q(medical_record__doctor_name__icontains=search_query)
        )

    # Status filtering
    status_filter = request.GET.get('status')
    if status_filter:
        prescriptions = prescriptions.filter(status=status_filter)

    # Frequency filtering
    frequency_filter = request.GET.get('frequency')
    if frequency_filter:
        prescriptions = prescriptions.filter(frequency=frequency_filter)

    # Date filtering
    start_date = request.GET.get('start_date')
    if start_date:
        prescriptions = prescriptions.filter(start_date__gte=start_date)

    # Pagination
    paginator = Paginator(prescriptions, 10)  # Show 10 prescriptions per page
    page_number = request.GET.get('page')
    prescriptions = paginator.get_page(page_number)

    context = {
        'prescriptions': prescriptions,
        'active_prescriptions': active_prescriptions,
        'expiring_prescriptions': expiring_prescriptions,
        'completed_prescriptions': completed_prescriptions,
        'discontinued_prescriptions': discontinued_prescriptions,
        'search_query': search_query,
        'status_filter': status_filter,
        'frequency_filter': frequency_filter,
        'start_date': start_date,
    }

    return render(request, 'api/prescriptions.html', context)
