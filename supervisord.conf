[supervisord]
nodaemon=true
user=root
logfile=/tmp/supervisord.log
pidfile=/tmp/supervisord.pid

[program:redis]
command=redis-server --daemonize no --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
user=redis
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autorestart=true
priority=100

[program:django]
command=python manage.py runserver 0.0.0.0:8000
directory=/app
user=thetha
environment=DJANGO_SETTINGS_MODULE=simple_settings,REDIS_URL=redis://localhost:6379/0
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autorestart=true
priority=200

[program:celery_worker]
command=celery -A thetha worker -l info --concurrency=2 --prefetch-multiplier=1 --max-tasks-per-child=1000
directory=/app
user=thetha
environment=DJANGO_SETTINGS_MODULE=simple_settings,REDIS_URL=redis://localhost:6379/0,OMP_NUM_THREADS=2,MKL_NUM_THREADS=2,TORCH_NUM_THREADS=2
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autorestart=true
priority=300

[program:celery_beat]
command=celery -A thetha beat -l info --schedule=/app/celerybeat-schedule.db
directory=/app
user=thetha
environment=DJANGO_SETTINGS_MODULE=simple_settings,REDIS_URL=redis://localhost:6379/0
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autorestart=true
priority=400
