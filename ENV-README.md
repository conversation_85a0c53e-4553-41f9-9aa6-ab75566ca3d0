# Thetha Unified Environment Configuration 🔧

This document explains the unified environment configuration system for the Thetha audio transcription application.

## 📁 File Structure

```
Thetha/
├── .env                                    # 🔑 MAIN unified environment file
├── .env.example                           # 📄 Template for new deployments
├── ENV-README.md                          # 📚 This documentation
├── validate-env.sh                       # ✅ Environment validation script
├── Frontend/thetha/
│   ├── .env.docker                       # 🐳 Frontend Docker-specific variables
│   ├── .env.development                  # 🔨 Frontend development variables
│   └── .env.production                   # 🚀 Frontend production variables
└── Backend/
    └── .env.deprecated                   # ❌ Old backend .env (no longer used)
```

## 🎯 Overview

The Thetha system now uses a **single unified `.env` file** that contains all configuration for:

- 🗄️ **Database** (PostgreSQL)
- 🐍 **Backend** (Django + Celery + Redis)
- ⚛️ **Frontend** (React/Vite)
- 📧 **Email** (SMTP)
- 🎵 **Audio Processing** (Whisper)
- 📊 **Monitoring** (Grafana/Prometheus)

## 🚀 Quick Start

### 1. Validate Configuration
```bash
# Run the validation script
./validate-env.sh
```

### 2. Start Services
```bash
# Start all services with unified configuration
docker-compose up --build
```

## 📋 Configuration Sections

### 🗄️ Database Configuration
```env
POSTGRES_DB=thetha_db
POSTGRES_USER=thetha_user
POSTGRES_PASSWORD=thetha_secure_password
POSTGRES_HOST=postgres
POSTGRES_PORT=5433
```

### 🐍 Django Backend Configuration
```env
DJANGO_SECRET_KEY="your-secret-key"
DJANGO_DEBUG=True
DJANGO_ALLOWED_HOSTS=localhost,127.0.0.1,thetha
DJANGO_SUPERUSER_USERNAME=admin
DJANGO_SUPERUSER_EMAIL=<EMAIL>
DJANGO_SUPERUSER_PASSWORD=admin
```

### ⚛️ Frontend Configuration
```env
VITE_API_URL=http://localhost:8000
VITE_APP_NAME=Thetha
FRONTEND_PORT=3000
BACKEND_PORT=8000
```

### 📧 Email Configuration
```env
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD="your-app-password"
```

### 🎵 Audio Processing Configuration
```env
WHISPER_MODEL=base
MAX_AUDIO_DURATION=3600
MAX_SEGMENT_DURATION=30
AUDIO_PROCESSING_MAX_WORKERS=2
```

### 🔄 Redis & Celery Configuration
```env
REDIS_HOST=localhost
REDIS_PORT=6379
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
```

## 🌍 Environment-Specific Settings

### 🔨 Development
- `DJANGO_DEBUG=True`
- `VITE_ENABLE_DEBUG=true`
- `VITE_API_URL=http://localhost:8000`

### 🐳 Docker
- Uses container networking (`thetha:8000`)
- Nginx proxy for frontend API calls
- Internal Redis/PostgreSQL communication

### 🚀 Production
- `DJANGO_DEBUG=False`
- `VITE_ENABLE_ANALYTICS=true`
- `VITE_ENABLE_SENTRY=true`
- Replace URLs with production domains

## 🔧 How It Works

### Backend Services
The Docker Compose backend service (`thetha`) reads environment variables directly from the unified `.env` file through the `docker-compose.yml` configuration.

### Frontend Services
The frontend uses environment-specific `.env` files that contain only the necessary Vite variables:

- **Development**: `.env.development` (localhost URLs)
- **Docker**: `.env.docker` (relative URLs with nginx proxy)
- **Production**: `.env.production` (production URLs)

### Variable Inheritance
```
.env (unified) → docker-compose.yml → containers
              → .env.docker → frontend build
              → .env.development → local development
              → .env.production → production build
```

## 📝 Configuration Management

### Adding New Variables
1. Add to the main `.env` file
2. Update `docker-compose.yml` if needed for container environment
3. Add to frontend `.env.*` files if it's a `VITE_*` variable
4. Update `validate-env.sh` for validation
5. Document in this README

### Environment-Specific Overrides
```env
# Development overrides
DEV_DJANGO_DEBUG=True
DEV_VITE_ENABLE_DEBUG=true

# Production overrides
PROD_DJANGO_DEBUG=False
PROD_VITE_ENABLE_ANALYTICS=true

# Docker overrides
DOCKER_VITE_API_URL=
DOCKER_VITE_API_BASE_URL=/api
```

## 🔒 Security Best Practices

### For Development
- ✅ Use provided defaults
- ✅ Keep `DJANGO_DEBUG=True`
- ✅ Use test email credentials

### For Production
- 🔒 Change `DJANGO_SECRET_KEY`
- 🔒 Set `DJANGO_DEBUG=False`
- 🔒 Update `DJANGO_ALLOWED_HOSTS`
- 🔒 Use strong database passwords
- 🔒 Use app-specific email passwords
- 🔒 Set up SSL/TLS certificates
- 🔒 Configure proper CORS settings

## 🛠️ Troubleshooting

### Common Issues

1. **Syntax Errors in .env**
   ```bash
   # Fix: Quote values with special characters
   DJANGO_SECRET_KEY="django-insecure-2t!*@(86xb%a#ex5_s2k*+"
   EMAIL_HOST_PASSWORD="$4067*#m3@/X"
   ```

2. **Frontend Can't Connect to API**
   ```bash
   # Check API URL configuration
   # Development: VITE_API_URL=http://localhost:8000
   # Docker: Uses nginx proxy with relative URLs
   ```

3. **Database Connection Issues**
   ```bash
   # Verify database variables match between .env and docker-compose.yml
   docker-compose logs postgres
   ```

### Validation
```bash
# Always validate after changes
./validate-env.sh

# Check specific service logs
docker-compose logs thetha
docker-compose logs frontend
docker-compose logs postgres
```

## 📊 Monitoring

Access monitoring tools:
- **Backend Health**: http://localhost:8000/health/
- **Frontend**: http://localhost:3000
- **Grafana**: http://localhost:3000 (if monitoring enabled)
- **Prometheus**: http://localhost:9090 (if monitoring enabled)

## 🔄 Migration Guide

### From Old Setup
If you're migrating from the old multi-file setup:

1. **Backup current configs**:
   ```bash
   cp .env .env.backup
   cp Backend/.env Backend/.env.backup
   ```

2. **Use unified configuration**:
   ```bash
   # The system now uses the main .env file
   # Old Backend/.env is deprecated
   ```

3. **Validate new setup**:
   ```bash
   ./validate-env.sh
   docker-compose up --build
   ```

## 📚 Additional Resources

- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [Vite Environment Variables](https://vitejs.dev/guide/env-and-mode.html)
- [Django Settings](https://docs.djangoproject.com/en/stable/ref/settings/)
- [Redis Configuration](https://redis.io/documentation)

---

✨ **The unified environment system ensures consistent configuration across all services while maintaining flexibility for different deployment scenarios.**
